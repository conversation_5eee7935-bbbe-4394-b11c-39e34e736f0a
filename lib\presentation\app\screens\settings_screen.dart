import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/managers/theme_manager.dart';
import '../../../core/preferences/preferences_manager.dart';
import '../widgets/settings_widgets.dart';

class SettingsScreen extends StatelessWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Kirat Keyboard Settings'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: ListView(
        children: [
          _buildSetupSection(context),
          _buildKeyboardSection(context),
          _buildThemeSection(context),
          _buildGesturesSection(context),
          _buildInputFeedbackSection(context),
          _buildClipboardSection(context),
          _buildAdvancedSection(context),
          _buildAboutSection(context),
        ],
      ),
    );
  }

  Widget _buildSetupSection(BuildContext context) {
    return SettingsSection(
      title: 'Setup',
      children: [
        SettingsTile.navigation(
          leading: const Icon(Icons.keyboard),
          title: const Text('Enable Kirat Keyboard'),
          description: const Text('Set Kirat Keyboard as your default keyboard'),
          onPressed: (context) => _openKeyboardSettings(),
        ),
        SettingsTile.navigation(
          leading: const Icon(Icons.security),
          title: const Text('Permissions'),
          description: const Text('Grant necessary permissions'),
          onPressed: (context) => _openPermissionsScreen(context),
        ),
      ],
    );
  }

  Widget _buildKeyboardSection(BuildContext context) {
    return SettingsSection(
      title: 'Keyboard',
      children: [
        BlocBuilder<ThemeManager, ThemeState>(
          builder: (context, state) {
            return SwitchSettingsTile(
              title: 'Number Row',
              description: 'Show number row above keyboard',
              value: PreferencesManager.instance.keyboard.numberRow,
              onChanged: (value) {
                PreferencesManager.instance.keyboard.numberRow = value;
              },
            );
          },
        ),
        BlocBuilder<ThemeManager, ThemeState>(
          builder: (context, state) {
            return SwitchSettingsTile(
              title: 'Key Popups',
              description: 'Show popup when key is pressed',
              value: PreferencesManager.instance.keyboard.popupEnabled,
              onChanged: (value) {
                PreferencesManager.instance.keyboard.popupEnabled = value;
              },
            );
          },
        ),
        SliderSettingsTile(
          title: 'Long Press Delay',
          description: 'Time before long press activates',
          value: PreferencesManager.instance.keyboard.longPressDelay.toDouble(),
          min: 100,
          max: 1000,
          divisions: 18,
          onChanged: (value) {
            PreferencesManager.instance.keyboard.longPressDelay = value.round();
          },
        ),
        SliderSettingsTile(
          title: 'Font Size',
          description: 'Keyboard font size multiplier',
          value: PreferencesManager.instance.keyboard.fontSizeMultiplier,
          min: 0.5,
          max: 2.0,
          divisions: 15,
          onChanged: (value) {
            PreferencesManager.instance.keyboard.fontSizeMultiplier = value;
          },
        ),
      ],
    );
  }

  Widget _buildThemeSection(BuildContext context) {
    return SettingsSection(
      title: 'Appearance',
      children: [
        BlocBuilder<ThemeManager, ThemeState>(
          builder: (context, themeState) {
            return ListSettingsTile<String>(
              title: 'Day Theme',
              description: 'Theme for light mode',
              value: PreferencesManager.instance.theme.dayThemeId,
              values: themeState.availableThemes
                  .where((theme) => !theme.isNightTheme)
                  .map((theme) => theme.id)
                  .toList(),
              valueLabels: Map.fromEntries(
                themeState.availableThemes
                    .where((theme) => !theme.isNightTheme)
                    .map((theme) => MapEntry(theme.id, theme.name)),
              ),
              onChanged: (value) {
                PreferencesManager.instance.theme.dayThemeId = value;
                context.read<ThemeManager>().switchTheme(value);
              },
            );
          },
        ),
        BlocBuilder<ThemeManager, ThemeState>(
          builder: (context, themeState) {
            return ListSettingsTile<String>(
              title: 'Night Theme',
              description: 'Theme for dark mode',
              value: PreferencesManager.instance.theme.nightThemeId,
              values: themeState.availableThemes
                  .where((theme) => theme.isNightTheme)
                  .map((theme) => theme.id)
                  .toList(),
              valueLabels: Map.fromEntries(
                themeState.availableThemes
                    .where((theme) => theme.isNightTheme)
                    .map((theme) => MapEntry(theme.id, theme.name)),
              ),
              onChanged: (value) {
                PreferencesManager.instance.theme.nightThemeId = value;
                context.read<ThemeManager>().switchTheme(value);
              },
            );
          },
        ),
      ],
    );
  }

  Widget _buildGesturesSection(BuildContext context) {
    return SettingsSection(
      title: 'Input',
      children: [
        SliderSettingsTile(
          title: 'Swipe Distance Threshold',
          description: 'Minimum distance for swipe gestures',
          value: PreferencesManager.instance.gestures.swipeDistanceThreshold,
          min: 20.0,
          max: 100.0,
          divisions: 8,
          onChanged: (value) {
            PreferencesManager.instance.gestures.swipeDistanceThreshold = value;
          },
        ),
        SliderSettingsTile(
          title: 'Swipe Velocity Threshold',
          description: 'Minimum velocity for swipe gestures',
          value: PreferencesManager.instance.gestures.swipeVelocityThreshold,
          min: 100.0,
          max: 1000.0,
          divisions: 9,
          onChanged: (value) {
            PreferencesManager.instance.gestures.swipeVelocityThreshold = value;
          },
        ),
      ],
    );
  }

  Widget _buildInputFeedbackSection(BuildContext context) {
    return SettingsSection(
      title: 'Feedback',
      children: [
        SwitchSettingsTile(
          title: 'Haptic Feedback',
          description: 'Vibrate when keys are pressed',
          value: PreferencesManager.instance.inputFeedback.hapticEnabled,
          onChanged: (value) {
            PreferencesManager.instance.inputFeedback.hapticEnabled = value;
          },
        ),
        SliderSettingsTile(
          title: 'Haptic Strength',
          description: 'Intensity of haptic feedback',
          value: PreferencesManager.instance.inputFeedback.hapticStrength,
          min: 0.0,
          max: 2.0,
          divisions: 10,
          onChanged: (value) {
            PreferencesManager.instance.inputFeedback.hapticStrength = value;
          },
        ),
        SwitchSettingsTile(
          title: 'Audio Feedback',
          description: 'Play sounds when keys are pressed',
          value: PreferencesManager.instance.inputFeedback.audioEnabled,
          onChanged: (value) {
            PreferencesManager.instance.inputFeedback.audioEnabled = value;
          },
        ),
        SliderSettingsTile(
          title: 'Audio Volume',
          description: 'Volume of key press sounds',
          value: PreferencesManager.instance.inputFeedback.audioVolume,
          min: 0.0,
          max: 1.0,
          divisions: 10,
          onChanged: (value) {
            PreferencesManager.instance.inputFeedback.audioVolume = value;
          },
        ),
      ],
    );
  }

  Widget _buildClipboardSection(BuildContext context) {
    return SettingsSection(
      title: 'Features',
      children: [
        SwitchSettingsTile(
          title: 'Clipboard History',
          description: 'Save clipboard items for quick access',
          value: PreferencesManager.instance.clipboard.historyEnabled,
          onChanged: (value) {
            PreferencesManager.instance.clipboard.historyEnabled = value;
          },
        ),
        SwitchSettingsTile(
          title: 'Sync to System Clipboard',
          description: 'Copy selected items to system clipboard',
          value: PreferencesManager.instance.clipboard.syncToSystem,
          onChanged: (value) {
            PreferencesManager.instance.clipboard.syncToSystem = value;
          },
        ),
        SwitchSettingsTile(
          title: 'Emoji History',
          description: 'Remember recently used emojis',
          value: PreferencesManager.instance.emoji.historyEnabled,
          onChanged: (value) {
            PreferencesManager.instance.emoji.historyEnabled = value;
          },
        ),
      ],
    );
  }

  Widget _buildAdvancedSection(BuildContext context) {
    return SettingsSection(
      title: 'Advanced',
      children: [
        SwitchSettingsTile(
          title: 'Smartbar',
          description: 'Show suggestions and quick actions',
          value: PreferencesManager.instance.smartbar.enabled,
          onChanged: (value) {
            PreferencesManager.instance.smartbar.enabled = value;
          },
        ),
        SwitchSettingsTile(
          title: 'Word Suggestions',
          description: 'Show word completion suggestions',
          value: PreferencesManager.instance.smartbar.suggestionsEnabled,
          onChanged: (value) {
            PreferencesManager.instance.smartbar.suggestionsEnabled = value;
          },
        ),
      ],
    );
  }

  Widget _buildAboutSection(BuildContext context) {
    return SettingsSection(
      title: 'About',
      children: [
        SettingsTile.navigation(
          leading: const Icon(Icons.info),
          title: const Text('About Kirat Keyboard'),
          description: const Text('Version 1.0.0'),
          onPressed: (context) => _showAboutDialog(context),
        ),
        SettingsTile.navigation(
          leading: const Icon(Icons.help),
          title: const Text('Help & Support'),
          description: const Text('User guide and support'),
          onPressed: (context) => _showHelpDialog(context),
        ),
      ],
    );
  }

  void _openKeyboardSettings() {
    // Open Android keyboard settings
    // This would require platform channel implementation
  }

  void _openPermissionsScreen(BuildContext context) {
    // Navigate to permissions screen
  }

  void _showAboutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('About Kirat Keyboard'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Version: 1.0.0'),
            SizedBox(height: 8),
            Text('A customizable keyboard for Android built with Flutter.'),
            SizedBox(height: 8),
            Text('Based on FlorisBoard architecture.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showHelpDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Help & Support'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Getting Started:'),
            Text('1. Enable Kirat Keyboard in Android settings'),
            Text('2. Set as default input method'),
            Text('3. Customize settings to your preference'),
            SizedBox(height: 16),
            Text('For more help, visit our documentation.'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }
}
