class AppConstants {
  static const String appName = 'Kirat Keyboard';
  static const String packageName = 'com.kirat.keyboard';
  static const String version = '1.0.0';

  // Keyboard constants
  static const double keyboardRowBaseHeight = 56.0;
  static const double keyMarginHorizontal = 2.0;
  static const double keyMarginVertical = 2.0;
  static const double smartbarHeight = 40.0;

  // Timing constants
  static const int defaultLongPressDelay = 300;
  static const int defaultRepeatDelay = 50;
  static const int gestureThreshold = 50;

  // Cache constants
  static const int maxLayoutCacheSize = 100;
  static const int maxThemeCacheSize = 50;
  static const Duration cacheExpiry = Duration(minutes: 5);

  // Clipboard constants
  static const int maxClipboardHistorySize = 50;
  static const int clipboardSyncInterval = 2000; // milliseconds

  // Emoji constants
  static const int maxEmojiHistorySize = 24;
  static const int emojiGridColumns = 8;

  // Extension constants
  static const String extensionFileExtension = '.flex';
  static const String extensionManifestFile = 'extension.json';

  // Method channel names
  static const String imeMethodChannel = 'kirat_keyboard/ime';
  static const String settingsMethodChannel = 'kirat_keyboard/settings';

  // Preference keys
  static const String prefKeyboardNumberRow = 'keyboard.numberRow';
  static const String prefKeyboardLongPressDelay = 'keyboard.longPressDelay';
  static const String prefThemeMode = 'theme.mode';
  static const String prefThemeDayId = 'theme.dayThemeId';
  static const String prefThemeNightId = 'theme.nightThemeId';

  // Built-in theme IDs
  static const String builtInDayThemeId = 'kirat_day';
  static const String builtInNightThemeId = 'kirat_night';

  // Default values
  static const bool defaultNumberRowEnabled = false;
  static const bool defaultHapticEnabled = true;
  static const bool defaultAudioEnabled = false;
  static const double defaultFontSizeMultiplier = 1.0;
}

// Enums
enum KeyboardMode {
  characters,
  symbols,
  symbols2,
  numeric,
  numericAdvanced,
  phone,
  media,
  clipboard,
}

enum KeyType {
  character,
  numeric,
  function,
  modifier,
  navigation,
  system,
}

enum InputShiftState {
  unshifted,
  shiftedManual,
  shiftedAutomatic,
  capsLock,
}

enum KeyVariation {
  normal,
  password,
  email,
  uri,
  phone,
  number,
}

enum SwipeDirection {
  up,
  down,
  left,
  right,
}

enum ThemeModeEnum {
  ALWAYS_DAY,
  ALWAYS_NIGHT,
  FOLLOW_SYSTEM,
  FOLLOW_TIME,
}

enum KeyHintMode {
  DISABLED,
  ENABLED_SMART_PRIORITY,
  ENABLED_ACCENT_PRIORITY,
  SMART_PRIORITY,
}

enum UtilityKeyAction {
  DISABLED,
  DYNAMIC_SWITCH_LANGUAGE_EMOJIS,
  SWITCH_TO_EMOJIS,
  SWITCH_LANGUAGE,
  SWITCH_KEYBOARD,
}

enum SpaceBarMode {
  NOTHING,
  CURRENT_LANGUAGE,
  SPACE_BAR_KEY,
}

enum IncognitoDisplayMode {
  NEVER,
  DISPLAY_BEHIND_KEYBOARD,
  DISPLAY_ABOVE_KEYBOARD,
}

enum SwipeAction {
  NO_ACTION,
  HIDE_KEYBOARD,
  SWITCH_TO_CLIPBOARD_CONTEXT,
  SWITCH_TO_PREV_SUBTYPE,
  SWITCH_TO_NEXT_SUBTYPE,
  SWITCH_TO_PREV_KEYBOARD,
  TOGGLE_SMARTBAR_VISIBILITY,
  TOGGLE_ONE_HANDED_MODE,
  DELETE_WORD,
  DELETE_CHARACTERS_PRECISELY,
  SELECT_WORD,
  SELECT_CHARACTERS_PRECISELY,
  SHIFT,
  UNDO,
  REDO,
  SPACE,
  MOVE_CURSOR_LEFT,
  MOVE_CURSOR_RIGHT,
  MOVE_CURSOR_UP,
  MOVE_CURSOR_DOWN,
  SHOW_INPUT_METHOD_PICKER,
}

enum SuggestionType {
  WORD_COMPLETION,
  NEXT_WORD_PREDICTION,
  CORRECTION,
  EMOJI,
  CONTACT,
}

enum ClipboardItemType {
  TEXT,
  IMAGE,
  URL,
  EMAIL,
  PHONE,
}

enum ThemeSelector {
  NONE,
  PRESSED,
  DISABLED,
  FOCUSED,
}
