<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">FlorisBoard</string>
    <string name="key__phone_pause" comment="Label for the Pause key in the telephone keyboard layout">一時停止</string>
    <string name="key__phone_wait" comment="Label for the Wait key in the telephone keyboard layout">お待ちください</string>
    <string name="key_popup__threedots_alt" comment="Content description for the three-dots icon in a key popup">3つのドットのアイコン。 表示されている場合は長押しするとより多くの文字を使用できることを示します。</string>
    <!-- One-handed strings -->
    <string name="one_handed__close_btn_content_description" comment="Content description for the one-handed close button">片手モードを閉じる</string>
    <string name="one_handed__move_start_btn_content_description" comment="Content description for the one-handed move to left button">キーボードを左に移動します。</string>
    <string name="one_handed__move_end_btn_content_description" comment="Content description for the one-handed move to right button">キーボードを右に移動</string>
    <!-- Media strings -->
    <string name="settings__media__title">絵文字</string>
    <string name="media__tab__emojis" comment="Tab description for emojis in the media UI">絵文字</string>
    <string name="media__tab__emoticons" comment="Tab description for emoticons in the media UI">絵文字</string>
    <string name="media__tab__kaomoji" comment="Tab description for kaomoji in the media UI">顔文字</string>
    <string name="prefs__media__emoji_preferred_skin_tone">好みの絵文字の肌の色</string>
    <string name="prefs__media__emoji_preferred_hair_style">好みの絵文字ヘアスタイル</string>
    <!-- Emoji strings -->
    <string name="emoji__category__smileys_emotion" comment="Emoji category name">スマイリー &amp; 感情</string>
    <string name="emoji__category__people_body" comment="Emoji category name">人間 &amp; 体</string>
    <string name="emoji__category__animals_nature" comment="Emoji category name">動物 &amp; 自然</string>
    <string name="emoji__category__food_drink" comment="Emoji category name">フード &amp; ドリンク</string>
    <string name="emoji__category__travel_places" comment="Emoji category name">旅行 &amp; 場所</string>
    <string name="emoji__category__activities" comment="Emoji category name">アクティビティ</string>
    <string name="emoji__category__objects" comment="Emoji category name">オブジェクト</string>
    <string name="emoji__category__symbols" comment="Emoji category name">シンボル</string>
    <string name="emoji__category__flags" comment="Emoji category name">旗</string>
    <!-- Quick action strings -->
    <string name="quick_action__arrow_up" maxLength="12">上矢印</string>
    <string name="quick_action__arrow_up__tooltip">Perform arrow up</string>
    <string name="quick_action__arrow_down" maxLength="12">下矢印</string>
    <string name="quick_action__arrow_down__tooltip">Perform arrow up</string>
    <string name="quick_action__arrow_left" maxLength="12">左矢印</string>
    <string name="quick_action__arrow_left__tooltip"></string>
    <string name="quick_action__arrow_right" maxLength="12">右矢印</string>
    <string name="quick_action__clipboard_copy" maxLength="12">コピー</string>
    <string name="quick_action__clipboard_cut" maxLength="12">カット</string>
    <string name="quick_action__clipboard_paste" maxLength="12">ペースト</string>
    <string name="quick_action__clipboard_select_all" maxLength="12">全て選択</string>
    <string name="quick_action__ime_ui_mode_clipboard" maxLength="12">クリップボード</string>
    <string name="quick_action__ime_ui_mode_media" maxLength="12">絵文字</string>
    <string name="quick_action__ime_ui_mode_media__tooltip">絵文字パネルを開く</string>
    <string name="quick_action__settings" maxLength="12">設定</string>
    <string name="quick_action__settings__tooltip">設定を開く</string>
    <string name="quick_action__undo" maxLength="12">元に戻す</string>
    <string name="quick_action__redo" maxLength="12">やり直し</string>
    <string name="quick_action__toggle_actions_overflow" maxLength="12">その他の操作</string>
    <string name="quick_action__toggle_incognito_mode" maxLength="12">シークレット</string>
    <string name="quick_action__noop" maxLength="12" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">なし</string>
    <!-- Incognito mode strings -->
    <!-- Settings UI strings -->
    <string name="settings__title" comment="Title of Settings">設定</string>
    <string name="settings__preview_keyboard" comment="Hint for try your setup box">セットアップを試す</string>
    <string name="settings__help" comment="General label for help buttons in Settings">ヘルプ</string>
    <string name="settings__default" comment="General string which is used when a preference has the default value set">デフォルト</string>
    <string name="settings__system_default" comment="General string which is used when a preference has the system default value set">システムのデフォルト</string>
    <string name="settings__home__title" comment="Title of the Home screen">ようこそ！{app_name}</string>
    <string name="settings__home__ime_not_enabled" comment="Error message shown in Home fragment when FlorisBoard is not enabled in the system">FlorisBoardはシステムで有効になっていないため、入力ピッカーの入力メソッドとして使用できません。 この問題を解決するには、ここをタップしてください。</string>
    <string name="settings__home__ime_not_selected" comment="Warning message shown in Home fragment when FlorisBoard is not selected as the default keyboard">FlorisBoardは、デフォルトの入力方法として選択されていません。 この問題を解決するには、ここをタップしてください。</string>
    <string name="settings__localization__title" comment="Title of languages and Layout screen">言語 &amp; 入力</string>
    <string name="settings__localization__display_language_names_in__label" comment="Label of Display language names in preference">で言語名を表示する</string>
    <string name="settings__localization__group_subtypes__label" comment="Label of subtypes group">サブタイプ</string>
    <string name="settings__localization__subtype_add_title" comment="Title of subtype dialog when adding a new subtype">サブタイプを追加</string>
    <string name="settings__localization__subtype_edit_title" comment="Title of subtype dialog when editing an existing subtype">サブタイプを編集</string>
    <string name="settings__localization__subtype_locale" comment="Label for locale dropdown in subtype dialog">主要言語</string>
    <string name="settings__localization__subtype_popup_mapping" comment="Label for popup mapping dropdown in subtype screen">ポップアップマッピング</string>
    <string name="settings__localization__subtype_characters_layout" comment="Label for layout dropdown in subtype dialog">キャラクターレイアウト</string>
    <string name="settings__localization__subtype_symbols_layout" comment="Label for layout dropdown in subtype dialog">シンボルのプライマリレイアウト</string>
    <string name="settings__localization__subtype_symbols2_layout" comment="Label for layout dropdown in subtype dialog">シンボルの二次レイアウト</string>
    <string name="settings__localization__subtype_composer" comment="Label for composer dropdown in subtype dialog.">作曲家</string>
    <string name="settings__localization__subtype_currency_set" comment="Label for currency set dropdown in subtype dialog. 'set' is used as a noun here and can be compared to a group of elements (in this case currency symbols).">通貨セット</string>
    <string name="settings__localization__subtype_numeric_layout" comment="Label for layout dropdown in subtype dialog">数字レイアウト</string>
    <string name="settings__localization__subtype_numeric_advanced_layout" comment="Label for layout dropdown in subtype dialog">(高度な) 数値レイアウト</string>
    <string name="settings__localization__subtype_numeric_row_layout" comment="Label for layout dropdown in subtype dialog">番号行のレイアウト</string>
    <string name="settings__localization__subtype_phone_layout" comment="Label for layout dropdown in subtype dialog">電話のプライマリレイアウト</string>
    <string name="settings__localization__subtype_phone2_layout" comment="Label for layout dropdown in subtype dialog">電話のセカンダリレイアウト</string>
    <string name="settings__localization__subtype_select_locale" comment="Subtype select language title">言語を選択</string>
    <string name="settings__localization__subtype_search_locale_placeholder" comment="Subtype search language placeholder">言語を検索</string>
    <string name="settings__localization__subtype_search_locale_not_found" comment="Subtype search language not found">\"{search_term}\"に一致する言語が見つかりませんでした。</string>
    <string name="settings__localization__subtype_select_placeholder" comment="Subtype dialog select value placeholder (&amp;#8210; is a hyphen character)">&#8210; 選択します &#8210;</string>
    <string name="settings__localization__subtype_summary" comment="Subtype summary">{characters_name} / {symbols_name} / {currency_set_name}</string>
    <string name="settings__localization__suggested_subtype_presets" comment="Suggested presets title">推奨されるサブタイププリセット</string>
    <string name="settings__localization__suggested_subtype_presets_none_found" comment="Suggested presets none found">利用可能な推奨プリセットはありません。 下のボタンを使用して、すべてのサブタイププリセットを表示します。</string>
    <string name="settings__localization__subtype_presets" comment="Subtype presets dialog title">サブタイププリセット</string>
    <string name="settings__localization__subtype_presets_view_all" comment="View all presets button">全て表示</string>
    <string name="settings__localization__subtype_no_subtypes_configured_warning" comment="Warning message that no subtype has been defined">サブタイプを構成していないようです。 フォールバックとして、サブタイプEnglish / QWERTYが使用されます！</string>
    <string name="settings__localization__subtype_error_already_exists" comment="Error message shown in subtype dialog when a subtype to add already exists">このサブタイプはすでに存在します！</string>
    <string name="settings__localization__subtype_error_fields_no_value" comment="Error message shown in subtype editor if at least one field is set to '- select -' (means no value specified)">少なくとも1つのフィールドで値が選択されていません。 フィールドの値を選択してください。</string>
    <string name="settings__localization__subtype_error_layout_not_installed" comment="Error message shown in subtype list when a layout is not installed, where %s will be replaced by the layout ID">{layout_id} (インストールされていません)</string>
    <string name="settings__localization__group_layouts__label" comment="Label of layouts group">レイアウト</string>
    <string name="settings__theme__title" comment="Title of the Theme screen">テーマ</string>
    <string name="pref__theme__mode__label" comment="Label of the theme mode preference">テーマモード</string>
    <string name="pref__theme__sunrise_time__label" comment="Label of the sunrise time preference">日の出時間</string>
    <string name="pref__theme__sunset_time__label" comment="Label of the sunset time preference">日の入時間</string>
    <string name="pref__theme__day" comment="Label of the day group (day means light theme)">日のテーマ</string>
    <string name="pref__theme__night" comment="Label of the night group (night means dark theme)">夜間テーマ</string>
    <string name="settings__theme_manager__title_manage" comment="Title of the theme manager screen for managing installed and custom themes">インストールされたテーマを管理</string>
    <string name="pref__theme__source_assets" comment="Label for the theme source field">FlorisBoardアプリアセット</string>
    <string name="pref__theme__source_internal" comment="Label for the theme source field">内部ストレージ</string>
    <string name="pref__theme__source_external" comment="Label for the theme source field">外部プロバイダー</string>
    <string name="settings__theme_manager__title_day" comment="Title of the theme manager screen for day theme selection">日のテーマを選択</string>
    <string name="settings__theme_manager__title_night" comment="Title of the theme manager screen for night theme selection">夜間テーマを選択</string>
    <string name="settings__theme_editor__fine_tune__title">微調整エディター</string>
    <string name="settings__theme_editor__fine_tune__level">編集レベル</string>
    <string name="settings__theme_editor__fine_tune__display_kbd_after_dialogs">ダイアログの後にキーボードを表示</string>
    <string name="settings__theme_editor__add_rule">ルールを追加</string>
    <string name="settings__theme_editor__edit_rule">ルールを編集</string>
    <string name="settings__theme_editor__no_rules_defined">このスタイルシートにはルールが定義されていません。 このスタイルシートのカスタマイズを開始するルールを追加します。</string>
    <string name="settings__theme_editor__rule_already_exists">このスタイルシートルールはすでに定義されています。</string>
    <string name="settings__theme_editor__rule_groups">グループ</string>
    <string name="settings__theme_editor__rule_selectors">セレクター</string>
    <string name="settings__theme_editor__code_already_exists">このキーコードはすでに定義されています。</string>
    <string name="settings__theme_editor__code_invalid">このキーコードは無効です。 キーコードが、文字の場合は {c_min} から {c_max} の範囲内、内部特殊キーの場合は {i_min} から {i_max} の範囲内にあることを確認してください。</string>
    <string name="settings__theme_editor__code_recording_help_text">キーのコードを見つけるには、コード入力フィールドの横にあるボタンを使用します。 アクティブ化されると、次にキーを押したことが記録され、コードが入力フィールドに挿入されます。</string>
    <string name="settings__theme_editor__add_property">プロパティを追加</string>
    <string name="settings__theme_editor__edit_property">プロパティを編集</string>
    <string name="settings__theme_editor__property_already_exists">この名前のプロパティは、現在のルール内にすでに存在します。</string>
    <string name="settings__theme_editor__property_name">プロパティの名前</string>
    <string name="settings__theme_editor__property_value">プロパティ値</string>
    <string name="settings__theme_editor__property_value_shape_apply_for_all_corners">すべてのコーナーに申し込む</string>
    <string name="settings__theme_editor__property_value_color_dialog_title">カラー文字列を編集</string>
    <string name="settings__theme_editor__component_meta_is_night_theme">夜間のテーマ</string>
    <string name="settings__theme_editor__component_meta_is_borderless">ボーダレスです</string>
    <string name="settings__theme_editor__component_meta_stylesheet_path">スタイルシートパス</string>
    <string name="snygg__rule_element__key">キー</string>
    <string name="snygg__rule_element__key_hint">重要なヒント</string>
    <string name="snygg__rule_element__clipboard_header">クリップボードヘッダー</string>
    <string name="snygg__rule_element__clipboard_item">クリップボードアイテム</string>
    <string name="snygg__rule_element__clipboard_item_popup">クリップボードアイテムのポップアップ</string>
    <string name="snygg__rule_element__glide_trail">グライドトレイル</string>
    <string name="snygg__rule_element__one_handed_panel">片手パネル</string>
    <string name="snygg__rule_element__smartbar_candidate_word">スマートバー候補の単語</string>
    <string name="snygg__rule_element__smartbar_candidate_clip">スマートバー候補クリップ</string>
    <string name="snygg__rule_element__smartbar_candidate_spacer">スマートバー候補スペーサー</string>
    <string name="snygg__rule_selector__pressed">押された</string>
    <string name="snygg__rule_selector__focus">集中</string>
    <string name="snygg__rule_selector__disabled">無効</string>
    <string name="snygg__property_name__background">バックグラウンド</string>
    <string name="snygg__property_name__foreground">フォアグラウンド</string>
    <string name="snygg__property_name__border_color">ボーダの色</string>
    <string name="snygg__property_name__border_style">ボーダースタイル</string>
    <string name="snygg__property_name__border_width">ボーダーの幅</string>
    <string name="snygg__property_name__font_family">フォントファミリー</string>
    <string name="snygg__property_name__font_size">フォントのサイズ</string>
    <string name="snygg__property_name__font_style">フォントスタイル</string>
    <string name="snygg__property_name__font_weight">フォントの太さ</string>
    <string name="snygg__property_name__shadow_elevation">影の標高</string>
    <string name="snygg__property_name__shape">形態</string>
    <string name="snygg__property_name__var_primary">プライマリーカラー</string>
    <string name="snygg__property_name__var_primary_variant">プライマリーカラー(バリアント)</string>
    <string name="snygg__property_name__var_secondary">セカンダリーカラー</string>
    <string name="snygg__property_name__var_secondary_variant">セカンダリーカラー(バリアント)</string>
    <string name="snygg__property_name__var_background">共通の背景</string>
    <string name="snygg__property_name__var_surface">共通の表面</string>
    <string name="snygg__property_name__var_surface_variant">共通の表面(バリアント)</string>
    <string name="snygg__property_name__var_on_primary">プライマリの前景</string>
    <string name="snygg__property_name__var_on_secondary">セカンダリの前景</string>
    <string name="snygg__property_name__var_on_background">背景の前景</string>
    <string name="snygg__property_name__var_on_surface">表面の前景</string>
    <string name="snygg__property_name__var_on_surface_variant">表面の前景(バリアント)</string>
    <string name="snygg__property_name__var_shape">一般的な形状</string>
    <string name="snygg__property_name__var_shape_variant">一般的な形状(バリアント)</string>
    <string name="snygg__property_value__explicit_inherit">継承</string>
    <string name="snygg__property_value__defined_var">変数リファレンス</string>
    <string name="snygg__property_value__solid_color">ソリッドカラー</string>
    <string name="snygg__property_value__rectangle_shape">長方形</string>
    <string name="snygg__property_value__circle_shape">円形</string>
    <string name="snygg__property_value__cut_corner_shape_dp">カットコーナー形状(dp)</string>
    <string name="snygg__property_value__cut_corner_shape_percent">カットコーナー形状(%)</string>
    <string name="snygg__property_value__rounded_corner_shape_dp">丸みを帯びた角の形(dp)</string>
    <string name="snygg__property_value__rounded_corner_shape_percent">丸みを帯びた角の形状(%)</string>
    <string name="snygg__property_value__dp_size">サイズ (dp)</string>
    <string name="snygg__property_value__sp_size">サイズ (sp)</string>
    <string name="snygg__property_value__percentage_size">サイズ (%)</string>
    <string name="settings__input_feedback__title" comment="Title of Input Feedback screen">音とバイブレーション</string>
    <string name="pref__input_feedback__group_audio__label" comment="Preference group title">ハウリング</string>
    <string name="pref__input_feedback__audio_enabled__label" comment="Preference title">ハウリングを有効にする</string>
    <string name="pref__input_feedback__audio_volume__label" comment="Preference title">入力イベントの音量</string>
    <string name="pref__input_feedback__audio_feat_key_press__label" comment="Preference title">キーを押したときのサウンド</string>
    <string name="pref__input_feedback__audio_feat_key_long_press__label" comment="Preference title">キーを長く押した時のサウンド</string>
    <string name="pref__input_feedback__audio_feat_key_repeated_action__label" comment="Preference title">キーリピートアクションサウンド</string>
    <string name="pref__input_feedback__audio_feat_gesture_swipe__label" comment="Preference title">ジェスチャーをスワイプした時のサウンド</string>
    <string name="pref__input_feedback__audio_feat_gesture_moving_swipe__label" comment="Preference title">ジェスチャーを移動した時のスワイプサウンド</string>
    <string name="pref__input_feedback__group_haptic__label" comment="Preference group title">触覚フィードバック/バイブレーション</string>
    <string name="pref__input_feedback__haptic_enabled__label" comment="Preference title">触覚フィードバックを有効</string>
    <string name="pref__input_feedback__haptic_vibration_duration__label" comment="Preference title">バイブレーション持続時間</string>
    <string name="pref__input_feedback__haptic_vibration_strength__label" comment="Preference title">バイブレーションの強さ</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_amplitude_ctrl" comment="Preference summary">この機能にはハードウェア振幅制御のサポートが必要ですが、これはこのデバイスにはありません</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_unsupported_android_version" comment="Preference summary">この機能には、Android8.0以降でのみ利用可能な振幅制御のサポートが必要です。</string>
    <string name="pref__input_feedback__haptic_feat_key_press__label" comment="Preference title">キーバイブレーション</string>
    <string name="pref__input_feedback__haptic_feat_key_long_press__label" comment="Preference title">キーを長く押した時の振動</string>
    <string name="pref__input_feedback__haptic_feat_key_repeated_action__label" comment="Preference title">キーリピートアクションバイブレーション</string>
    <string name="pref__input_feedback__haptic_feat_gesture_swipe__label" comment="Preference title">ジェスチャーをスワイプした時の振動</string>
    <string name="pref__input_feedback__haptic_feat_gesture_moving_swipe__label" comment="Preference title">ジェスチャーを移動した時のスワイプ振動</string>
    <string name="pref__input_feedback__any_feat_key_press__summary" comment="Preference summary">例えば キー、ボタン、絵文字タブ</string>
    <string name="pref__input_feedback__any_feat_key_long_press__summary" comment="Preference summary">例えば ポップアップメニュー</string>
    <string name="pref__input_feedback__any_feat_key_repeated_action__summary" comment="Preference summary">例えばキーを削除</string>
    <string name="pref__input_feedback__any_feat_gesture_swipe__summary" comment="Preference summary">実装されていません</string>
    <string name="pref__input_feedback__any_feat_gesture_moving_swipe__summary" comment="Preference summary">例えば カーソルコントロールスワイプ</string>
    <string name="settings__keyboard__title" comment="Title of Keyboard preferences screen">キーボード</string>
    <string name="pref__keyboard__number_row__label" comment="Preference title">行数</string>
    <string name="pref__keyboard__number_row__summary" comment="Preference summary">文字レイアウトの上に数字の行を表示する</string>
    <string name="pref__keyboard__hinted_number_row_mode__label" comment="Preference title">ヒント付きの数字の行</string>
    <string name="pref__keyboard__hinted_symbols_mode__label" comment="Preference title">ヒント記号</string>
    <string name="pref__keyboard__utility_key_enabled__label" comment="Preference title">ユーティリティキーを表示</string>
    <string name="pref__keyboard__utility_key_enabled__summary" comment="Preference summary">スペースバーの横に設定可能なユーティリティキーを表示します</string>
    <string name="pref__keyboard__utility_key_action__label" comment="Preference title">ユーティリティキーアクション</string>
    <string name="pref__keyboard__font_size_multiplier__label" comment="Preference title">フォントサイズの倍率</string>
    <string name="pref__keyboard__group_layout__label" comment="Preference group title">レイアウト</string>
    <string name="pref__keyboard__one_handed_mode__label" comment="Preference title">片手モード</string>
    <string name="pref__keyboard__one_handed_mode_scale_factor__label" comment="Preference title">片手モード時のキーボードの幅</string>
    <string name="pref__keyboard__landscape_input_ui_mode__label" comment="Preference value">横向き時のフルスクリーンの入力</string>
    <string name="pref__keyboard__height_factor__label" comment="Preference title">キーボードの高さ</string>
    <string name="pref__keyboard__key_spacing__label" comment="Preference title">キーの間隔</string>
    <string name="pref__keyboard__bottom_offset__label" comment="Preference title">ボトムオフセット</string>
    <string name="pref__keyboard__group_keypress__label" comment="Preference group title">キーを押す時の設定</string>
    <string name="pref__keyboard__popup_enabled__label" comment="Preference title">ポップアップの可視性</string>
    <string name="pref__keyboard__popup_enabled__summary" comment="Preference summary">キーを押すとポップアップが表示されます</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__label" comment="Preference title">アクセントにシンボルポップアップを含む</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__summary" comment="Preference summary">デフォルトのレイアウトのアクセントにシンボルポップアップを追加します</string>
    <string name="pref__keyboard__long_press_delay__label" comment="Preference title">長くキーを押した時の待ち時間</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__label" comment="Preference title">スペースバーを文字に切り替える</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__summary" comment="Preference summary">記号または数字の場合、自動的に文字に戻ります</string>
    <!-- Smartbar strings -->
    <string name="settings__smartbar__title" comment="Title of Smartbar screen">スマートバー</string>
    <string name="pref__smartbar__enabled__label" comment="Preference title">スマートバーを有効</string>
    <string name="pref__smartbar__enabled__summary" comment="Preference summary">キーボードの上に表示されます</string>
    <string name="pref__smartbar__flip_toggles__label" comment="Preference title">トグルボタンを切り替える</string>
    <string name="pref__smartbar__flip_toggles__summary" comment="Preference summary">アクション行の切り替えを反転します</string>
    <!-- Typing strings -->
    <string name="pref__suggestion__title" comment="Preference group title">提案</string>
    <string name="pref__suggestion__display_mode__label" comment="Preference title">提案表示モード</string>
    <string name="pref__suggestion__api30_inline_suggestions_enabled__summary" comment="Preference summary">オートフィルサービスによって提供されるインライン提案を表示する</string>
    <string name="pref__correction__title" comment="Preference group title">訂正</string>
    <string name="pref__correction__auto_capitalization__label" comment="Preference title">自動大文字変換</string>
    <string name="pref__correction__auto_capitalization__summary" comment="Preference summary">現在の入力コンテキストに基づいて単語を大文字にする</string>
    <string name="pref__correction__remember_caps_lock_state__label" comment="Preference title">CapsLock状態を維持</string>
    <string name="pref__correction__remember_caps_lock_state__summary" comment="Preference summary">別のテキストフィールドに移動しても、CapsLockはオンのままになります</string>
    <string name="pref__correction__double_space_period__label" comment="Preference title">ダブルスペースでピリオドを入力</string>
    <string name="pref__correction__double_space_period__summary" comment="Preference summary">スペースバーを2回タップすると、ピリオドの後にスペースが挿入されます</string>
    <string name="settings__dictionary__title" comment="Title of the User dictionaries screen">ユーザー辞書</string>
    <string name="pref__dictionary__enable_system_user_dictionary__label" comment="Preference title">システムユーザー辞書を有効化</string>
    <string name="pref__dictionary__enable_system_user_dictionary__summary" comment="Preference summary">システムユーザー辞書に保存されている単語を提案します</string>
    <string name="pref__dictionary__manage_system_user_dictionary__label" comment="Preference title">システムユーザー辞書を管理</string>
    <string name="pref__dictionary__manage_system_user_dictionary__summary" comment="Preference summary">システムユーザー辞書のエントリを追加、表示、および削除をします</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__label" comment="Preference title">内部ユーザー辞書を有効化</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__summary" comment="Preference summary">内部ユーザー辞書に保存されている単語を提案します</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__label" comment="Preference title">内部ユーザー辞書を管理</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__summary" comment="Preference summary">内部ユーザー辞書のエントリを追加、表示、および削除をします</string>
    <string name="settings__udm__title_floris" comment="Title of the User Dictionary Manager activity for internal">内部ユーザー辞書</string>
    <string name="settings__udm__title_system" comment="Title of the User Dictionary Manager activity for system">システムユーザー辞書</string>
    <string name="settings__udm__no_words_in_dictionary" comment="String to show if no words are present in the dictionary">このユーザー辞書には単語が含まれていません。</string>
    <string name="settings__udm__word_summary_freq" comment="Summary label for a word entry. The decimal placeholder inserts the frequency for the word it summarizes.">頻度:{freq}</string>
    <string name="settings__udm__word_summary_freq_shortcut" comment="Summary label for a word entry. The first placeholder inserts the frequency for the word it summarizes, the second placeholder the shortcut defined.">頻度:{freq} | ショートカット:{shortcut}</string>
    <string name="settings__udm__all_languages" comment="Label of the For all languages entry in the language list">すべての言語</string>
    <string name="settings__udm__dialog__locale_error_invalid" comment="Error label for the language code in the user dictionary add/edit dialog">この言語コードは、予想される構文に準拠していません。 コードは、言語のみ (en など)、言語と国 (en_US など)、または言語、国、およびスクリプト (en_US-script など) のいずれかである必要があります。</string>
    <!-- About UI strings -->
    <!-- Setup UI strings -->
    <string name="setup__enable_ime__description">Android では、使用する前にすべてのカスタム キーボードを個別に有効にする必要があります。 システム <i>言語 &amp; を開きます。 </i> 設定を入力すると、\"{app_name}\"が有効になります。</string>
    <!-- Back up & Restore -->
    <string name="backup_and_restore__restore__metadata_warn_different_version">このバックアップ アーカイブは、通常サポートされている現在とは別のバージョンで生成されました。 ただし、機能の違いにより、軽微な問題が発生したり、一部の設定が正しく転送されない場合があることに注意してください。</string>
    <!-- Crash Dialog strings -->
    <string name="crash_dialog__report_instructions" comment="Issue tracker report instructions for the crash dialog. The %s placeholder is the name of the crash report template and always in English/LTR.">このエラーを報告する場合、クラッシュがまだ報告されていない場合は、まず GitHub の課題トラッカーを確認してください。\n報告されていない場合は、生成されたクラッシュ ログをコピーして、新しい課題を開きます。 \"%s\"テンプレートを使用して、説明と再現手順を入力し、生成されたクラッシュ ログを最後に貼り付けます。 これは、FlorisBoard を誰にとってもより良く、より安定させるのに役立ちます。 ありがとうございました！</string>
    <!-- Clipboard strings -->
    <string name="clipboard__disabled__message">{app_name} のクリップボード履歴を使用すると、コピーしたテキストや画像をすばやく保存してアクセスできます。また、アイテムを固定したり、自動クリーンアップを構成したり、アイテムの上限を設定したりできます。</string>
    <!-- Devtools strings -->
    <!-- Extension strings -->
    <string name="ext__editor__create_component__from_empty_warning">{app_name} を初めて使用する場合、または詳細に慣れていない場合は、空のコンポーネントを作成して構成するのが難しい場合があります。 その場合は、既存のコンポーネントをコピーして好みに合わせて変更することを検討してください。</string>
    <string name="ext__import__file_skip_ext_core" comment="Reason string when ext has core extension ID">アプリのコア アセットで提供されるデフォルトの拡張機能パッケージを置換または更新できません。 新しいバージョンのコア拡張機能パッケージを使用する場合は、アプリ自体を更新することを検討してください。</string>
    <!-- Action strings -->
    <!-- Error strings (generic) -->
    <!-- General strings -->
    <!-- Screen orientation strings -->
    <!-- State strings -->
    <!-- Enum label and description strings -->
    <string name="enum__extended_actions_placement__overlay_app_ui__description" comment="Enum value description">キーボード UI の高さに影響を与えずに、拡張アクションをアプリ UI の上にオーバーレイとして配置します。 この配置により、アプリの入力フィールドが部分的に上書きされる可能性があることに注意してください</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly__description" comment="Enum value description">{app_name} デフォルトのハードウェアバイブレータと直接やり取りします。 これにより、振動の持続時間と強さをより詳細に制御できますが、振動は、触覚フィードバック インターフェイスを使用するほど滑らかで最適化されない場合があります。</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface__description" comment="Enum value description">{app_name} は触覚フィードバック インターフェースを使用して、キーを押すと定義済みのバイブレーション シーケンスをトリガーします。 これは、一部のデバイスでは非常にうまく動作する場合がありますが、他のデバイスでは動作しないか、パフォーマンスが非常に低くなります。</string>
    <string name="enum__incognito_mode__force_off__description" comment="Enum value description">ターゲット アプリの渡されたオプションに関係なく、シークレット モードは常に無効になります。 このオプションでは、Smartbar のシークレット クイック アクションは使用できません。</string>
    <string name="enum__incognito_mode__force_on__description" comment="Enum value description">ターゲット アプリの渡されたオプションに関係なく、シークレット モードは常に有効になります。 このオプションでは、Smartbar のシークレット クイック アクションは使用できません。</string>
    <string name="enum__incognito_mode__dynamic_on_off__description" comment="Enum value description">推奨オプション。 シークレット モードは、ターゲット アプリのオプションを使用するか、Smartbar のシークレット クイック アクションを介して手動で切り替えることにより、動的に有効または無効になります。</string>
    <!-- Unit strings (symbols) -->
    <!-- Unit strings (written words) -->
</resources>
