<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">FlorisBoard</string>
    <string name="key__phone_pause" comment="Label for the Pause key in the telephone keyboard layout">Pause</string>
    <string name="key__phone_wait" comment="Label for the Wait key in the telephone keyboard layout">Warten</string>
    <string name="key_popup__threedots_alt" comment="Content description for the three-dots icon in a key popup">Drei-Punkte-Symbol. Zeigt an, dass durch langes Drücken mehr Zeichen verwendet werden können.</string>
    <!-- One-handed strings -->
    <string name="one_handed__close_btn_content_description" comment="Content description for the one-handed close button">Einhandmodus schließen.</string>
    <string name="one_handed__move_start_btn_content_description" comment="Content description for the one-handed move to left button">Tastatur nach links verschieben.</string>
    <string name="one_handed__move_end_btn_content_description" comment="Content description for the one-handed move to right button">Tastatur nach rechts verschieben.</string>
    <!-- Media strings -->
    <string name="settings__media__title">Emojis</string>
    <string name="media__tab__emojis" comment="Tab description for emojis in the media UI">Emojis</string>
    <string name="media__tab__emoticons" comment="Tab description for emoticons in the media UI">Emoticons</string>
    <string name="media__tab__kaomoji" comment="Tab description for kaomoji in the media UI">Kaomoji</string>
    <string name="prefs__media__emoji_preferred_skin_tone">Bevorzugte Emoji-Hautfarbe</string>
    <string name="prefs__media__emoji_preferred_hair_style">Bevorzugte Emoji-Haarfarbe</string>
    <string name="prefs__media__emoji_history__title" comment="Preference group title">Emoji-Historie</string>
    <string name="prefs__media__emoji_history_enabled" comment="Preference title">Aktiviere Emoji-Historie</string>
    <string name="prefs__media__emoji_history_enabled__summary" comment="Preference summary">Behalte kürzlich genutzte Emojis für einen schnellen Zugriff</string>
    <string name="prefs__media__emoji_history_pinned_update_strategy" comment="Preference title">Update Strategie (gepinnt)</string>
    <string name="prefs__media__emoji_history_recent_update_strategy" comment="Preference title">Update Strategie (kürzlich)</string>
    <string name="prefs__media__emoji_history_max_size">Maximale zu behaltende Emojis</string>
    <string name="prefs__media__emoji_history_pinned_reset">Angepinnte Emojis zurücksetzen</string>
    <string name="prefs__media__emoji_history_reset">Zuletzt verwendete Emojis zurücksetzen</string>
    <string name="prefs__media__emoji_suggestion__title" comment="Preference group title">Emoji Vorschläge</string>
    <string name="prefs__media__emoji_suggestion_enabled" comment="Preference title">Aktiviere Emoji Vorschläge</string>
    <string name="prefs__media__emoji_suggestion_enabled__summary" comment="Preference summary">Erhalte Emoji Vorschläge während des Tippens</string>
    <string name="prefs__media__emoji_suggestion_type" comment="Preference title">Auslösetyp</string>
    <string name="prefs__media__emoji_suggestion_update_history" comment="Preference title">Update Emoji-Historie</string>
    <string name="prefs__media__emoji_suggestion_update_history__summary" comment="Preference summary">Angenommene vorgeschlagene Emojis werden zur Emoji-Historie hinzugefügt</string>
    <string name="prefs__media__emoji_suggestion_candidate_show_name" comment="Preference title">Zeige Emoji Name</string>
    <string name="prefs__media__emoji_suggestion_candidate_show_name__summary" comment="Preference summary">Emoji Vorschläge zeigen den Namen neben dem Emoji</string>
    <string name="prefs__media__emoji_suggestion_query_min_length" comment="Preference title">Minimale Suchbegriff-Länge</string>
    <string name="prefs__media__emoji_suggestion_candidate_max_count" comment="Preference title">Maximale Anzahl von Vorschlägen</string>
    <!-- Emoji strings -->
    <string name="emoji__category__smileys_emotion" comment="Emoji category name">Smileys &amp; Emotionen</string>
    <string name="emoji__category__people_body" comment="Emoji category name">Personen &amp; Körper</string>
    <string name="emoji__category__animals_nature" comment="Emoji category name">Tiere &amp; Natur</string>
    <string name="emoji__category__food_drink" comment="Emoji category name">Essen &amp; Trinken</string>
    <string name="emoji__category__travel_places" comment="Emoji category name">Reisen &amp; Orte</string>
    <string name="emoji__category__activities" comment="Emoji category name">Aktivitäten</string>
    <string name="emoji__category__objects" comment="Emoji category name">Objekte</string>
    <string name="emoji__category__symbols" comment="Emoji category name">Symbole</string>
    <string name="emoji__category__flags" comment="Emoji category name">Flaggen</string>
    <string name="emoji__history__empty_message" comment="Message if the emoji history is empty">Keine kürzlich benutzen Emojis gefunden. Sobald Emojis benutzt werden, erscheinen diese hier.</string>
    <string name="emoji__history__phone_locked_message" comment="Message to show if phone is locked">Um auf deinen Emoji-Historie zuzugreifen, entsperre das Gerät.</string>
    <string name="emoji__history__usage_tip" comment="Feature discoverability for actions of emoji history">Tipp: Tippe lange auf Emojis in der Historie um sie anzupinnen oder zu entfernen!</string>
    <string name="emoji__history__removal_success_message" comment="Toast message if user has used the delete action on an emoji in the emoji history">{emoji} aus Historie gelöscht</string>
    <string name="emoji__history__pinned">Angepinnt</string>
    <string name="emoji__history__recent">Kürzlich</string>
    <!-- Quick action strings -->
    <string name="quick_action__arrow_up" maxLength="12">Pfeil hoch</string>
    <string name="quick_action__arrow_up__tooltip">Pfeil nach oben ausführen</string>
    <string name="quick_action__arrow_down" maxLength="12">Pfeil unten</string>
    <string name="quick_action__arrow_down__tooltip">Pfeil nach unten ausführen</string>
    <string name="quick_action__arrow_left" maxLength="12">Pfeil links</string>
    <string name="quick_action__arrow_left__tooltip">Pfeil nach links ausführen</string>
    <string name="quick_action__arrow_right" maxLength="12">Pfeil rechts</string>
    <string name="quick_action__arrow_right__tooltip">Pfeil nach rechts ausführen</string>
    <string name="quick_action__clipboard_clear_primary_clip" maxLength="12">Clip löschen</string>
    <string name="quick_action__clipboard_clear_primary_clip__tooltip">Hauptclip von der Zwischenablage löschen</string>
    <string name="quick_action__clipboard_copy" maxLength="12">Kopieren</string>
    <string name="quick_action__clipboard_copy__tooltip">Zur Zwischenablage kopieren</string>
    <string name="quick_action__clipboard_cut" maxLength="12">Ausschneiden</string>
    <string name="quick_action__clipboard_cut__tooltip">Zur Zwischenablage ausschneiden</string>
    <string name="quick_action__clipboard_paste" maxLength="12">Einfügen</string>
    <string name="quick_action__clipboard_paste__tooltip">Einfügen aus der Zwischenablage</string>
    <string name="quick_action__clipboard_select_all" maxLength="12">Alles ausw.</string>
    <string name="quick_action__clipboard_select_all__tooltip">Alles auswählen in der Zwischenablage</string>
    <string name="quick_action__ime_ui_mode_clipboard" maxLength="12">Zwischenab.</string>
    <string name="quick_action__ime_ui_mode_clipboard__tooltip">Zwischenablage Verlauf öffnen</string>
    <string name="quick_action__ime_ui_mode_media" maxLength="12">Emoji</string>
    <string name="quick_action__ime_ui_mode_media__tooltip">Öffne Emoji-Panel</string>
    <string name="quick_action__settings" maxLength="12">Einstellung</string>
    <string name="quick_action__settings__tooltip">Einstellungen öffnen</string>
    <string name="quick_action__undo" maxLength="12">Rückgängig</string>
    <string name="quick_action__undo__tooltip">Letzte Aktion rückgängig machen</string>
    <string name="quick_action__redo" maxLength="12">Wiederholen</string>
    <string name="quick_action__redo__tooltip">Letzte rückgängig gemachte Aktion wiederholen</string>
    <string name="quick_action__toggle_actions_overflow" maxLength="12">Mehr</string>
    <string name="quick_action__toggle_actions_overflow__tooltip">Zusätzliche Aktionen anzeigen oder verstecken</string>
    <string name="quick_action__toggle_incognito_mode" maxLength="12">Inkognito</string>
    <string name="quick_action__toggle_incognito_mode__tooltip">Inkognitomodus umschalten</string>
    <string name="quick_action__toggle_autocorrect" maxLength="12">Autokorrekt.</string>
    <string name="quick_action__toggle_autocorrect__tooltip">Autokorrektur umschalten</string>
    <string name="quick_action__voice_input" maxLength="12">Spracheinga.</string>
    <string name="quick_action__voice_input__tooltip" comment="IME stands for Input Method Editor and is indirectly equivalent to 'keyboard'.">Öffne Anbieter für Spracheingabe</string>
    <string name="quick_action__one_handed_mode" maxLength="12">Einhändig</string>
    <string name="quick_action__one_handed_mode__tooltip">Einhandmodus umschalten</string>
    <string name="quick_action__drag_marker" maxLength="12" comment="This action is only used as a placeholder in the actions editor drag and drop screen and only visible in debug mode">Zug-Marker</string>
    <string name="quick_action__drag_marker__tooltip" comment="This action is only used as a placeholder in the actions editor drag and drop screen and only visible in debug mode">Aktuelle Marker-Position</string>
    <string name="quick_action__noop" maxLength="12" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">Keine</string>
    <string name="quick_action__noop__tooltip" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">Keine Operation</string>
    <string name="quick_actions_overflow__customize_actions_button">Aktionen neu ordnen</string>
    <string name="quick_actions_editor__header">Passe Reihenfolge der Aktionen an</string>
    <string name="quick_actions_editor__subheader_sticky_action">Fixierte Aktion ({n})</string>
    <string name="quick_actions_editor__subheader_dynamic_actions">Dynamische Aktionen ({n})</string>
    <string name="quick_actions_editor__subheader_hidden_actions">Versteckte Aktionen ({n})</string>
    <string name="select_subtype_panel__header"></string>
    <!-- Incognito mode strings -->
    <string name="incognito_mode__toast_after_enabled">Inkognito-Modus ist nun aktiviert \n\n{app_name} wird während dieses Modus keine neuen Wörter lernen</string>
    <string name="incognito_mode__toast_after_disabled">Inkognito-Modus ist nun standardmäßig deaktiviert</string>
    <!-- Settings UI strings -->
    <string name="settings__title" comment="Title of Settings">Einstellungen</string>
    <string name="settings__preview_keyboard" comment="Hint for try your setup box">Tastaturkonfiguration ausprobieren</string>
    <string name="settings__help" comment="General label for help buttons in Settings">Hilfe</string>
    <string name="settings__default" comment="General string which is used when a preference has the default value set">Standard</string>
    <string name="settings__system_default" comment="General string which is used when a preference has the system default value set">Systemstandard</string>
    <string name="settings__home__title" comment="Title of the Home screen">Willkommen bei {app_name}</string>
    <string name="settings__home__ime_not_enabled" comment="Error message shown in Home fragment when FlorisBoard is not enabled in the system">FlorisBoard ist in deinem System nicht aktiviert und kann daher nicht als Eingabemethode ausgewählt werden. Hier klicken, um dieses Problem zu lösen.</string>
    <string name="settings__home__ime_not_selected" comment="Warning message shown in Home fragment when FlorisBoard is not selected as the default keyboard">FlorisBoard ist nicht als Standard-Eingabemethode ausgewählt. Hier klicken, um dieses Problem zu lösen.</string>
    <string name="settings__localization__title" comment="Title of languages and Layout screen">Sprachen &amp; Layouts</string>
    <string name="settings__localization__display_language_names_in__label" comment="Label of Display language names in preference">Sprachnamen anzeigen in</string>
    <string name="settings__localization__group_subtypes__label" comment="Label of subtypes group">Untertypen</string>
    <string name="settings__localization__subtype_add_title" comment="Title of subtype dialog when adding a new subtype">Stil hinzufügen</string>
    <string name="settings__localization__language_pack_title" comment="Title of the language pack manager screen for managing installed and custom language packs">Installierte Sprachpakete verwalten</string>
    <string name="settings__localization__language_pack_summary" comment="Summary of preference item for adding a new language pack">Experimentell: Verwaltung von Erweiterungen, die Unterstützung für bestimmte Sprachen hinzufügen (vorerst formgebundene chinesische Eingabe)</string>
    <string name="settings__localization__subtype_edit_title" comment="Title of subtype dialog when editing an existing subtype">Benutzerdefinierten Eingabestil bearbeiten</string>
    <string name="settings__localization__subtype_locale" comment="Label for locale dropdown in subtype dialog">Primäre Sprache</string>
    <string name="settings__localization__subtype_popup_mapping" comment="Label for popup mapping dropdown in subtype screen">Pop-up-Anordnung</string>
    <string name="settings__localization__subtype_characters_layout" comment="Label for layout dropdown in subtype dialog">Zeichenanordnung</string>
    <string name="settings__localization__subtype_suggestion_provider" comment="Label for suggestion provider dropdown in subtype dialog">Vorschlag Generator</string>
    <string name="settings__localization__subtype_symbols_layout" comment="Label for layout dropdown in subtype dialog">Primäre Symbolanordnung</string>
    <string name="settings__localization__subtype_symbols2_layout" comment="Label for layout dropdown in subtype dialog">Sekundäre Symbolanordnung</string>
    <string name="settings__localization__subtype_composer" comment="Label for composer dropdown in subtype dialog.">Verfasser</string>
    <string name="settings__localization__subtype_currency_set" comment="Label for currency set dropdown in subtype dialog. 'set' is used as a noun here and can be compared to a group of elements (in this case currency symbols).">Währungssatz</string>
    <string name="settings__localization__subtype_numeric_layout" comment="Label for layout dropdown in subtype dialog">Zahlenlayout</string>
    <string name="settings__localization__subtype_numeric_advanced_layout" comment="Label for layout dropdown in subtype dialog">(Erweiterte) Numerische Anordnung</string>
    <string name="settings__localization__subtype_numeric_row_layout" comment="Label for layout dropdown in subtype dialog">Anordnung Zahlenreihe</string>
    <string name="settings__localization__subtype_phone_layout" comment="Label for layout dropdown in subtype dialog">Primäres Wählfeld</string>
    <string name="settings__localization__subtype_phone2_layout" comment="Label for layout dropdown in subtype dialog">Sekundäres Wählfeld</string>
    <string name="settings__localization__subtype_select_locale" comment="Subtype select language title">Sprache auswählen</string>
    <string name="settings__localization__subtype_search_locale_placeholder" comment="Subtype search language placeholder">Nach einer Sprache suchen</string>
    <string name="settings__localization__subtype_search_locale_not_found" comment="Subtype search language not found">Es konnte keine Sprache gefunden werden, die mit „{search_term}“ übereinstimmt.</string>
    <string name="settings__localization__subtype_select_placeholder" comment="Subtype dialog select value placeholder (&amp;#8210; is a hyphen character)">&#8210; wählen &#8210;</string>
    <string name="settings__localization__subtype_summary" comment="Subtype summary">{characters_name} / {symbols_name} / {currency_set_name}</string>
    <string name="settings__localization__suggested_subtype_presets" comment="Suggested presets title">Vorgeschlagene Eingabestile</string>
    <string name="settings__localization__suggested_subtype_presets_none_found" comment="Suggested presets none found">Keine vorgeschlagenen Voreinstellungen verfügbar. Verwende die Schaltfläche unten, um alle Untertyp-Voreinstellungen anzuzeigen.</string>
    <string name="settings__localization__subtype_presets" comment="Subtype presets dialog title">Vorkonfigurierte Eingabestile</string>
    <string name="settings__localization__subtype_presets_view_all" comment="View all presets button">Alle anzeigen</string>
    <string name="settings__localization__subtype_no_subtypes_configured_warning" comment="Warning message that no subtype has been defined">Es scheinen keine benutzerdefinierten Eingabestile konfiguriert zu sein. Als Ausweichlösung wird daher der Eingabestil English/QWERTY benutzt!</string>
    <string name="settings__localization__subtype_error_already_exists" comment="Error message shown in subtype dialog when a subtype to add already exists">Dieser Eingabestil ist bereits vorhanden!</string>
    <string name="settings__localization__subtype_error_fields_no_value" comment="Error message shown in subtype editor if at least one field is set to '- select -' (means no value specified)">In mindestens einem Feld ist kein Wert ausgewählt. Bitte wähle einen Wert für das Feld / die Felder.</string>
    <string name="settings__localization__subtype_error_layout_not_installed" comment="Error message shown in subtype list when a layout is not installed, where %s will be replaced by the layout ID">{layout_id} (nicht installiert)</string>
    <string name="settings__localization__group_layouts__label" comment="Label of layouts group">Layouts</string>
    <string name="settings__localization__subtype_delete_confirmation_title" comment="Title of the subtype delete confirmation dialog">Löschen bestätigen</string>
    <string name="settings__localization__subtype_delete_confirmation_warning" comment="Warning message in the confirmation dialog to confirm the user's intent to delete">Bist du sicher, dass du diesen Untertyp löschen willst?</string>
    <string name="settings__theme__title" comment="Title of the Theme screen">Design</string>
    <string name="pref__theme__mode__label" comment="Label of the theme mode preference">Design-Modus</string>
    <string name="pref__theme__sunrise_time__label" comment="Label of the sunrise time preference">Sonnenuntergangszeit</string>
    <string name="pref__theme__sunset_time__label" comment="Label of the sunset time preference">Sonnenuntergangszeit</string>
    <string name="pref__theme__day" comment="Label of the day group (day means light theme)">Helles Design</string>
    <string name="pref__theme__night" comment="Label of the night group (night means dark theme)">Dunkles Design</string>
    <string name="pref__theme__theme_accent_color__label" comment="Label of accent color preference in Theme">Akzentfarbe (Material You Designs)</string>
    <string name="settings__theme_manager__title_manage" comment="Title of the theme manager screen for managing installed and custom themes">Installierte Designs verwalten</string>
    <string name="pref__theme__source_assets" comment="Label for the theme source field">FlorisBoard App Ressourcen</string>
    <string name="pref__theme__source_internal" comment="Label for the theme source field">Interner Speicher</string>
    <string name="pref__theme__source_external" comment="Label for the theme source field">Externer Anbieter</string>
    <string name="settings__theme_manager__title_day" comment="Title of the theme manager screen for day theme selection">Tages-Design auswählen</string>
    <string name="settings__theme_manager__title_night" comment="Title of the theme manager screen for night theme selection">Nacht-Design auswählen</string>
    <string name="settings__theme_editor__fine_tune__title">Editoreinstellungen</string>
    <string name="settings__theme_editor__fine_tune__level">Bearbeitungsmodus</string>
    <string name="settings__theme_editor__fine_tune__display_kbd_after_dialogs">Zeige Tastatur nach Dialogen</string>
    <string name="settings__theme_editor__add_rule">Regel hinzufügen</string>
    <string name="settings__theme_editor__edit_rule">Regel bearbeiten</string>
    <string name="settings__theme_editor__no_rules_defined">Für das Stylesheet sind keine Regeln definiert. Füge eine Regel hinzu, um dieses Stylesheet anzupassen.</string>
    <string name="settings__theme_editor__rule_already_exists">Diese Stylesheetregel ist schon definiert.</string>
    <string name="settings__theme_editor__rule_codes">Ziel-Codes</string>
    <string name="settings__theme_editor__rule_groups">Gruppen</string>
    <string name="settings__theme_editor__rule_selectors">Selektoren</string>
    <string name="settings__theme_editor__add_code">Key code hinzufügen</string>
    <string name="settings__theme_editor__edit_code">Key code bearbeiten</string>
    <string name="settings__theme_editor__no_codes_defined">Regel auf alle Zielelemente anwenden.</string>
    <string name="settings__theme_editor__code_already_exists">Dieser Tastencode ist bereits definiert.</string>
    <string name="settings__theme_editor__code_invalid">Dieser Tastencode ist nicht gültig. Stelle sicher, dass der Tastencode innerhalb der Spanne von {c_min} bis {c_max} für Zeichen oder von {i_min} bis {i_max} für interne Spezialschlüssel liegt.</string>
    <string name="settings__theme_editor__code_help_text">Alternativ werden die folgenden Links helfen die korrespondierenden Codes zu finden:</string>
    <string name="settings__theme_editor__code_placeholder">Code</string>
    <string name="settings__theme_editor__code_recording_help_text">Um den Code einer Taste zu ermitteln, verwenden Sie die Schaltfläche neben dem Code-Eingabefeld. Sobald sie aktiviert ist, zeichnet sie den nächsten Tastendruck auf und fügt den Code in das Eingabefeld ein.</string>
    <string name="settings__theme_editor__code_recording_started">Key code aufnahme gestartet</string>
    <string name="settings__theme_editor__code_recording_stopped">Key code aufnahme beendet</string>
    <string name="settings__theme_editor__code_recording_requires_default_ime_floris">{app_name} muss die Standard-Tastatur-App sein, um eine Taste aufzunehmen</string>
    <string name="settings__theme_editor__code_recording_placeholder">Aufnahme läuft …</string>
    <string name="settings__theme_editor__add_property">Eigenschaft hinzufügen</string>
    <string name="settings__theme_editor__edit_property">Eigenschaft bearbeiten</string>
    <string name="settings__theme_editor__property_already_exists">Eine Eigenschaft mit diesem Namen existiert bereits in der aktuellen Regel.</string>
    <string name="settings__theme_editor__property_name">Eigenschaftsname</string>
    <string name="settings__theme_editor__property_value">Eigenschaftswert</string>
    <string name="settings__theme_editor__property_value_shape_apply_for_all_corners">Auf alle Ecken anwenden</string>
    <string name="settings__theme_editor__property_value_color_dialog_title">Farbzeichenkette bearbeiten</string>
    <string name="settings__theme_editor__file_selector_dialog_title">Datei auswählen</string>
    <string name="settings__theme_editor__component_meta_is_night_theme">Ist Nachtthema</string>
    <string name="settings__theme_editor__component_meta_is_borderless">Ist randlos</string>
    <string name="settings__theme_editor__component_meta_stylesheet_path">Stylesheetpfad</string>
    <string name="snygg__rule_annotation__defines">Variablen</string>
    <string name="snygg__rule_annotation__font">Schriftart</string>
    <string name="snygg__rule_annotation__font_name">Schriftname</string>
    <string name="snygg__rule_element__root">Wurzel</string>
    <string name="snygg__rule_element__window">Fenster</string>
    <string name="snygg__rule_element__key">Taste</string>
    <string name="snygg__rule_element__key_hint">Tastenhinweis</string>
    <string name="snygg__rule_element__clipboard_header">Zwischenablagekopfzeile</string>
    <string name="snygg__rule_element__clipboard_header_text">Zwischenablagekopfzeilentext</string>
    <string name="snygg__rule_element__clipboard_item">Zwischenablagenitem</string>
    <string name="snygg__rule_element__clipboard_item_popup">Zwischensblagenitem-Popup</string>
    <string name="snygg__rule_element__extracted_landscape_input_layout">Eingabeanordnung im Querformat</string>
    <string name="snygg__rule_element__extracted_landscape_input_field">Eingabefeld im Querformat</string>
    <string name="snygg__rule_element__extracted_landscape_input_action">Eingabeaktion im Querformat</string>
    <string name="snygg__rule_element__glide_trail">Bewegungsspur</string>
    <string name="snygg__rule_element__incognito_mode_indicator">Inkognito-Modus Indikator</string>
    <string name="snygg__rule_element__media">Medien</string>
    <string name="snygg__rule_element__one_handed_panel">Einhändiges Feld</string>
    <string name="snygg__rule_element__smartbar">Schnellzugriffsleiste</string>
    <string name="snygg__rule_element__smartbar_shared_actions_row">Smartbar geteilte Aktionsleiste</string>
    <string name="snygg__rule_element__smartbar_shared_actions_toggle">Smartbar geteilte Aktionszeile Schalter</string>
    <string name="snygg__rule_element__smartbar_extended_actions_row">Smartbar erweiterte Aktionsleiste</string>
    <string name="snygg__rule_element__smartbar_extended_actions_toggle">Smartbar erweiterte Aktionsleiste Schalter</string>
    <string name="snygg__rule_element__smartbar_action_key">Smartbar Aktionstaste</string>
    <string name="snygg__rule_element__smartbar_action_tile">Smartbar Aktionsfeld</string>
    <string name="snygg__rule_element__smartbar_actions_overflow">Smartbar Aktionsüberlauf</string>
    <string name="snygg__rule_element__smartbar_actions_overflow_customize_button">Smartbar Aktionsüberlauf Bearbeitungsknopf</string>
    <string name="snygg__rule_element__smartbar_actions_editor">Smartbar Aktionseditor</string>
    <string name="snygg__rule_element__smartbar_actions_editor_header">Smartbar Aktionseditor Kopfzeile</string>
    <string name="snygg__rule_element__smartbar_actions_editor_subheader">Smartbar Aktionseditor Unterkopfzeile</string>
    <string name="snygg__rule_element__smartbar_candidates_row">Smartbar Kandidatenzeile</string>
    <string name="snygg__rule_element__smartbar_candidate_word">Smartbar Wortkandidat</string>
    <string name="snygg__rule_element__smartbar_candidate_clip">Smartbar Kandidatenclip</string>
    <string name="snygg__rule_element__smartbar_candidate_spacer">Smartbar Kandidaten-Abstandshalter</string>
    <string name="snygg__rule_selector__pressed">Gedrückt</string>
    <string name="snygg__rule_selector__focus">Fokussiert</string>
    <string name="snygg__rule_selector__hover">Schweben</string>
    <string name="snygg__rule_selector__disabled">Deaktiviert</string>
    <string name="snygg__property_name__background">Hintergrund</string>
    <string name="snygg__property_name__foreground">Vordergrund</string>
    <string name="snygg__property_name__background_image">Hintergrundbild</string>
    <string name="snygg__property_name__border_color">Rahmenfarbe</string>
    <string name="snygg__property_name__border_style">Rahmenstil</string>
    <string name="snygg__property_name__border_width">Rahmenbreite</string>
    <string name="snygg__property_name__font_family">Schriftart</string>
    <string name="snygg__property_name__font_size">Schriftgröße</string>
    <string name="snygg__property_name__font_style">Schriftstil</string>
    <string name="snygg__property_name__font_weight">Schriftstärke</string>
    <string name="snygg__property_name__letter_spacing">Zeichenabstand</string>
    <string name="snygg__property_name__line_height">Zeilenhöhe</string>
    <string name="snygg__property_name__shadow_elevation">Höhe des Schattens</string>
    <string name="snygg__property_name__shape">Form</string>
    <string name="snygg__property_name__var_primary">Primärfarbe</string>
    <string name="snygg__property_name__var_primary_variant">Primärfarbe (Variante)</string>
    <string name="snygg__property_name__var_secondary">Sekundärfarbe</string>
    <string name="snygg__property_name__var_secondary_variant">Sekundärfarbe (Variante)</string>
    <string name="snygg__property_name__var_background">Gemeinsamer Hintergrund</string>
    <string name="snygg__property_name__var_surface">Gemeinsame Oberfläche</string>
    <string name="snygg__property_name__var_surface_variant">Gemeinsame Oberfläche (Variante)</string>
    <string name="snygg__property_name__var_on_primary">Vordergrund der primären</string>
    <string name="snygg__property_name__var_on_secondary">Vordergrund der sekundären</string>
    <string name="snygg__property_name__var_on_background">Vordergrund des Hintergrunds</string>
    <string name="snygg__property_name__var_on_surface">Vordergrund der Oberfläche</string>
    <string name="snygg__property_name__var_on_surface_variant">Vordergrund der Oberfläche (Variante)</string>
    <string name="snygg__property_name__var_shape">Gemeinsame Form</string>
    <string name="snygg__property_name__var_shape_variant">Gemeinsame Form (Variante)</string>
    <string name="snygg__property_value__explicit_inherit">Erben</string>
    <string name="snygg__property_value__defined_var">Variable referenzieren</string>
    <string name="snygg__property_value__yes">Ja</string>
    <string name="snygg__property_value__no">Nein</string>
    <string name="snygg__property_value__solid_color">Einfarbig</string>
    <string name="snygg__property_value__material_you_light_color">Material You Farbe (Hell)</string>
    <string name="snygg__property_value__material_you_dark_color">Material You Farbe (Dunkel)</string>
    <string name="snygg__property_value__font_style">Schriftart</string>
    <string name="snygg__property_value__rectangle_shape">Rechteckform</string>
    <string name="snygg__property_value__circle_shape">Kreisform</string>
    <string name="snygg__property_value__cut_corner_shape_dp">Form mit eckigen Ecken (dp)</string>
    <string name="snygg__property_value__cut_corner_shape_percent">Form mit eckigen Ecken (%)</string>
    <string name="snygg__property_value__rounded_corner_shape_dp">Form mit abgerundeten Ecken (dp)</string>
    <string name="snygg__property_value__rounded_corner_shape_percent">Form mit abgerundeten Ecken (%)</string>
    <string name="snygg__property_value__dp_size">Größe (dp)</string>
    <string name="snygg__property_value__sp_size">Größe (sp)</string>
    <string name="snygg__property_value__percentage_size">Größe (%)</string>
    <string name="settings__input_feedback__title" comment="Title of Input Feedback screen">Ton &amp; Vibration</string>
    <string name="pref__input_feedback__group_audio__label" comment="Preference group title">Ton Rückmeldung / Klänge</string>
    <string name="pref__input_feedback__audio_enabled__label" comment="Preference title">Ton Rückmeldung aktivieren</string>
    <string name="pref__input_feedback__audio_enabled__summary_disabled" comment="Preference summary">Niemals Töne für Eingaben abspielen, unabhängig von den Systemeinstellungen</string>
    <string name="pref__input_feedback__audio_volume__label" comment="Preference title">Lautstärke für Eingabeereignisse</string>
    <string name="pref__input_feedback__audio_feat_key_press__label" comment="Preference title">Tastentöne</string>
    <string name="pref__input_feedback__audio_feat_key_long_press__label" comment="Preference title">Ton bei langem Tastendruck</string>
    <string name="pref__input_feedback__audio_feat_key_repeated_action__label" comment="Preference title">Ton bei wiederholten Tasten</string>
    <string name="pref__input_feedback__audio_feat_gesture_swipe__label" comment="Preference title">Swipe Ton</string>
    <string name="pref__input_feedback__audio_feat_gesture_moving_swipe__label" comment="Preference title">Swipe Bewegungston</string>
    <string name="pref__input_feedback__group_haptic__label" comment="Preference group title">Haptische Rückmeldung / vibrieren</string>
    <string name="pref__input_feedback__haptic_enabled__label" comment="Preference title">Haptische Rückmeldung aktivieren</string>
    <string name="pref__input_feedback__haptic_enabled__summary_disabled" comment="Preference summary">Niemals für Eingaben vibrieren, unabhängig von den Systemeinstellungen</string>
    <string name="pref__input_feedback__haptic_vibration_mode__label" comment="Preference title">Vibrationsmodus</string>
    <string name="pref__input_feedback__haptic_vibration_duration__label" comment="Preference title">Vibrationsdauer</string>
    <string name="pref__input_feedback__haptic_vibration_strength__label" comment="Preference title">Vibrationsstärke</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_vibrator" comment="Preference summary">Diese Funktion benötigt einen Vibrationsmotor, der bei diesem Gerät anscheinend fehlt</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_amplitude_ctrl" comment="Preference summary">Diese Funktion erfordert hardwareseitige Unterstützung zur Festlegung der Stärke von Vibrationsimpulsen, die auf diesem Gerät fehlt</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_unsupported_android_version" comment="Preference summary">Diese Funktion verwendet Api Funktionen zur Steuerung der Stärke von Vibrationsimpulsen, die erst ab Android 8.0 unterstützt werden</string>
    <string name="pref__input_feedback__haptic_feat_key_press__label" comment="Preference title">Bei Tastendruck vibrieren</string>
    <string name="pref__input_feedback__haptic_feat_key_long_press__label" comment="Preference title">Bei langem Drücken einer Taste vibrieren</string>
    <string name="pref__input_feedback__haptic_feat_key_repeated_action__label" comment="Preference title">Bei Doppeltippen auf eine Taste vibrieren</string>
    <string name="pref__input_feedback__haptic_feat_gesture_swipe__label" comment="Preference title">Swype Gesten Vibration</string>
    <string name="pref__input_feedback__haptic_feat_gesture_moving_swipe__label" comment="Preference title">Bei Swype Bewegung vibrieren</string>
    <string name="pref__input_feedback__any_feat_key_press__summary" comment="Preference summary">z.B. Tasten, Schaltflächen, Emoji Tabs</string>
    <string name="pref__input_feedback__any_feat_key_long_press__summary" comment="Preference summary">z.B. Popup Menü</string>
    <string name="pref__input_feedback__any_feat_key_repeated_action__summary" comment="Preference summary">z.B. Löschen Taste</string>
    <string name="pref__input_feedback__any_feat_gesture_swipe__summary" comment="Preference summary">nicht implementiert</string>
    <string name="pref__input_feedback__any_feat_gesture_moving_swipe__summary" comment="Preference summary">z.B. Geste zum Bewegen des Cursors</string>
    <string name="settings__keyboard__title" comment="Title of Keyboard preferences screen">Tastatur</string>
    <string name="pref__keyboard__number_row__label" comment="Preference title">Zahlenreihe</string>
    <string name="pref__keyboard__number_row__summary" comment="Preference summary">Eine Zahlenreihe über dem Tastaturlayout anzeigen</string>
    <string name="pref__keyboard__hinted_number_row_mode__label" comment="Preference title">Angedeutete Zahlenreihe</string>
    <string name="pref__keyboard__hinted_symbols_mode__label" comment="Preference title">Angedeutete Symbole</string>
    <string name="pref__keyboard__utility_key_enabled__label" comment="Preference title">Utility-Taste anzeigen</string>
    <string name="pref__keyboard__utility_key_enabled__summary" comment="Preference summary">Zeigt eine konfigurierbare Utility-Taste neben der Leertaste an</string>
    <string name="pref__keyboard__utility_key_action__label" comment="Preference title">Utility-Taste Aktion</string>
    <string name="pref__keyboard__space_bar_mode__label" comment="Preference title">Leertastenbeschriftung</string>
    <string name="pref__keyboard__capitalization_behavior__label" comment="Preference title">Großschreibverhalten</string>
    <string name="pref__keyboard__font_size_multiplier__label" comment="Preference title">Schriftgröße</string>
    <string name="pref__keyboard__group_layout__label" comment="Preference group title">Layout</string>
    <string name="pref__keyboard__one_handed_mode__label" comment="Preference title">Einhandmodus</string>
    <string name="pref__keyboard__one_handed_mode_scale_factor__label" comment="Preference title">Breite der Tastatur im Einhandmodus</string>
    <string name="pref__keyboard__landscape_input_ui_mode__label" comment="Preference value">Querformat Vollbild Eingabe</string>
    <string name="pref__keyboard__height_factor__label" comment="Preference title">Tastaturhöhe</string>
    <string name="pref__keyboard__key_spacing__label" comment="Preference title">Platz zwischen Tasten</string>
    <string name="pref__keyboard__bottom_offset__label" comment="Preference title">Abstand nach unten</string>
    <string name="pref__keyboard__group_keypress__label" comment="Preference group title">Tastendruck</string>
    <string name="pref__keyboard__popup_enabled__label" comment="Preference title">Pop-Up Sichtbarkeit</string>
    <string name="pref__keyboard__popup_enabled__summary" comment="Preference summary">Pop-Up bei Tastendruck anzeigen</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__label" comment="Preference title">Akzente in Popups</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__summary" comment="Preference summary">Fügt Akzente zu Symbol-Popups des Standart Layout hinzu</string>
    <string name="pref__keyboard__long_press_delay__label" comment="Preference title">Verzögerung bei langem Tastendruck</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__label" comment="Preference title">Leertaste wechselt zu Buchstaben zurück</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__summary" comment="Preference summary">Leertaste wechselt aus der Symbol- oder Zahlenansicht zurück zur Buchstabenansicht</string>
    <string name="pref__keyboard__incognito_indicator__label" comment="Preference title">Inkognito-Indikator</string>
    <!-- Smartbar strings -->
    <string name="settings__smartbar__title" comment="Title of Smartbar screen">Schnellzugriffsleiste</string>
    <string name="pref__smartbar__enabled__label" comment="Preference title">Schnellzugriffsleiste einschalten</string>
    <string name="pref__smartbar__enabled__summary" comment="Preference summary">Wird über der Tastatur angezeigt</string>
    <string name="pref__smartbar__layout__label" comment="Preference title">Layout</string>
    <string name="pref__smartbar__group_layout_specific__label" comment="Preference group title">Aufbauspezifische Einstellungen</string>
    <string name="pref__smartbar__flip_toggles__label" comment="Preference title">Tausche Schalter für Zugriffsleisten</string>
    <string name="pref__smartbar__flip_toggles__summary" comment="Preference summary">Vertauscht die Schnellzugriffs-Leisten</string>
    <string name="pref__smartbar__shared_actions_auto_expand_collapse__label" comment="Preference title">Automatisch aus-/einklappen</string>
    <string name="pref__smartbar__shared_actions_auto_expand_collapse__summary" comment="Preference summary">Klappt die geteilte Aktionsleiste abhängig vom aktuellen Status automatisch ein/aus</string>
    <string name="pref__smartbar__extended_actions_placement__label" comment="Preference title">Platzierung der Aktionszeile</string>
    <!-- Typing strings -->
    <string name="settings__typing__title" comment="Title of Typing screen">Tippen</string>
    <string name="pref__suggestion__title" comment="Preference group title">Vorschläge</string>
    <string name="pref__suggestion__enabled__label" comment="Preference title">Vorschläge anzeigen</string>
    <string name="pref__suggestion__enabled__summary" comment="Preference summary">Bietet Vorschläge an während Sie tippen</string>
    <string name="pref__suggestion__display_mode__label" comment="Preference title">Darstellungsart für Vorschläge</string>
    <string name="pref__suggestion__block_possibly_offensive__label" comment="Preference title">Möglicherweise unangebrachte Wörter blockieren</string>
    <string name="pref__suggestion__api30_inline_suggestions_enabled__summary" comment="Preference summary">Zeige Inline-Vorschläge von Autofill-Diensten</string>
    <string name="pref__suggestion__incognito_mode__label" comment="Label of Incognito mode preference in Typing">Inkognito-Modus</string>
    <string name="pref__correction__title" comment="Preference group title">Korrekturen</string>
    <string name="pref__correction__auto_capitalization__label" comment="Preference title">Autom. Groß-/Kleinschreibung</string>
    <string name="pref__correction__auto_capitalization__summary" comment="Preference summary">Automatisches Großschreiben je nach aktuellem Kontext</string>
    <string name="pref__correction__auto_space_punctuation__label" comment="Preference title">Automatische Leerzeichen nach Punktsetzung</string>
    <string name="pref__correction__auto_space_punctuation__summary" comment="Preference summary">Automatisch ein Leerzeichen nach einem Punkt setzen</string>
    <string name="pref__correction__remember_caps_lock_state__label" comment="Preference title">Status der festgestellten Umschalttaste merken</string>
    <string name="pref__correction__remember_caps_lock_state__summary" comment="Preference summary">Die festgestellte Umschalttaste bleibt auch beim Wechsel in ein anderes Textfeld aktiviert</string>
    <string name="pref__correction__double_space_period__label" comment="Preference title">Doppeltes Leerzeichen durch Punkt ersetzen</string>
    <string name="pref__correction__double_space_period__summary" comment="Preference summary">Doppeltes Tippen auf die Leertaste fügt Punkt und ein Leerzeichen ein</string>
    <string name="pref__spelling__title" comment="Preference group title">Rechtschreibung</string>
    <string name="pref__spelling__active_spellchecker__summary_disabled">Systemweit deaktiviert. Es werden keine roten Linien in Textfeldern für falsche Wörter angezeigt. Zum Ändern antippen.</string>
    <string name="pref__spelling__active_spellchecker__summary_none">Keinen Inline-Rechtschreibprüfungs-Service eingestellt. Zum Ändern antippen.</string>
    <string name="pref__spelling__language_mode__label" comment="Label of Language mode pref">Sprachen</string>
    <string name="pref__spelling__use_contacts__label" comment="Label of Use contact list pref">Namen aus Kontakten verwenden</string>
    <string name="pref__spelling__use_contacts__summary" comment="Summary of Use contact list pref">Nach Namen in der Kontaktliste Suchen</string>
    <string name="pref__spelling__use_udm_entries__label" comment="Label of Use user dictionary entries pref">Benutzerwörterbücher verwenden</string>
    <string name="pref__spelling__use_udm_entries__summary" comment="Summary of Use user dictionary entries pref">Nach Einträgen in den Benutzerwörterbüchern suchen</string>
    <string name="settings__dictionary__title" comment="Title of the User dictionaries screen">Benutzerwörterbücher</string>
    <string name="pref__dictionary__enable_system_user_dictionary__label" comment="Preference title">System-Wörterbuch aktivieren</string>
    <string name="pref__dictionary__enable_system_user_dictionary__summary" comment="Preference summary">Wörter vorschlagen, die im System-Benutzerwörterbuch gespeichert sind</string>
    <string name="pref__dictionary__manage_system_user_dictionary__label" comment="Preference title">System-Benutzerwörterbuch verwalten</string>
    <string name="pref__dictionary__manage_system_user_dictionary__summary" comment="Preference summary">Hinzufügen, ansehen und bearbeiten von Einträgen für das System-Benutzerwörterbuch</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__label" comment="Preference title">Internes Wörterbuch aktivieren</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__summary" comment="Preference summary">Wörter vorschlagen, die im internen Benutzerwörterbuch gespeichert sind</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__label" comment="Preference title">Internes Benutzerwörterbuch verwalten</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__summary" comment="Preference summary">Hinzufügen, ansehen und bearbeiten von Einträgen für das interne Benutzerwörterbuch</string>
    <string name="settings__udm__title_floris" comment="Title of the User Dictionary Manager activity for internal">Internes Benutzerwörterbuch</string>
    <string name="settings__udm__title_system" comment="Title of the User Dictionary Manager activity for system">System-Benutzerwörterbuch</string>
    <string name="settings__udm__no_words_in_dictionary" comment="String to show if no words are present in the dictionary">Dieses Wörterbuch enthält keine Wörter.</string>
    <string name="settings__udm__word_summary_freq" comment="Summary label for a word entry. The decimal placeholder inserts the frequency for the word it summarizes.">Häufigkeit: {freq}</string>
    <string name="settings__udm__word_summary_freq_shortcut" comment="Summary label for a word entry. The first placeholder inserts the frequency for the word it summarizes, the second placeholder the shortcut defined.">Häufigkeit: {freq} | Shortcut: {shortcut}</string>
    <string name="settings__udm__all_languages" comment="Label of the For all languages entry in the language list">Für alle Sprachen</string>
    <string name="settings__udm__open_system_manager_ui" comment="Label of the Open system manager UI menu option">Öffne System Manager UI</string>
    <string name="settings__udm__dictionary_import_success" comment="Message for dictionary import success">Benutzerwörterbuch erfolgreich importiert!</string>
    <string name="settings__udm__dictionary_export_success" comment="Message for dictionary export success">Benutzerwörterbuch erfolgreich exportiert!</string>
    <string name="settings__udm__dialog__title_add" comment="Label for the title (when in adding mode) in the user dictionary add/edit dialog">Wort hinzufügen</string>
    <string name="settings__udm__dialog__title_edit" comment="Label for the title (when in editing mode) in the user dictionary add/edit dialog">Wort bearbeiten</string>
    <string name="settings__udm__dialog__word_label" comment="Label for the word in the user dictionary add/edit dialog">Wort</string>
    <string name="settings__udm__dialog__word_error_empty" comment="Error label for the word in the user dictionary add/edit dialog">Bitte ein Wort eingeben</string>
    <string name="settings__udm__dialog__word_error_invalid" comment="Error label for the word in the user dictionary add/edit dialog">Bitte ein Wort eingeben, das zu {regex} passt</string>
    <string name="settings__udm__dialog__freq_label" comment="Label for the frequency in the user dictionary add/edit dialog. The two decimal placeholders are the minimum and maximum frequency, both inclusive.">Häufigkeit (zwischen {f_min} und {f_max})</string>
    <string name="settings__udm__dialog__freq_error_empty" comment="Error label for the frequency in the user dictionary add/edit dialog">Bitte einen Häufigkeitswert eingeben</string>
    <string name="settings__udm__dialog__freq_error_invalid" comment="Error label for the frequency in the user dictionary add/edit dialog">Bitte eine gültige Zahl aus den angegebenen Grenzen eingeben</string>
    <string name="settings__udm__dialog__shortcut_label" comment="Label for the shortcut in the user dictionary add/edit dialog">Abkürzung (optional)</string>
    <string name="settings__udm__dialog__shortcut_error_invalid" comment="Error label for the shortcut in the user dictionary add/edit dialog">Bitte einen Shortcut eingeben, der zu {regex} passt</string>
    <string name="settings__udm__dialog__locale_label" comment="Label for the language code in the user dictionary add/edit dialog">Sprachcode (optional)</string>
    <string name="settings__udm__dialog__locale_error_invalid" comment="Error label for the language code in the user dictionary add/edit dialog">Dieser Sprachcode entspricht nicht der Syntax. Der Code muss entweder ein Land (wie en), Land und Region (wie en_US) oder Land, Region und Script (wie en_US-script) sein.</string>
    <string name="settings__gestures__title" comment="Title of Gestures screen">Gesten &amp; Glide Typing</string>
    <string name="pref__glide__title" comment="Preference group title">Glide Typing</string>
    <string name="pref__glide__enabled__label" comment="Preference title">Glide Typing aktivieren</string>
    <string name="pref__glide__enabled__summary" comment="Preference summary">Durch Gleiten über die Buchstaben Wort eingeben</string>
    <string name="pref__glide__show_trail__label" comment="Preference title">Zeige Bewegungsspur</string>
    <string name="pref__glide__show_trail__summary" comment="Preference summary">Wird jeweils nach einem Wort ausgeblendet</string>
    <string name="pref__glide_trail_fade_duration">Ausblendzeit der Spur</string>
    <string name="pref__glide_preview_refresh_delay">Verzögerung der Vorschau</string>
    <string name="pref__glide__show_preview">Vorschau während des Gleitens anzeigen</string>
    <string name="pref__glide__immediate_backspace_deletes_word__label">Wort immer löschen</string>
    <string name="pref__glide__immediate_backspace_deletes_word__summary">Löschtaste löscht ganzes Wort nach Glide-Typing-Eingabe</string>
    <string name="pref__gestures__general_title" comment="Preference group title">Allgemeine Gesten</string>
    <string name="pref__gestures__space_bar_title" comment="Preference group title">Leertaste Gesten</string>
    <string name="pref__gestures__other_title" comment="Preference group title">Andere Gesten / Gesten Schwellenwerte</string>
    <string name="pref__gestures__swipe_up__label" comment="Preference title">Nach oben wischen</string>
    <string name="pref__gestures__swipe_down__label" comment="Preference title">Nach unten wischen</string>
    <string name="pref__gestures__swipe_left__label" comment="Preference title">Nach links wischen</string>
    <string name="pref__gestures__swipe_right__label" comment="Preference title">Nach rechts wischen</string>
    <string name="pref__gestures__space_bar_swipe_up__label" comment="Preference title">Leertaste nach oben wischen</string>
    <string name="pref__gestures__space_bar_swipe_left__label" comment="Preference title">Leertaste nach links wischen</string>
    <string name="pref__gestures__space_bar_swipe_right__label" comment="Preference title">Leertaste nach rechts wischen</string>
    <string name="pref__gestures__space_bar_long_press__label" comment="Preference title">Leertaste lang drücken</string>
    <string name="pref__gestures__delete_key_swipe_left__label" comment="Preference title">Löschtaste nach links wischen</string>
    <string name="pref__gestures__delete_key_long_press__label" comment="Preference title">Aktion bei lang gedrückter Löschtaste</string>
    <string name="pref__gestures__swipe_velocity_threshold__label" comment="Preference title">Gesten-Geschwindigkeitsschwelle</string>
    <string name="pref__gestures__swipe_distance_threshold__label" comment="Preference title">Gesten-Distanzschwelle</string>
    <string name="settings__other__title" comment="Title of Other settings">Sonstige</string>
    <string name="pref__other__settings_theme__label" comment="Label of Settings theme preference in Other">App-Design</string>
    <string name="pref__other__settings_theme__auto_amoled" comment="Possible value of Settings theme preference in Other">Systemstandard (AMOLED)</string>
    <string name="pref__other__settings_theme__light" comment="Possible value of Settings theme preference in Other">Hell</string>
    <string name="pref__other__settings_theme__dark" comment="Possible value of Settings theme preference in Other">Dunkel</string>
    <string name="pref__other__settings_theme__amoled_dark" comment="Possible value of Settings theme preference in Other">AMOLED Dunkel</string>
    <string name="pref__other__settings_accent_color__label" comment="Label of accent color preference in Other">Akzentfarben der Einstellungen    </string>
    <string name="pref__other__settings_language__label" comment="Label of Settings language preference in Other">App-Sprache</string>
    <string name="pref__other__show_app_icon__label" comment="Label of Show app icon preference in Other">Zeige die App in der Übersicht</string>
    <string name="pref__other__show_app_icon__summary_atleast_q" comment="Summary of Show app icon preference in Other for Android 10+">Immer aktiviert in Android 10+ aufgrund von System-Beschränkungen</string>
    <!-- About UI strings -->
    <string name="about__title" comment="Title of About activity">Über</string>
    <string name="about__app_icon_content_description" comment="Content description of app icon in About">App-Icon von FlorisBoard</string>
    <string name="about__view_licenses" comment="Label of View licenses button in About">Open Source-Lizenzen</string>
    <string name="about__view_privacy_policy" comment="Label of View privacy policy button in About">Datenschutzrichtlinien</string>
    <string name="about__view_source_code" comment="Label of View source code button in About">Quellcode</string>
    <string name="about__license__title" comment="Title of Open-source licenses dialog">Open Source-Lizenzen</string>
    <string name="about__version__title" comment="Preference title">Version</string>
    <string name="about__version_copied__title" comment="Title of the toast for copying the version string">Version in die Zwischenablage kopiert</string>
    <string name="about__version_copied__error" comment="Title of the error toast for copying the version string">Etwas ist schief gelaufen: {error_message}</string>
    <string name="about__changelog__title" comment="Preference title">Änderungsprotokoll</string>
    <string name="about__changelog__summary" comment="Preference summary">Was ist neu</string>
    <string name="about__repository__title" comment="Preference title">Repository (GitHub)</string>
    <string name="about__repository__summary" comment="Preference summary">Quellcode, Diskussionen, Probleme und Info</string>
    <string name="about__privacy_policy__title" comment="Preference title">Datenschutzrichtlinie</string>
    <string name="about__privacy_policy__summary" comment="Preference summary">Datenschutzrichtlinie für dieses Projekt</string>
    <string name="about__project_license__title" comment="Preference title">Projekt-Lizenz</string>
    <string name="about__project_license__summary" comment="Preference summary">FlorisBoard ist unter {license_name} lizenziert</string>
    <string name="about__project_license__error_license_text_failed" comment="Error text for license text loading failure">Fehler: Lizenztext konnte nicht geladen werden.\n-&gt; Grund: {error_message}</string>
    <string name="about__project_license__error_reason_asset_manager_null" comment="Error text if asset manager is null">Assetmanager Referenz ist null</string>
    <string name="about__third_party_licenses__title" comment="Preference title">Drittanbieter-Lizenzen</string>
    <string name="about__third_party_licenses__summary" comment="Preference summary">Lizenzen der Drittanbieter-Bibliotheken, die FlorisBoard verwendet</string>
    <!-- Setup UI strings -->
    <string name="setup__title" comment="Title of Setup">Herzlich willkommen!</string>
    <string name="setup__intro_message" comment="Short intro message welcoming new users">Vielen Dank für die Nutzung von {app_name}! Dieses Quick Setup leitet dich durch die erforderlichen Schritte {app_name} auf deinem Gerät zu nutzen.</string>
    <string name="setup__footer__privacy_policy" comment="Privacy policy label for URL">Datenschutzrichtlinie</string>
    <string name="setup__footer__repository" comment="Repository label for URL">Quellcode</string>
    <string name="setup__enable_ime__title">{app_name} aktivieren</string>
    <string name="setup__enable_ime__description">Android erfordert, dass jede nachinstallierte Tastatur vor der Nutzung aktiviert (eingeschaltet) wird. Öffne <i> Sprachen und Eingabe</i> in den Systemeinstellungen und aktiviere dort {app_name}.</string>
    <string name="setup__enable_ime__open_settings_btn">Systemeinstellungen öffnen</string>
    <string name="setup__select_ime__title">{app_name} auswählen</string>
    <string name="setup__select_ime__description">{app_name} ist nun auf deinem System aktiviert. Um es zu benutzen, wähle bei der Standard-Eingabemethode {app_name} aus!</string>
    <string name="setup__select_ime__switch_keyboard_btn">Tastatur wechseln</string>
    <string name="setup__grant_notification_permission__title">Fehlerberichts-Benachrichtigungen zulassen</string>
    <string name="setup__grant_notification_permission__description">Ab Android 13+ müssen Apps um Erlaubnis bitten, um         Benachrichtigungen zu senden. FlorisBoard verwendet diese nur, um im Falle eines App-Absturzes einen Fehlerbericht zu senden.         Diese Berechtigung kann jederzeit in den Systemeinstellungen widerrufen werden.    </string>
    <string name="setup__grant_notification_permission__btn">Berechtigung erteilen</string>
    <string name="setup__finish_up__title">Fertigstellen</string>
    <string name="setup__finish_up__description_p1">{app_name} ist nun im System aktiviert und bereit von dir angepasst zu werden.</string>
    <string name="setup__finish_up__description_p2">Falls dir irgendwelche Probleme, Bugs oder Abstürze begegnen, oder du einfach einen Vorschlag machen möchtest, besuche einfach die GitHub Seite des Projekts (zu finden im About \"Über die App\" Screen)!</string>
    <string name="setup__finish_up__finish_btn">Anpassung beginnen</string>
    <!-- Back up & Restore -->
    <string name="backup_and_restore__title">Sichern &amp; Wiederherstellen</string>
    <string name="backup_and_restore__back_up__title">Daten sichern</string>
    <string name="backup_and_restore__back_up__summary">Erstelle ein Backup der Einstellungen und Personalisierungen</string>
    <string name="backup_and_restore__back_up__destination">Wähle den Speicherort der Sicherung</string>
    <string name="backup_and_restore__back_up__destination_file_sys">Lokales Dateisystem</string>
    <string name="backup_and_restore__back_up__destination_share_intent">Drittanbieter-Apps über das Teilen-Menü</string>
    <string name="backup_and_restore__back_up__files">Auswählen, wovon eine Sicherung erstellt werden soll</string>
    <string name="backup_and_restore__back_up__files_jetpref_datastore">Einstellungen</string>
    <string name="backup_and_restore__back_up__files_ime_keyboard">Tastaturerweiterungen</string>
    <string name="backup_and_restore__back_up__files_ime_spelling">Rechtschreibeerweiterungen / Wörterbücher</string>
    <string name="backup_and_restore__back_up__files_ime_theme">Design-Erweiterungen</string>
    <string name="backup_and_restore__back_up__files_clipboard_history">Zwischenablageverlauf</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_text_items">Text Inhalte</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_image_items">Bilder</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_video_items">Videos</string>
    <string name="backup_and_restore__back_up__success">Sicherung wurde erfolgreich exportiert!</string>
    <string name="backup_and_restore__back_up__failure">Exportieren des Sicherungsarchivs fehlgeschlagen: {error_message}</string>
    <string name="backup_and_restore__restore__title">Daten wiederherstellen</string>
    <string name="backup_and_restore__restore__summary">Einstellungen und Personalisierungen aus einer Sicherung wiederherstellen</string>
    <string name="backup_and_restore__restore__files">Wähle aus, was wiederhergestellt werden soll</string>
    <string name="backup_and_restore__restore__metadata">Ausgewählte Sicherungsarchive</string>
    <string name="backup_and_restore__restore__metadata_warn_different_version">Dieses Sicherungsarchiv wurde in einer anderen als der aktuellen Version erstellt, was grundsätzlich unterstützt wird. Beachte jedoch, dass kleinere Probleme auftreten können oder einige Einstellungen aufgrund von Funktionsunterschieden möglicherweise nicht richtig übertragen werden.</string>
    <string name="backup_and_restore__restore__metadata_warn_different_vendor">Dieses Sicherungsarchiv wurde in einer Drittanbieter-App erstellt, was grundsätzlich nicht unterstützt wird. Es kann zu Datenverlust kommen, Wiederherstellung auf eigene Gefahr!</string>
    <string name="backup_and_restore__restore__metadata_error_invalid_metadata">Dieses Sicherungsarchiv enthält ungültige Metadaten. Entweder ist die Datei beschädigt oder sie wurde falsch modifiziert. Das Wiederherstellen dieser Datei ist nicht möglich, bitte eine andere wählen.</string>
    <string name="backup_and_restore__restore__metadata_error_nothing_to_restore">Dieses Sicherungsarchiv enthält keine wiederherstellbaren Dateien, bitte ein anderes auswählen.</string>
    <string name="backup_and_restore__restore__mode">Wiederherstellungsmodus</string>
    <string name="backup_and_restore__restore__mode_merge">Mit aktuellen Daten zusammenführen</string>
    <string name="backup_and_restore__restore__mode_erase_and_overwrite">Aktuelle Daten löschen und überschreiben</string>
    <string name="backup_and_restore__restore__success">Daten erfolgreich wiederhergestellt!</string>
    <string name="backup_and_restore__restore__failure">Wiederherstellung der Daten fehlgeschlagen: {error_message}</string>
    <!-- Crash Dialog strings -->
    <string name="crash_dialog__title" comment="Title of crash dialog">FlorisBoard Fehlermeldung</string>
    <string name="crash_dialog__description" comment="Description of crash dialog">Entschuldigung für die Unannehmlichkeiten, aber FlorisBoard ist aufgrund eines unerwarteten Fehlers abgestürzt.</string>
    <string name="crash_dialog__report_instructions" comment="Issue tracker report instructions for the crash dialog. The %s placeholder is the name of the crash report template and always in English/LTR.">Wenn du diesen Fehler melden möchtest, prüfe zuerst im Issue Tracker auf Github, ob der Absturz bereits gemeldet worden ist.\nFalls nicht, kopiere den erzeugten Absturzbericht und erstelle ein neues Problem. Benutze die \"%s\"-Vorlage, gib die Beschreibung und die Schritte zum Reproduzieren des Fehlers an und füge am Ende den erzeugten Absturzbericht hinzu. Dies hilft, FlorisBoard für alle besser und stabiler zu machen. Vielen Dank!</string>
    <string name="crash_dialog__copy_to_clipboard" comment="Label of Copy to clipboard button in crash dialog">In die System-Zwischenablage kopieren</string>
    <string name="crash_dialog__copy_to_clipboard_success" comment="Label of Copy to clipboard success message in crash dialog">In die System-Zwischenablage kopiert</string>
    <string name="crash_dialog__copy_to_clipboard_failure" comment="Label of Copy to clipboard failure message in crash dialog">Kopieren zur System-Zwischenablage nicht möglich: Zwischenablagen-Manager-Instanz nicht gefunden</string>
    <string name="crash_dialog__open_issue_tracker" comment="Label of Open issue tracker button in crash dialog">Öffne \"Issue Tracker\" (github.com)</string>
    <string name="crash_dialog__close" comment="Label of Close button in crash dialog">Schließen</string>
    <string name="crash_notification_channel__title" comment="Title of crash notification channel">FlorisBoard Fehlermeldungen</string>
    <string name="crash_once_notification__title" comment="Title of the notification for a single crash">FlorisBoard funktioniert nicht mehr …</string>
    <string name="crash_once_notification__body" comment="Body of the notification for a single crash">Tippen, um Details anzuzeigen</string>
    <string name="crash_multiple_notification__title" comment="Title of the notification for consecutive crashes">FlorisBoard funktioniert zum wiederholten Male nicht …</string>
    <string name="crash_multiple_notification__body" comment="Body of the notification for consecutive crashes">Um eine endlose Absturzschleife zu verhindern, wurde automatisch auf die zuvor benutzte Tastatur zurückgegriffen. Tippen, um die Fehlermeldung anzuzeigen</string>
    <!-- Clipboard strings -->
    <string name="clipboard__header_title">Zwischenablage</string>
    <string name="clipboard__disabled__title">Der Verlauf der Zwischenablage ist aktuell deaktiviert</string>
    <string name="clipboard__disabled__message">{app_name}\'s Zwischenablageverlauf ermöglicht es dir schnell kopierte Texte und Bilder zu speichern und abzurufen, mit der Möglichkeit, Elemente anzuheften, eine automatische Bereinigung einzurichten und eine maximale Anzahl von Elementen festzulegen.</string>
    <string name="clipboard__disabled__enable_button">Verlauf für Zwischenablage aktivieren</string>
    <string name="clipboard__empty__title">Deine Zwischenablage ist leer</string>
    <string name="clipboard__empty__message">Sobald du Texte oder Bilder kopierst, werden sie hier angezeigt.</string>
    <string name="clipboard__locked__title">Deine Zwischenablage ist gesperrt</string>
    <string name="clipboard__locked__message">Um auf deinen Zwischenablageverlauf zuzugreifen, entsperre zuerst dein Gerät.</string>
    <string name="clipboard__group_pinned">Angeheftet</string>
    <string name="clipboard__group_recent">Aktuell</string>
    <string name="clipboard__group_other">Andere</string>
    <string name="clipboard__item_description_email">E-Mail</string>
    <string name="clipboard__item_description_url">URL</string>
    <string name="clipboard__item_description_phone">Telefon</string>
    <string name="clip__clear_history">Verlauf leeren</string>
    <string name="clip__unpin_item">Element lösen</string>
    <string name="clip__pin_item">Element anheften</string>
    <string name="clip__delete_item">Löschen</string>
    <string name="clip__paste_item">Einfügen</string>
    <string name="clip__back_to_text_input">Zurück zur Texteingabe</string>
    <string name="clip__cant_paste">Diese App erlaubt kein Einfügen aus der Zwischenablage.</string>
    <string name="clipboard__cleared_primary_clip">Aktives Element gelöscht</string>
    <string name="clipboard__cleared_history">Verlauf gelöscht</string>
    <string name="clipboard__cleared_full_history">Kompletten Verlauf gelöscht</string>
    <string name="clipboard__confirm_clear_history__message">Soll der Zwischenablagenverlauf wirklich gelöscht werden?</string>
    <string name="settings__clipboard__title">Zwischenablage</string>
    <string name="pref__clipboard__use_internal_clipboard__label">Interne Zwischenablage nutzen</string>
    <string name="pref__clipboard__use_internal_clipboard__summary">Interne Zwischenablage anstatt der Systemzwischenablage verwenden</string>
    <string name="pref__clipboard__sync_from_system_clipboard__label">Von Systemzwischenablage synchronisieren</string>
    <string name="pref__clipboard__sync_from_system_clipboard__summary">Änderungen in der Systemzwischenablage aktualisieren auch die interne Zwischenablage</string>
    <string name="pref__clipboard__sync_to_system_clipboard__label">Zur Systemzwischenablage synchronisieren</string>
    <string name="pref__clipboard__sync_to_system_clipboard__summary">Aktualisierungen der Floris Zwischenablage aktualisieren auch die Systemzwischenablage</string>
    <string name="pref__clipboard__group_clipboard_suggestion__label">Vorschläge aus der Zwischenablage</string>
    <string name="pref__clipboard__suggestion_enabled__label" comment="Preference title">Vorschläge aus der Zwischenablage</string>
    <string name="pref__clipboard__suggestion_enabled__summary" comment="Preference summary">Zuvor kopierte Inhalte vorschlagen</string>
    <string name="pref__clipboard__suggestion_timeout__label" comment="Preference title; Translators: This should form a sentence together with `pref__clipboard__suggestion_timeout__summary` and is the first part">Beschränkung für die Vorschläge der Zwischenablage</string>
    <string name="pref__clipboard__suggestion_timeout__summary" comment="Preference summary; Translators: This should form a sentence together with `pref__clipboard__suggestion_timeout__label` and is the second part">Elemente, kopiert in den letzten {v} s</string>
    <string name="pref__clipboard__group_clipboard_history__label">Zwischenablageverlauf</string>
    <string name="pref__clipboard__enable_clipboard_history__label">Verlauf für Zwischenablage aktivieren</string>
    <string name="pref__clipboard__enable_clipboard_history__summary">Speichere Elemente der Zwischenablage für schnellen Zugriff</string>
    <string name="pref__clipboard__clean_up_old__label">Alte Elemente bereinigen</string>
    <string name="pref__clipboard__clean_up_after__label">Alte Elemente bereinigen nach</string>
    <string name="pref__clipboard__auto_clean_sensitive__label">Lösche Zwischenablageinhalte automatisch</string>
    <string name="pref__clipboard__auto_clean_sensitive_after__label">Lösche Zwischenablageinhalte automatisch nach</string>
    <string name="pref__clipboard__limit_history_size__label">Verlaufsgröße beschränken</string>
    <string name="pref__clipboard__max_history_size__label">Maximale Verlaufsgröße</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__label">Das Löschen des aktiven Elements betrifft den Verlauf</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__summary">Durch das Löschen des aktiven Elements wird auch das neueste Verlaufselement entfernt</string>
    <string name="send_to_clipboard__unknown_error">Ein unbekannter Fehler ist aufgetreten. Bitte versuche es erneut!</string>
    <string name="send_to_clipboard__type_not_supported_error">Dieser Media-Typ wird nicht unterstützt.</string>
    <string name="send_to_clipboard__android_version_to_old_error">Die Androidversion ist zu alt für diese Funktion.    </string>
    <string name="send_to_clipboard__description__copied_image_to_clipboard">Nachfolgendes Bild wurde in die Zwischenablage kopiert.</string>
    <!-- Devtools strings -->
    <string name="devtools__title" comment="Title of Devtools screen. Translators: treat this string as 'Developer tools' for translation, except a similar short term is available for your language.">Entwicklerwerkzeuge</string>
    <string name="devtools__enabled__label" comment="Label of Enable developer tools in Devtools">Entwickler-Werkzeuge ein/aus</string>
    <string name="devtools__enabled__summary" comment="Summary of Enable developer tools in Devtools">Speziell für Debugging und Fehlersuche entwickelte Werkzeuge</string>
    <string name="devtools__show_primary_clip__label" comment="Label of Show primary clip in Devtools">Zeige aktives Element</string>
    <string name="devtools__show_primary_clip__summary" comment="Summary of Show primary clip in Devtools">Zeigt das aktuell aktive Element der Zwischenablage</string>
    <string name="devtools__show_input_state_overlay__label" comment="Label of Show input cache overlay in Devtools">Zeige Eingabe-Overlay an</string>
    <string name="devtools__show_input_state_overlay__summary" comment="Summary of Show input cache overlay in Devtools">Blendet Eingabe-Overlay zur Fehlersuche ein</string>
    <string name="devtools__show_spelling_overlay__label" comment="Label of Show spelling overlay in Devtools">Zeige Rechtschreibprüfungs-Overlay</string>
    <string name="devtools__show_spelling_overlay__summary" comment="Summary of Show spelling overlay in Devtools">Blendet die aktuellen Ergebnisse der Rechtschreibprüfung fürs Debugging ein</string>
    <string name="devtools__show_inline_autofill_overlay__label">Zeige zeileneingeschobene automatisch einfügende überlage</string>
    <string name="devtools__show_inline_autofill_overlay__summary">Blendet das aktuelle Autofill-Ergebnis für das Debugging ein</string>
    <string name="devtools__show_key_touch_boundaries__label" comment="Label of Show key touch boundaries in Devtools">Tastendruck-Umrandungen einschalten</string>
    <string name="devtools__show_key_touch_boundaries__summary" comment="Summary of Show key touch boundaries in Devtools">Umrandet die gedrückte Taste in Rot</string>
    <string name="devtools__show_drag_and_drop_helpers__label" comment="Label of Show drag and drop helpers in Devtools">Zeige Verschiebe-Hilfen</string>
    <string name="devtools__show_drag_and_drop_helpers__summary" comment="Summary of Show drag and drop helpers in Devtools">Zeige andernfalls unsichtbare Hilfen in Verschiebe-Bildschirmen für Fehlerfindungen</string>
    <string name="devtools__clear_udm_internal_database__label" comment="Label of Clear internal user dictionary database in Devtools">Interne Benutzerwörterbuch-Datenbank leeren</string>
    <string name="devtools__clear_udm_internal_database__summary" comment="Summary of Clear internal user dictionary database in Devtools">Leert alle Wörter von der Wörterbuch-Datenbank</string>
    <string name="devtools__reset_quick_actions_to_default__label">Setzte den Smartbar Schnellzugriff zurück</string>
    <string name="devtools__reset_quick_actions_to_default__summary">Setzte den Smartbar Schnellzugriff auf Normalzustand zurück</string>
    <string name="devtools__reset_quick_actions_to_default__toast_success">Zurücksetzen der Smartbar Schnellzugriff auf Normalzustand war Erfolgreich</string>
    <string name="devtools__reset_flag__label" comment="Label of Reset flag preferences in Devtools">\"{flag_name}\"-Einstellung zurücksetzen</string>
    <string name="devtools__reset_flag_is_ime_set_up__summary" comment="Summary of Reset is IME set up flag in Devtools">Debug Aktion: Setup Bildschirm erneut anzeigen</string>
    <string name="devtools__test_crash_report__label" comment="Label of Test Crash Report in Devtools">Ansicht des Absturzberichts testen</string>
    <string name="devtools__test_crash_report__summary" comment="Summary of Test Crash Report in Devtools">Debug-Aktion, um absichtlich einen Absturz zu verursachen</string>
    <string name="devtools__group_android__title" comment="Title of Android group in Devtools">Android-Systemwerkzeuge</string>
    <string name="devtools__android_settings_global__title" comment="Title of Android settings (global) screen">Globale Android-Einstellungen</string>
    <string name="devtools__android_settings_secure__title" comment="Title of Android settings (secure) screen">Sichere Android-Einstellungen</string>
    <string name="devtools__android_settings_system__title" comment="Title of Android settings (system) screen">Android System-Einstellungen</string>
    <string name="devtools__android_locales__title" comment="Title of Android locales screen">Systemgebietsschemata</string>
    <string name="devtools__debuglog__title">Debug Log</string>
    <string name="devtools__debuglog__copied_to_clipboard">Debug Log wurde in die Zwischenablage kopiert</string>
    <string name="devtools__debuglog__copy_log">Log kopieren</string>
    <string name="devtools__debuglog__copy_for_github">Log kopieren (GitHub Formatierung)</string>
    <string name="devtools__debuglog__loading">Lädt…</string>
    <!-- Extension strings -->
    <string name="ext__home__title">Add-ons &amp; Erweiterungen</string>
    <string name="ext__list__ext_theme">Design-Erweiterungen</string>
    <string name="ext__list__ext_keyboard">Tastatur-Erweiterungen</string>
    <string name="ext__list__ext_languagepack">Sprachpaket-Erweiterungen</string>
    <string name="ext__meta__authors">Ersteller</string>
    <string name="ext__meta__components">Gebündelte Komponenten</string>
    <string name="ext__meta__components_theme">Enthaltene Designs</string>
    <string name="ext__meta__components_language_pack">Gebündelte Sprachpakete</string>
    <string name="ext__meta__components_none_found">Dieses Erweiterungsarchiv enthält keine gebündelten Komponenten.</string>
    <string name="ext__meta__description">Beschreibung</string>
    <string name="ext__meta__homepage">Startseite</string>
    <string name="ext__meta__id">ID</string>
    <string name="ext__meta__issue_tracker">Ticket System</string>
    <string name="ext__meta__keywords">Stichwörter</string>
    <string name="ext__meta__label">Bezeichnung</string>
    <string name="ext__meta__license">Lizenz</string>
    <string name="ext__meta__maintainers">Betreuer</string>
    <string name="ext__meta__maintainers_by">Von: {maintainers}</string>
    <string name="ext__meta__title">Titel</string>
    <string name="ext__meta__version">Version</string>
    <string name="ext__error__not_found_title">Erweiterung nicht gefunden</string>
    <string name="ext__error__not_found_description">Es konnte keine Erweiterung mit der ID \"{id}\" gefunden werden.</string>
    <string name="ext__editor__title_create_any">Erweiterung erstellen</string>
    <string name="ext__editor__title_create_keyboard">Tastaturerweiterung erstellen</string>
    <string name="ext__editor__title_create_theme">Designerweiterung erstellen</string>
    <string name="ext__editor__title_edit_any">Erweiterung bearbeiten</string>
    <string name="ext__editor__title_edit_keyboard">Tastaturerweiterung bearbeiten</string>
    <string name="ext__editor__title_edit_theme">Designerweiterung bearbeiten</string>
    <string name="ext__editor__metadata__title">Metadaten verwalten</string>
    <string name="ext__editor__metadata__title_invalid">Ungültige Metadaten</string>
    <string name="ext__editor__metadata__message_invalid">Die Meta-Daten für diese Erweiterunf ist nicht gültig, bitte den Meta-Daten-Editor für Details überprüfen!</string>
    <string name="ext__editor__dependencies__title">Abhängigkeiten verwalten</string>
    <string name="ext__editor__files__title">Archivdateien verwalten</string>
    <string name="ext__editor__files__type_fonts">Schriftarten</string>
    <string name="ext__editor__files__type_images">Bilder</string>
    <string name="ext__editor__create_component__title">Komponente erstellen</string>
    <string name="ext__editor__create_component__title_theme">Design erstellen</string>
    <string name="ext__editor__create_component__from_empty">Leer</string>
    <string name="ext__editor__create_component__from_existing">Aus bestehendem</string>
    <string name="ext__editor__create_component__from_empty_warning">Das Erstellen und Konfigurieren einer leeren Komponente kann schwer sein, wenn Sie mit {app_name} oder den Genauigkeiten unvertraut sind. Ziehen Sie es dann in Betracht eine existierende Komponente zu kopieren und diese zu modifizieren.</string>
    <string name="ext__editor__edit_component__title">Komponente bearbeiten</string>
    <string name="ext__editor__edit_component__title_theme">Designkomponente bearbeiten</string>
    <string name="ext__export__success">Erweiterung erfolgreich exportiert!</string>
    <string name="ext__export__failure">Erweiterung exportieren fehlgeschlagen: {error_message}</string>
    <string name="ext__import__success">Erweiterung erfolgreich importiert!</string>
    <string name="ext__import__failure">Erweiterung importieren fehlgeschlagen: {error_message}</string>
    <string name="ext__import__ext_any" comment="Title of Importer screen for import of any supported FlorisBoard extension">Importiere Erweiterung</string>
    <string name="ext__import__ext_keyboard" comment="Title of Importer screen for keyboard extension import">Importiere Tastatur-Erweiterung</string>
    <string name="ext__import__ext_theme" comment="Title of Importer screen for theme extension import">Importiere Design-Erweiterung</string>
    <string name="ext__import__ext_languagepack" comment="Title of Importer screen for language pack extension import">Importiere Sprachpaket-Erweiterung</string>
    <string name="ext__import__file_skip" comment="Label when a file cannot be imported in the current context. The actual reason string is in a separate text view below this string.">Datei kann nicht importiert werden. Grund:</string>
    <string name="ext__import__file_skip_unsupported" comment="Reason string when file is unsupported">Nicht unterstützter oder nicht erkannter Datei-Typ.</string>
    <string name="ext__import__file_skip_ext_core" comment="Reason string when ext has core extension ID">Das Standarderweiterungspaket, das mit der App mitgeliefert wird, konnte nicht ersetzt oder aktualisiert werden. Aktualisiere die App, wenn du eine neuere Version des Standarderweiterungspakets nutzen möchtest.</string>
    <string name="ext__import__file_skip_ext_corrupted" comment="Reason string when file seems to be ext but is corrupted">Die Datei scheint ein Erweiterungsarchiv zu sein, aber das Parsen der Archivdaten ist fehlgeschlagen. Entweder ist das Archiv beschädigt oder diese Datei ist gar keine Erweiterung.</string>
    <string name="ext__import__file_skip_ext_incorrect_type" comment="Reason string when file is of incorrect extension serial type">Ein Erweiterungsarchiv des seriellen Typs \"{expected_serial_type}\" wurde erwartet, aber \"{actual_serial_type}\" wurde gefunden.</string>
    <string name="ext__import__file_skip_ext_not_supported" comment="Reason string when file is loaded in incorrect context">Eine Mediendatei (Bild, Audio, Text, etc.) wurde erwartet, aber ein Erweiterungsarchiv wurde gefunden.</string>
    <string name="ext__import__file_skip_media_not_supported" comment="Reason string when file is loaded in incorrect context">Ein Erweiterungsarchiv wurde erwartet, aber eine Mediendatei (Bild, Audio, Text, etc.) wurde gefunden.</string>
    <string name="ext__import__error_unexpected_exception" comment="Label when an error occurred during import. The error message will be appended below this text view">Beim Import ist ein unerwarteter Fehler aufgetreten. Folgende Angaben wurden gemacht:</string>
    <string name="ext__validation__enter_package_name">Bitte gib einen Paketnamen ein</string>
    <string name="ext__validation__error_package_name">Paketname stimmt nicht mit Regex {id_regex} überein</string>
    <string name="ext__validation__enter_version">Bitte gib eine Version an</string>
    <string name="ext__validation__enter_title">Bitte gib einen Titel ein</string>
    <string name="ext__validation__enter_maintainer">Bitte gib mindestens einen gültigen Maintainer an</string>
    <string name="ext__validation__enter_license">Bitte gib eine Lizenzbezeichnung an</string>
    <string name="ext__validation__enter_component_id">Bitte gib eine Komponenten-ID ein</string>
    <string name="ext__validation__error_component_id">Bitte eine Komponenten-ID eingeben, welche zu {component_id_regex} passt</string>
    <string name="ext__validation__enter_component_label">Bitte gib eine Komponentenbezeichnung ein</string>
    <string name="ext__validation__hint_component_label_to_long">Die Komponentenbezeichnung ist recht lang, was zu einem Abschneiden auf der Benutzeroberfläche führen kann</string>
    <string name="ext__validation__error_author">Bitte gib mindestens einen gültigen Autor an</string>
    <string name="ext__validation__error_stylesheet_path_blank">Der Stylesheet-Pfad darf nicht leer sein</string>
    <string name="ext__validation__error_stylesheet_path">Bitte einen Stylesheet-Pfad eingeben, welcher zu {stylesheet_path_regex} passt</string>
    <string name="ext__validation__enter_property">Bitte gib einen Variablennamen an</string>
    <string name="ext__validation__error_property">Bitte einen gültigen Variablennamen eingeben, welcher zu {variable_name_regex} passt</string>
    <string name="ext__validation__enter_color">Bitte gib einen Farbstring ein</string>
    <string name="ext__validation__error_color">Bitte gib einen gültigen Farbstring ein</string>
    <string name="ext__validation__enter_dp_size">Bitte gib eine dp-Größe an</string>
    <string name="ext__validation__enter_valid_number">Bitte gib eine gültige Nummer ein</string>
    <string name="ext__validation__enter_positive_number">Bitte gib eine positive Zahl ein (&gt;=0)</string>
    <string name="ext__validation__enter_percent_size">Bitte gib eine Prozentzahl an</string>
    <string name="ext__validation__enter_number_between_0_100">Bitte gib eine positive Zahl zwischen 0 und 100 an</string>
    <string name="ext__validation__hint_value_above_50_percent">Jeder Wert über 50 % verhält sich so, als ob 50 % eingestellt wären. Überlege den Wert zu senken</string>
    <string name="ext__update_box__internet_permission_hint">Weil diese App keine Internetberechtigungen hat, muss manuell nach Updates für Erweiterungen gesucht werden.</string>
    <string name="ext__update_box__search_for_updates">Nach Updates suchen</string>
    <string name="ext__addon_management_box__managing_placeholder">Verwalte {extensions}</string>
    <string name="ext__addon_management_box__addon_manager_info">Alle Aufgaben im Zusammenhang mit dem Importieren, Exportieren, Erstellen, Anpassen und Entfernen von Erweiterungen können über den Add-on-Manager abgewickelt werden.</string>
    <string name="ext__addon_management_box__go_to_page">Gehe zu {ext_home_title}</string>
    <string name="ext__home__info">Erweiterungen können über den FlorisBoard Add-on Store heruntergeladen und installiert werden oder importiere beliebige Erweiterungen aus dem Internet.</string>
    <string name="ext__home__visit_store">Besuche den Add-ons Store</string>
    <string name="ext__home__manage_extensions">Installierte Erweiterungen verwalten</string>
    <string name="ext__list__view_details">Details anzeigen</string>
    <string name="ext__check_updates__title">Auf Updates prüfen</string>
    <!-- Action strings -->
    <string name="action__add">Hinzufügen</string>
    <string name="action__apply">Übernehmen</string>
    <string name="action__back_up">Sicherung erstellen</string>
    <string name="action__cancel">Abbrechen</string>
    <string name="action__create">Erstellen</string>
    <string name="action__default">Standard</string>
    <string name="action__delete">Entfernen</string>
    <string name="action__delete_confirm_title">Löschvorgang bestätigen</string>
    <string name="action__delete_confirm_message">Bist du sicher, dass du \"{name}\" löschen möchtest? Dieser Vorgang kann nicht rückgängig gemacht werden.</string>
    <string name="action__reset_confirm_title">Zurücksetzung bestätigen</string>
    <string name="action__reset_confirm_message">Bist du sicher, dass du \"{name}\" zurücksetzen willst? Dies kann nicht rückgängig gemacht werden.</string>
    <string name="action__discard">Verwerfen</string>
    <string name="action__discard_confirm_title">Nicht gespeicherte Änderungen</string>
    <string name="action__discard_confirm_message">Bist du sicher, dass du deine ungespeicherten Änderungen verwerfen möchtest? Diese Aktion kann nicht rückgängig gemacht werden.</string>
    <string name="action__edit">Bearbeiten</string>
    <string name="action__export">Exportieren</string>
    <string name="action__export_file">Datei importieren</string>
    <string name="action__export_files">Dateien importieren</string>
    <string name="action__import">Importieren</string>
    <string name="action__import_file">Datei importieren</string>
    <string name="action__import_files">Dateien importieren</string>
    <string name="action__no">Nein</string>
    <string name="action__ok">Okay</string>
    <string name="action__restore">Wiederherstellen</string>
    <string name="action__save">Speichern</string>
    <string name="action__select">Auswählen</string>
    <string name="action__select_dir">Verzeichnis auswählen</string>
    <string name="action__select_dirs">Verzeichnisse auswählen</string>
    <string name="action__select_file">Datei auswählen</string>
    <string name="action__select_files">Dateien auswählen</string>
    <string name="action__yes">Ja</string>
    <!-- Error strings (generic) -->
    <string name="error__title">Fehler</string>
    <string name="error__details">Details</string>
    <string name="error__invalid">Ungültig</string>
    <string name="error__snackbar_message">Etwas ist schief gelaufen</string>
    <string name="error__snackbar_message_template">Etwas ist schief gelaufen: {error_message}</string>
    <!-- General strings -->
    <string name="general__example_given" comment="This string is used where an example will be inserted in the {example} block. When translating, use the abbreviation used in your language for 'example given'.">z. B. {example}</string>
    <string name="general__no_browser_app_found_for_url">Keine Browseranwendung für die URL {url} gefunden</string>
    <string name="general__select_dropdown_value_placeholder">&#45; wählen &#45;</string>
    <string name="general__unlimited">Unbegrenzt</string>
    <string name="general__file_name">Dateiname</string>
    <string name="general__properties">Eigenschaften</string>
    <!-- Screen orientation strings -->
    <string name="screen_orientation__portrait">Hochformat</string>
    <string name="screen_orientation__landscape">Querformat</string>
    <string name="screen_orientation__vertical">Vertikal</string>
    <string name="screen_orientation__horizontal">Horizontal</string>
    <!-- State strings -->
    <string name="state__disabled">Deaktiviert</string>
    <string name="state__enabled">Aktiviert</string>
    <string name="state__no_dir_selected">Kein Verzeichnis ausgewählt</string>
    <string name="state__no_dirs_selected">Keine Verzeichnisse ausgewählt</string>
    <string name="state__no_file_selected">Keine Datei ausgewählt</string>
    <string name="state__no_files_selected">Keine Dateien ausgewählt</string>
    <!-- Enum label and description strings -->
    <string name="enum__candidates_display_mode__classic" comment="Enum value label">Klassisch (3 Spalten)</string>
    <string name="enum__candidates_display_mode__dynamic" comment="Enum value label">Dynamische Breite</string>
    <string name="enum__candidates_display_mode__dynamic_scrollable" comment="Enum value label">Dynamische Breite &amp; scrollbar</string>
    <string name="enum__capitalization_behavior__capslock_by_double_tap" comment="Enum value label">Aktiviere die Feststelltaste durch Doppeltippen von Shift</string>
    <string name="enum__capitalization_behavior__capslock_by_cycle" comment="Enum value label">Wechsel mit jedem Drücken der Shift-Taste zur nächsten Großschreibstufe</string>
    <string name="enum__color_representation__hex" comment="Enum value label">Hexadezimal</string>
    <string name="enum__color_representation__rgb" comment="Enum value label">Rot Grün Blau</string>
    <string name="enum__color_representation__hsv" comment="Enum value label">Farbton Sättigung Wert</string>
    <string name="enum__display_kbd_after_dialogs__always" comment="Enum value label">Immer anzeigen</string>
    <string name="enum__display_kbd_after_dialogs__always__description" comment="Enum value description">Immer die Tastatur nach Schließen eines Editordialogs anzeigen</string>
    <string name="enum__display_kbd_after_dialogs__never" comment="Enum value label">Nie anzeigen</string>
    <string name="enum__display_kbd_after_dialogs__never__description" comment="Enum value description">Nie die Tastatur nach Schließen eines Editordialogs anzeigen</string>
    <string name="enum__display_kbd_after_dialogs__remember" comment="Enum value label">Letzten Zustand merken</string>
    <string name="enum__display_kbd_after_dialogs__remember__description" comment="Enum value description">Nur die Tastatur nach Schließen eines Editordialogs anzeigen, wenn sie vorher sichtbar war</string>
    <string name="enum__display_language_names_in__system_locale" comment="Enum value label">Systemsprache</string>
    <string name="enum__display_language_names_in__system_locale__description" comment="Enum value description">Sprachnamen in der App und in der Tastatur werden in der Systemsprache des Geräts dargestellt</string>
    <string name="enum__display_language_names_in__native_locale" comment="Enum value label">Referenzierte Sprache</string>
    <string name="enum__display_language_names_in__native_locale__description" comment="Enum value description">Sprachnamen in der App und in der Tastatur werden in der referenzierten Sprache dargestellt</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_prepend" comment="Enum value label">Automatisch Sortieren (Voranstellen)</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_prepend__description" comment="Enum value description">Emojis werden bei Verwendung neu sortiert. Neue Emojis werden am Anfang hinzugefügt.</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_append" comment="Enum value label">Automatisch Sortieren (Anhängen)</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_append__description" comment="Enum value description">Emojis werden bei Verwendung neu sortiert. Neue Emojis werden am Ende hinzugefügt.</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_prepend" comment="Enum value label">Manuelles Sortieren (Voranstellen)</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_prepend__description" comment="Enum value description">Emojis werden bei Verwendung nicht neu sortiert. Neue Emojis werden am Anfang hinzugefügt.</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_append" comment="Enum value label">Manuelles Sortieren (Anhängen)</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_append__description" comment="Enum value description">Emojis werden bei Verwendung nicht neu sortiert. Neue Emojis werden am Ende hinzugefügt.</string>
    <string name="enum__emoji_skin_tone__default" comment="Enum value label">{emoji} Standard Hautfarbe</string>
    <string name="enum__emoji_skin_tone__light_skin_tone" comment="Enum value label">{emoji} Helle Hautfarbe</string>
    <string name="enum__emoji_skin_tone__medium_light_skin_tone" comment="Enum value label">{emoji} Mittel helle Hautfarbe</string>
    <string name="enum__emoji_skin_tone__medium_skin_tone" comment="Enum value label">{emoji} Mittlere Hautfarbe</string>
    <string name="enum__emoji_skin_tone__medium_dark_skin_tone" comment="Enum value label">{emoji} Mittel dunkle Hautfarbe</string>
    <string name="enum__emoji_skin_tone__dark_skin_tone" comment="Enum value label">{emoji} Dunkle Hautfarbe</string>
    <string name="enum__emoji_hair_style__default" comment="Enum value label">{emoji} Standard Frisur</string>
    <string name="enum__emoji_hair_style__red_hair" comment="Enum value label">{emoji} Rote Haare</string>
    <string name="enum__emoji_hair_style__curly_hair" comment="Enum value label">{emoji} Lockige Haare</string>
    <string name="enum__emoji_hair_style__white_hair" comment="Enum value label">{emoji} Weiße Haare</string>
    <string name="enum__emoji_hair_style__bald" comment="Enum value label">{emoji} Glatze</string>
    <string name="enum__emoji_suggestion_type__leading_colon">Führender Doppelpunkt</string>
    <string name="enum__emoji_suggestion_type__leading_colon__description" comment="Keep the :emoji_name while translating, this is a syntax guide">Emojis mit der Syntax :emoji_name vorschlagen</string>
    <string name="enum__emoji_suggestion_type__inline_text">Eingebetteter Text</string>
    <string name="enum__emoji_suggestion_type__inline_text__description">Schlage Emojis vor, indem einfach der Emoji-Namen als Wort eingeben wird</string>
    <string name="enum__extended_actions_placement__above_candidates" comment="Enum value label">Über Kandidaten</string>
    <string name="enum__extended_actions_placement__above_candidates__description" comment="Enum value description">Platziert die erweiterte Aktionsleiste zwischen der App-Oberfläche und der Kandidatenleiste</string>
    <string name="enum__extended_actions_placement__below_candidates" comment="Enum value label">Unter Kandidaten</string>
    <string name="enum__extended_actions_placement__below_candidates__description" comment="Enum value description">Platziert die erweiterte Aktionsleiste zwischen der Kandidatenleiste und der Tastatur</string>
    <string name="enum__extended_actions_placement__overlay_app_ui" comment="Enum value label">Überlagere App-Oberfläche</string>
    <string name="enum__extended_actions_placement__overlay_app_ui__description" comment="Enum value description">Zeigt die erweiterte Aktionsleiste als Overlay über der App-Oberfläche an, ohne die Höhe der Tastatur zu beeinflussen. Es ist zu beachten, dass durch diese Platzierung das Eingabefeld in der App teilweise verdeckt werden kann</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly" comment="Enum value label">Benutze Vibrationsmotor direkt</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly__description" comment="Enum value description">{app_name} interagiert direkt mit der Vibrationshardware. Das erlaubt eine bessere Kontrolle über Dauer und Stärke der Vibration, aber die Vibration könnte weniger optimiert als bei der Verwendung der Schnittstelle fürs haptische Feedback sein</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface" comment="Enum value label">Interface für haptisches Feedback verwenden</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface__description" comment="Enum value description">{app_name} nutzt das Interface für haptisches Feedback im wir vordefinierte Vibrationssequenz bei Tastendruck auszulösen. Das könnte auf einigen Geräten sehr gut funktionieren, aber auf anderen auch komplett fehlschlagen oder sehr schlecht funktionieren</string>
    <string name="enum__key_hint_mode__accent_priority" comment="Enum value label">Aktiviert (Akzente sind priorisiert)</string>
    <string name="enum__key_hint_mode__accent_priority__description" comment="Enum value description">Das erste Zeichen, das nach langem Drücken ausgewählt wird, ist immer der Hauptakzent oder das Hinweissymbol, wenn kein Hauptakzent verfügbar ist</string>
    <string name="enum__key_hint_mode__hint_priority" comment="Enum value label">Hinweis wird priorisiert</string>
    <string name="enum__key_hint_mode__hint_priority__description" comment="Enum value description">Das erste Zeichen, das nach langem Drücken ausgewählt wird, ist immer das Hinweissymbol, oder der primäre Akzent, wenn kein Hinweissymbol verfügbar ist</string>
    <string name="enum__key_hint_mode__smart_priority" comment="Enum value label">Intelligente Priorisierung</string>
    <string name="enum__key_hint_mode__smart_priority__description" comment="Enum value description">Das erste Zeichen, das nach langem Drücken ausgewählt wird, wird je nach Sprache und Layout dynamisch, entweder als Hauptakzent oder als Hinweissymbol festgelegt</string>
    <string name="enum__incognito_display_mode__replace_shared_actions_toggle" comment="Enum value label">Symbol für \"Geteilte Aktionen\"-Schalter mit dem Inkognito-Indikator ersetzen</string>
    <string name="enum__incognito_display_mode__display_behind_keyboard" comment="Enum value label">Inkognito-Indikator hinter die Tastatur anzeigen</string>
    <string name="enum__incognito_mode__force_off" comment="Enum value label">Zwingend aus</string>
    <string name="enum__incognito_mode__force_off__description" comment="Enum value description">Inkognito-Modus ist immer deaktiviert, egal, was die aktuelle App sagt. Die Inkognito-Aktion in der Smartbar ist nicht verfügbar.</string>
    <string name="enum__incognito_mode__force_on" comment="Enum value label">Zwingend an</string>
    <string name="enum__incognito_mode__force_on__description" comment="Enum value description">Inkognito-Modus ist immer aktiviert, egal, was die aktuelle App sagt. Die Inkognito-Aktion in der Smartbar ist nicht verfügbar.</string>
    <string name="enum__incognito_mode__dynamic_on_off" comment="Enum value label">Dynamisch an/aus</string>
    <string name="enum__incognito_mode__dynamic_on_off__description" comment="Enum value description">Empfohlene Einstellung. Der anonyme Modus wird dynamisch aktiviert oder deaktiviert, entweder durch die Einstellungen der Ziel-App, oder durch das manuelle Umschalten des anonymen Modus mit der schnellen Aktion in der Smartbar.</string>
    <string name="enum__input_feedback_activation_mode__audio_respect_system_settings" comment="Enum value label">Abhängig von den Systemeinstellungen dynamisch bei Eingabe Ton abspielen</string>
    <string name="enum__input_feedback_activation_mode__audio_ignore_system_settings" comment="Enum value label">Bei Eingabe immer Ton abspielen, unabhängig von den Systemeinstellungen</string>
    <string name="enum__input_feedback_activation_mode__haptic_respect_system_settings" comment="Enum value label">Abhängig von den Systemeinstellungen dynamisch bei Eingabe vibrieren</string>
    <string name="enum__input_feedback_activation_mode__haptic_ignore_system_settings" comment="Enum value label">Bei Eingabe immer vibrieren, unabhängig von den Systemeinstellungen</string>
    <string name="enum__input_shift_state__unshifted" comment="Enum value label">Nicht umgeschaltet</string>
    <string name="enum__input_shift_state__shifted_manual" comment="Enum value label">Umgeschaltet (manuell)</string>
    <string name="enum__input_shift_state__shifted_automatic" comment="Enum value label">Umgeschaltet (automatisch)</string>
    <string name="enum__input_shift_state__caps_lock" comment="Enum value label">Feststelltaste</string>
    <string name="enum__landscape_input_ui_mode__never_show" comment="Enum value label">Nie anzeigen</string>
    <string name="enum__landscape_input_ui_mode__always_show" comment="Enum value label">Immer anzeigen</string>
    <string name="enum__landscape_input_ui_mode__dynamically_show" comment="Enum value label">Dynamisch anzeigen</string>
    <string name="enum__one_handed_mode__start" comment="Enum value label">Linkshänder-Modus</string>
    <string name="enum__one_handed_mode__end" comment="Enum value label">Rechtshänder-Modus</string>
    <string name="enum__shape_corner__top_start" comment="Enum value label">Anfang oben</string>
    <string name="enum__shape_corner__top_end" comment="Enum value label">Ende oben</string>
    <string name="enum__shape_corner__bottom_end" comment="Enum value label">Ende unten</string>
    <string name="enum__shape_corner__bottom_start" comment="Enum value label">Anfang unten</string>
    <string name="enum__smartbar_layout__suggestions_only" comment="Enum value label">Nur Vorschläge</string>
    <string name="enum__smartbar_layout__suggestions_only__description" comment="Enum value description">Zeigt nur die Kandidatenleiste an, ohne Aktionsleiste / Schalter oder fixierter Aktion</string>
    <string name="enum__smartbar_layout__actions_only" comment="Enum value label">Nur Aktionen</string>
    <string name="enum__smartbar_layout__actions_only__description" comment="Enum value description">Zeigt nur die Aktionsleiste, ohne Vorschläge-Leiste oder expliziter fixierter Aktion</string>
    <string name="enum__smartbar_layout__suggestions_action_shared" comment="Enum value label">Geteilte Vorschläge &amp; Aktionen</string>
    <string name="enum__smartbar_layout__suggestions_action_shared__description" comment="Enum value description">Geteilte umschaltbare Vorschlags- und Aktionsleiste, mit fixierter Aktion</string>
    <string name="enum__smartbar_layout__suggestions_actions_extended" comment="Enum value label">Vorschläge &amp; erweiterte Aktionen</string>
    <string name="enum__smartbar_layout__suggestions_actions_extended__description" comment="Enum value description">Statische Vorschlagsleiste und zusätzliche umschaltbare Aktionsleiste mit fixierter Aktion</string>
    <string name="enum__snygg_level__basic" comment="Enum value label">Einfach</string>
    <string name="enum__snygg_level__basic__description" comment="Enum value description">Nur Farbeigenschaften werden angezeigt. Eigenschaften und Regeln werden übersetzt.</string>
    <string name="enum__snygg_level__advanced" comment="Enum value label">Erweitert</string>
    <string name="enum__snygg_level__advanced__description" comment="Enum value description">Alle Eigenschaften werden angezeigt. Eigenschaften und Regeln werden übersetzt.</string>
    <string name="enum__snygg_level__developer" comment="Enum value label">Entwickler</string>
    <string name="enum__snygg_level__developer__description" comment="Enum value description">Alle Eigenschaften werden angezeigt. Eigenschaften und Regeln werden so wie im Stylesheet selbst dargestellt.</string>
    <string name="enum__space_bar_mode__nothing" comment="Enum value label">Keine Beschriftung</string>
    <string name="enum__space_bar_mode__current_language" comment="Enum value label">Aktuelle Sprache</string>
    <string name="enum__space_bar_mode__space_bar_key" comment="Enum value label">␣</string>
    <string name="enum__spelling_language_mode__use_system_languages" comment="Enum value label">Systemsprache verwenden</string>
    <string name="enum__spelling_language_mode__use_keyboard_subtypes" comment="Enum value label">Tastatur-Subtypen verwenden</string>
    <string name="enum__swipe_action__no_action" comment="Enum value label">Keine Aktion</string>
    <string name="enum__swipe_action__cycle_to_previous_keyboard_mode" comment="Enum value label">Wechsle zum vorherigen Tastaturmodus</string>
    <string name="enum__swipe_action__cycle_to_next_keyboard_mode" comment="Enum value label">Wechsle zum nächsten Tastaturmodus</string>
    <string name="enum__swipe_action__delete_character" comment="Enum value label">Löscht einzelne Zeichen</string>
    <string name="enum__swipe_action__delete_characters_precisely" comment="Enum value label">Zeichen einzeln löschen</string>
    <string name="enum__swipe_action__delete_word" comment="Enum value label">Löscht ganze Wörter</string>
    <string name="enum__swipe_action__delete_words_precisely" comment="Enum value label">Ganzes Wort löschen</string>
    <string name="enum__swipe_action__hide_keyboard" comment="Enum value label">Tastatur ausblenden</string>
    <string name="enum__swipe_action__insert_space" comment="Enum value label">Leerzeichen einfügen</string>
    <string name="enum__swipe_action__move_cursor_up" comment="Enum value label">Cursor nach oben bewegen</string>
    <string name="enum__swipe_action__move_cursor_down" comment="Enum value label">Cursor nach unten bewegen</string>
    <string name="enum__swipe_action__move_cursor_left" comment="Enum value label">Cursor nach links bewegen</string>
    <string name="enum__swipe_action__move_cursor_right" comment="Enum value label">Cursor nach rechts bewegen</string>
    <string name="enum__swipe_action__move_cursor_start_of_line" comment="Enum value label">Cursor an den Zeilenanfang bewegen</string>
    <string name="enum__swipe_action__move_cursor_end_of_line" comment="Enum value label">Cursor an das Zeilenende bewegen</string>
    <string name="enum__swipe_action__move_cursor_start_of_page" comment="Enum value label">Bewege den Cursor zum Anfang der Seite</string>
    <string name="enum__swipe_action__move_cursor_end_of_page" comment="Enum value label">Bewege den Cursor zum Ende der Seite</string>
    <string name="enum__swipe_action__switch_to_clipboard_context" comment="Enum value label">Zwischenablage-Verlauf öffnen</string>
    <string name="enum__swipe_action__shift" comment="Enum value label">Umschalt</string>
    <string name="enum__swipe_action__redo" comment="Enum value label">Wiederherstellen</string>
    <string name="enum__swipe_action__undo" comment="Enum value label">Rückgängig</string>
    <string name="enum__swipe_action__select_characters_precisely" comment="Enum value label">Zeichen präzise auswählen</string>
    <string name="enum__swipe_action__select_words_precisely" comment="Enum value label">Wörter präzise auswählen</string>
    <string name="enum__swipe_action__show_input_method_picker" comment="Enum value label">Auswahl der Eingabemethode anzeigen</string>
    <string name="enum__swipe_action__switch_to_prev_keyboard" comment="Enum value label">Wechsle zur vorherigen Tastatur</string>
    <string name="enum__swipe_action__switch_to_prev_subtype" comment="Enum value label">Wechsle zum vorherigen Subtyp</string>
    <string name="enum__swipe_action__switch_to_next_subtype" comment="Enum value label">Wechsle zum nächsten Subtyp</string>
    <string name="enum__swipe_action__toggle_smartbar_visibility" comment="Enum value label">Smartbar sichtbarkeit umschalten</string>
    <string name="enum__theme_mode__always_day" comment="Enum value label">Immer Tag</string>
    <string name="enum__theme_mode__always_night" comment="Enum value label">Immer Nacht</string>
    <string name="enum__theme_mode__follow_system" comment="Enum value label">System-Design folgen</string>
    <string name="enum__theme_mode__follow_time" comment="Enum value label">Zeit folgen</string>
    <string name="enum__utility_key_action__switch_to_emojis" comment="Enum value label">Zu Emojis wechseln</string>
    <string name="enum__utility_key_action__switch_language" comment="Enum value label">Sprache wechseln</string>
    <string name="enum__utility_key_action__switch_keyboard_app" comment="Enum value label">Tastatur-App wechseln</string>
    <string name="enum__utility_key_action__dynamic_switch_language_emojis" comment="Enum value label">Dynamisch: Zu Emojis / Sprache wechseln</string>
    <!-- Unit strings (symbols) -->
    <!-- Unit strings (written words) -->
    <plurals name="unit__hours__written">
        <item quantity="one">{v} Stunde</item>
        <item quantity="other">{v} Stunden</item>
    </plurals>
    <plurals name="unit__minutes__written">
        <item quantity="one">{v} Minute</item>
        <item quantity="other">{v} Minuten</item>
    </plurals>
    <plurals name="unit__seconds__written">
        <item quantity="one">{v} Sekunde</item>
        <item quantity="other">{v} Sekunden</item>
    </plurals>
    <plurals name="unit__items__written">
        <item quantity="one">{v} Element</item>
        <item quantity="other">{v} Elemente</item>
    </plurals>
    <plurals name="unit__characters__written">
        <item quantity="one">{v} Zeichen</item>
        <item quantity="other">{v} Zeichen</item>
    </plurals>
    <plurals name="unit__candidates__written">
        <item quantity="one">{v} Vorschlag</item>
        <item quantity="other">{v} Vorschläge</item>
    </plurals>
</resources>
