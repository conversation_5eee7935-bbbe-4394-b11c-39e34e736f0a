<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">FlorisBoard</string>
    <string name="key__phone_pause" comment="Label for the Pause key in the telephone keyboard layout">Пауза</string>
    <string name="key__phone_wait" comment="Label for the Wait key in the telephone keyboard layout">Ожидание</string>
    <string name="key_popup__threedots_alt" comment="Content description for the three-dots icon in a key popup">Значок троеточия. Если отображается, показывает, что можно использовать больше знаков при долгом нажатии.</string>
    <!-- One-handed strings -->
    <string name="one_handed__close_btn_content_description" comment="Content description for the one-handed close button">Закрыть режим одной руки.</string>
    <string name="one_handed__move_start_btn_content_description" comment="Content description for the one-handed move to left button">Переместить клавиатуру влево.</string>
    <string name="one_handed__move_end_btn_content_description" comment="Content description for the one-handed move to right button">Переместить клавиатуру вправо.</string>
    <!-- Media strings -->
    <string name="settings__media__title">Эмодзи</string>
    <string name="media__tab__emojis" comment="Tab description for emojis in the media UI">Эмодзи</string>
    <string name="media__tab__emoticons" comment="Tab description for emoticons in the media UI">Смайлики</string>
    <string name="media__tab__kaomoji" comment="Tab description for kaomoji in the media UI">Каомодзи</string>
    <string name="prefs__media__emoji_preferred_skin_tone">Предпочтительный цвет кожи эмодзи</string>
    <string name="prefs__media__emoji_preferred_hair_style">Предпочтительная прическа эмодзи</string>
    <string name="prefs__media__emoji_history__title" comment="Preference group title">История эмодзи</string>
    <string name="prefs__media__emoji_history_enabled" comment="Preference title">Включить историю эмодзи</string>
    <string name="prefs__media__emoji_history_enabled__summary" comment="Preference summary">Сохраняйте недавно использованные эмодзи для быстрого доступа</string>
    <string name="prefs__media__emoji_history_pinned_update_strategy" comment="Preference title">Обновление истории (закрепленного)</string>
    <string name="prefs__media__emoji_history_recent_update_strategy" comment="Preference title">Обновление истории (недавнего)</string>
    <string name="prefs__media__emoji_history_max_size">Максимум элементов, которые можно сохранить</string>
    <string name="prefs__media__emoji_history_pinned_reset">Сбросить закрепленные эмодзи</string>
    <string name="prefs__media__emoji_history_reset">Сбросить последние эмодзи</string>
    <string name="prefs__media__emoji_suggestion__title" comment="Preference group title">Подсказки эмодзи</string>
    <string name="prefs__media__emoji_suggestion_enabled" comment="Preference title">Включить подсказки эмодзи</string>
    <string name="prefs__media__emoji_suggestion_enabled__summary" comment="Preference summary">Предлагать эмодзи при наборе текста</string>
    <string name="prefs__media__emoji_suggestion_type" comment="Preference title">Тип триггера</string>
    <string name="prefs__media__emoji_suggestion_update_history" comment="Preference title">Обновить историю эмодзи</string>
    <string name="prefs__media__emoji_suggestion_update_history__summary" comment="Preference summary">Принятие предложенных эмодзи добавляет их в историю эмодзи</string>
    <string name="prefs__media__emoji_suggestion_candidate_show_name" comment="Preference title">Показать название эмодзи</string>
    <string name="prefs__media__emoji_suggestion_candidate_show_name__summary" comment="Preference summary">Предложения эмодзи отображают их название рядом</string>
    <string name="prefs__media__emoji_suggestion_query_min_length" comment="Preference title">Минимальная длина запроса</string>
    <string name="prefs__media__emoji_suggestion_candidate_max_count" comment="Preference title">Максимальное количество предложений</string>
    <!-- Emoji strings -->
    <string name="emoji__category__smileys_emotion" comment="Emoji category name">Смайлы и эмоции</string>
    <string name="emoji__category__people_body" comment="Emoji category name">Люди и тело</string>
    <string name="emoji__category__animals_nature" comment="Emoji category name">Животные и природа</string>
    <string name="emoji__category__food_drink" comment="Emoji category name">Еда и напитки</string>
    <string name="emoji__category__travel_places" comment="Emoji category name">Путешествия и места</string>
    <string name="emoji__category__activities" comment="Emoji category name">События</string>
    <string name="emoji__category__objects" comment="Emoji category name">Объекты</string>
    <string name="emoji__category__symbols" comment="Emoji category name">Символы</string>
    <string name="emoji__category__flags" comment="Emoji category name">Флаги</string>
    <string name="emoji__history__empty_message" comment="Message if the emoji history is empty">Недавно использованные эмодзи не найдены. Как только вы начнете использовать эмодзи, они автоматически будут появляться здесь.</string>
    <string name="emoji__history__phone_locked_message" comment="Message to show if phone is locked">Чтобы получить доступ к истории эмодзи, сначала разблокируйте ваше устройство.</string>
    <string name="emoji__history__usage_tip" comment="Feature discoverability for actions of emoji history">Совет: долгое нажатие на эмодзи в истории эмодзи для его закрепления или удаления!</string>
    <string name="emoji__history__removal_success_message" comment="Toast message if user has used the delete action on an emoji in the emoji history">Удаление {emoji} из истории эмодзи</string>
    <string name="emoji__history__pinned">Закреплено</string>
    <string name="emoji__history__recent">Недавние</string>
    <!-- Quick action strings -->
    <string name="quick_action__arrow_up" maxLength="12">Вверх</string>
    <string name="quick_action__arrow_up__tooltip">Переместить курсор вверх</string>
    <string name="quick_action__arrow_down" maxLength="12">Вниз</string>
    <string name="quick_action__arrow_down__tooltip">Переместить курсор вниз</string>
    <string name="quick_action__arrow_left" maxLength="12">Влево</string>
    <string name="quick_action__arrow_left__tooltip">Переместить курсор влево</string>
    <string name="quick_action__arrow_right" maxLength="12">Вправо</string>
    <string name="quick_action__arrow_right__tooltip">Переместить курсор вправо</string>
    <string name="quick_action__clipboard_clear_primary_clip" maxLength="12">Очистить</string>
    <string name="quick_action__clipboard_clear_primary_clip__tooltip">Выполнить очистку основного клипа буфера обмена</string>
    <string name="quick_action__clipboard_copy" maxLength="12">Копировать</string>
    <string name="quick_action__clipboard_copy__tooltip">Выполнить копирование из буфера обмена</string>
    <string name="quick_action__clipboard_cut" maxLength="12">Вырезать</string>
    <string name="quick_action__clipboard_cut__tooltip">Выполнить вырезку из буфера обмена</string>
    <string name="quick_action__clipboard_paste" maxLength="12">Вставить</string>
    <string name="quick_action__clipboard_paste__tooltip">Выполнить вставку из буфера обмена</string>
    <string name="quick_action__clipboard_select_all" maxLength="12">Выбрать все</string>
    <string name="quick_action__clipboard_select_all__tooltip">Выполнить выделение всего буфера обмена</string>
    <string name="quick_action__ime_ui_mode_clipboard" maxLength="12">Буфер обмена</string>
    <string name="quick_action__ime_ui_mode_clipboard__tooltip">Включить историю буфера обмена</string>
    <string name="quick_action__ime_ui_mode_media" maxLength="12">Эмодзи</string>
    <string name="quick_action__ime_ui_mode_media__tooltip">Открыть панель эмодзи</string>
    <string name="quick_action__settings" maxLength="12">Настройки</string>
    <string name="quick_action__settings__tooltip">Открыть настройки</string>
    <string name="quick_action__undo" maxLength="12">Отменить</string>
    <string name="quick_action__undo__tooltip">Отменить последний ввод</string>
    <string name="quick_action__redo" maxLength="12">Повторить</string>
    <string name="quick_action__redo__tooltip">Повторить последний ввод</string>
    <string name="quick_action__toggle_actions_overflow" maxLength="12">Ещё действия</string>
    <string name="quick_action__toggle_actions_overflow__tooltip">Показать или убрать дополнительные действия</string>
    <string name="quick_action__toggle_incognito_mode" maxLength="12">Инкогнито</string>
    <string name="quick_action__toggle_incognito_mode__tooltip">Включить режим инкогнито</string>
    <string name="quick_action__toggle_autocorrect" maxLength="12">Автоисправ.</string>
    <string name="quick_action__toggle_autocorrect__tooltip">Включить автоисправление</string>
    <string name="quick_action__voice_input" maxLength="12">Голос. ввод</string>
    <string name="quick_action__voice_input__tooltip" comment="IME stands for Input Method Editor and is indirectly equivalent to 'keyboard'.">Открытый провайдер голосового ввода</string>
    <string name="quick_action__one_handed_mode" maxLength="12">Одной рукой</string>
    <string name="quick_action__one_handed_mode__tooltip">Включить режим управления одной рукой</string>
    <string name="quick_action__drag_marker" maxLength="12" comment="This action is only used as a placeholder in the actions editor drag and drop screen and only visible in debug mode">Курсор</string>
    <string name="quick_action__drag_marker__tooltip" comment="This action is only used as a placeholder in the actions editor drag and drop screen and only visible in debug mode">Текущее положение маркера перетаскивания</string>
    <string name="quick_action__noop" maxLength="12" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">Отсутствует</string>
    <string name="quick_action__noop__tooltip" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">Нет операции</string>
    <string name="quick_actions_overflow__customize_actions_button">Изменить порядок действий</string>
    <string name="quick_actions_editor__header">Настроить порядок действий</string>
    <string name="quick_actions_editor__subheader_sticky_action">Закреплённое действие ({n})</string>
    <string name="quick_actions_editor__subheader_dynamic_actions">Динамические действия ({n})</string>
    <string name="quick_actions_editor__subheader_hidden_actions">Скрытые действия ({n})</string>
    <string name="select_subtype_panel__header">Выбрать подтип</string>
    <!-- Incognito mode strings -->
    <string name="incognito_mode__toast_after_enabled">Режим инкогнито включён\n\n{app_name} не будет обучаться под ваш стиль письма, пока этот режим активен</string>
    <string name="incognito_mode__toast_after_disabled">Режим инкогнито отключён по умолчанию</string>
    <!-- Settings UI strings -->
    <string name="settings__title" comment="Title of Settings">Настройки</string>
    <string name="settings__preview_keyboard" comment="Hint for try your setup box">Протестируйте свои настройки</string>
    <string name="settings__help" comment="General label for help buttons in Settings">Помощь</string>
    <string name="settings__default" comment="General string which is used when a preference has the default value set">По умолчанию</string>
    <string name="settings__system_default" comment="General string which is used when a preference has the system default value set">Как в системе</string>
    <string name="settings__home__title" comment="Title of the Home screen">Приветствуем в {app_name}</string>
    <string name="settings__home__ime_not_enabled" comment="Error message shown in Home fragment when FlorisBoard is not enabled in the system">FlorisBoard не включена в системе и поэтому не будет доступна в качестве метода ввода. Нажмите здесь, чтобы исправить эту проблему.</string>
    <string name="settings__home__ime_not_selected" comment="Warning message shown in Home fragment when FlorisBoard is not selected as the default keyboard">FlorisBoard не выбрана в качестве метода ввода по умолчанию. Нажмите здесь, чтобы исправить эту проблему.</string>
    <string name="settings__localization__title" comment="Title of languages and Layout screen">Языки и раскладки</string>
    <string name="settings__localization__display_language_names_in__label" comment="Label of Display language names in preference">Подписи к языкам</string>
    <string name="settings__localization__display_keyboard_labels_in_subtype_language" comment="Label of Display keyboard labels in subtype language preference">Отображать надписи на клавиатуре на языке подтипов</string>
    <string name="settings__localization__group_subtypes__label" comment="Label of subtypes group">Подвиды</string>
    <string name="settings__localization__subtype_add_title" comment="Title of subtype dialog when adding a new subtype">Добавить раскладку</string>
    <string name="settings__localization__language_pack_title" comment="Title of the language pack manager screen for managing installed and custom language packs">Установленные языковые пакеты</string>
    <string name="settings__localization__language_pack_summary" comment="Summary of preference item for adding a new language pack">Экспериментально: управление расширениями, которые добавляют поддержку определенных языков (на данный момент китайский ввод на основе формы)</string>
    <string name="settings__localization__subtype_edit_title" comment="Title of subtype dialog when editing an existing subtype">Изменить раскладку</string>
    <string name="settings__localization__subtype_locale" comment="Label for locale dropdown in subtype dialog">Основной язык</string>
    <string name="settings__localization__subtype_popup_mapping" comment="Label for popup mapping dropdown in subtype screen">Отображение всплывающих окон</string>
    <string name="settings__localization__subtype_characters_layout" comment="Label for layout dropdown in subtype dialog">Схема расположения кнопок</string>
    <string name="settings__localization__subtype_suggestion_provider" comment="Label for suggestion provider dropdown in subtype dialog">Механизм предложений</string>
    <string name="settings__localization__subtype_symbols_layout" comment="Label for layout dropdown in subtype dialog">Основная символьная раскладка</string>
    <string name="settings__localization__subtype_symbols2_layout" comment="Label for layout dropdown in subtype dialog">Дополнительная символьная раскладка</string>
    <string name="settings__localization__subtype_composer" comment="Label for composer dropdown in subtype dialog.">Редактор</string>
    <string name="settings__localization__subtype_currency_set" comment="Label for currency set dropdown in subtype dialog. 'set' is used as a noun here and can be compared to a group of elements (in this case currency symbols).">Валюта по умолчанию</string>
    <string name="settings__localization__subtype_numeric_layout" comment="Label for layout dropdown in subtype dialog">Цифровая раскладка</string>
    <string name="settings__localization__subtype_numeric_advanced_layout" comment="Label for layout dropdown in subtype dialog">Цифровая (расширенная) раскладка</string>
    <string name="settings__localization__subtype_numeric_row_layout" comment="Label for layout dropdown in subtype dialog">Настройка цифрового ряда</string>
    <string name="settings__localization__subtype_phone_layout" comment="Label for layout dropdown in subtype dialog">Основная телефонная раскладка</string>
    <string name="settings__localization__subtype_phone2_layout" comment="Label for layout dropdown in subtype dialog">Дополнительная телефонная раскладка</string>
    <string name="settings__localization__subtype_select_locale" comment="Subtype select language title">Выберите язык</string>
    <string name="settings__localization__subtype_search_locale_placeholder" comment="Subtype search language placeholder">Поиск языка</string>
    <string name="settings__localization__subtype_search_locale_not_found" comment="Subtype search language not found">Не удалось найти соответствие языка \"{search_term}\".</string>
    <string name="settings__localization__subtype_select_placeholder" comment="Subtype dialog select value placeholder (&amp;#8210; is a hyphen character)">&#8210; не выбрано &#8210;</string>
    <string name="settings__localization__subtype_summary" comment="Subtype summary">{characters_name} / {symbols_name} / {currency_set_name}</string>
    <string name="settings__localization__suggested_subtype_presets" comment="Suggested presets title">Предлагаемые шаблоны</string>
    <string name="settings__localization__suggested_subtype_presets_none_found" comment="Suggested presets none found">Нет предложений. Используйте кнопку ниже для просмотра всех шаблонов.</string>
    <string name="settings__localization__subtype_presets" comment="Subtype presets dialog title">Шаблоны</string>
    <string name="settings__localization__subtype_presets_view_all" comment="View all presets button">Показать все</string>
    <string name="settings__localization__subtype_no_subtypes_configured_warning" comment="Warning message that no subtype has been defined">Вы не настроили раскладки, поэтому в качестве временного варианта будет отображаться English/QWERTY!</string>
    <string name="settings__localization__subtype_error_already_exists" comment="Error message shown in subtype dialog when a subtype to add already exists">Эта раскладка уже существует!</string>
    <string name="settings__localization__subtype_error_fields_no_value" comment="Error message shown in subtype editor if at least one field is set to '- select -' (means no value specified)">По крайней мере, для одного поля не выбрано значение. Пожалуйста, выберите значение для этого поля (полей).</string>
    <string name="settings__localization__subtype_error_layout_not_installed" comment="Error message shown in subtype list when a layout is not installed, where %s will be replaced by the layout ID">{layout_id} (не установлен)</string>
    <string name="settings__localization__group_layouts__label" comment="Label of layouts group">Раскладки</string>
    <string name="settings__localization__subtype_delete_confirmation_title" comment="Title of the subtype delete confirmation dialog">Подтверждение удаления</string>
    <string name="settings__localization__subtype_delete_confirmation_warning" comment="Warning message in the confirmation dialog to confirm the user's intent to delete">Вы уверены что хотите удалить этот подтип?</string>
    <string name="settings__theme__title" comment="Title of the Theme screen">Темы</string>
    <string name="pref__theme__mode__label" comment="Label of the theme mode preference">Тема оформления</string>
    <string name="pref__theme__sunrise_time__label" comment="Label of the sunrise time preference">Время восхода</string>
    <string name="pref__theme__sunset_time__label" comment="Label of the sunset time preference">Время заката</string>
    <string name="pref__theme__day" comment="Label of the day group (day means light theme)">Дневная тема</string>
    <string name="pref__theme__night" comment="Label of the night group (night means dark theme)">Ночная тема</string>
    <string name="pref__theme__theme_accent_color__label" comment="Label of accent color preference in Theme">        Цвет акцента (для тем Material you)
    </string>
    <string name="settings__theme_manager__title_manage" comment="Title of the theme manager screen for managing installed and custom themes">Установленные темы</string>
    <string name="pref__theme__source_assets" comment="Label for the theme source field">Наборы приложений FlorisBoard</string>
    <string name="pref__theme__source_internal" comment="Label for the theme source field">Внутреннее хранилище</string>
    <string name="pref__theme__source_external" comment="Label for the theme source field">Внешний источник</string>
    <string name="settings__theme_manager__title_day" comment="Title of the theme manager screen for day theme selection">Выбрать светлую тему</string>
    <string name="settings__theme_manager__title_night" comment="Title of the theme manager screen for night theme selection">Выбрать темную тему</string>
    <string name="settings__theme_editor__fine_tune__title">Настройки редактора тем</string>
    <string name="settings__theme_editor__fine_tune__level">Уровень редактирования</string>
    <string name="settings__theme_editor__fine_tune__display_kbd_after_dialogs">Отображать клавиатуру после диалогов</string>
    <string name="settings__theme_editor__add_rule">Добавить правило</string>
    <string name="settings__theme_editor__edit_rule">Изменить правило</string>
    <string name="settings__theme_editor__no_rules_defined">В таблице стилей не определены правила. Добавьте правило, чтобы начать её настройку.</string>
    <string name="settings__theme_editor__rule_already_exists">Это правило таблицы стилей уже определено.</string>
    <string name="settings__theme_editor__rule_codes">Целевые коды клавиш</string>
    <string name="settings__theme_editor__rule_groups">Группы</string>
    <string name="settings__theme_editor__rule_selectors">Переключатели</string>
    <string name="settings__theme_editor__add_code">Добавить код клавиши</string>
    <string name="settings__theme_editor__edit_code">Изменить код клавиши</string>
    <string name="settings__theme_editor__no_codes_defined">Применить правило ко всем целевым элементам.</string>
    <string name="settings__theme_editor__code_already_exists">Этот код уже используется.</string>
    <string name="settings__theme_editor__code_invalid">Это неправильный код клавиши. Убедитесь, что он находится в диапазоне от {c_min} до {c_max} для символов или от {i_min} до {i_max} для специальных внутренних клавиш.</string>
    <string name="settings__theme_editor__code_help_text">В качестве альтернативы следующие ссылки помогут вам найти соответствующий код клавиши:</string>
    <string name="settings__theme_editor__code_placeholder">Код</string>
    <string name="settings__theme_editor__code_recording_help_text">Чтобы найти код клавиши, используйте кнопку рядом с полем ввода кода. После активации следующее нажатие кнопки будет записано и код вставлен в поле ввода.</string>
    <string name="settings__theme_editor__code_recording_started">Началась запись кода клавиши</string>
    <string name="settings__theme_editor__code_recording_stopped">Запись кода клавиши остановлена</string>
    <string name="settings__theme_editor__code_recording_requires_default_ime_floris">{app_name} должна быть клавиатурой по умолчанию для записи кода клавиши</string>
    <string name="settings__theme_editor__code_recording_placeholder">Запись…</string>
    <string name="settings__theme_editor__add_property">Добавить свойство</string>
    <string name="settings__theme_editor__edit_property">Изменить свойство</string>
    <string name="settings__theme_editor__property_already_exists">Свойство с таким именем уже существует в текущем правиле.</string>
    <string name="settings__theme_editor__property_name">Название свойства</string>
    <string name="settings__theme_editor__property_value">Значения свойства</string>
    <string name="settings__theme_editor__property_value_shape_apply_for_all_corners">Применить ко всем углам</string>
    <string name="settings__theme_editor__property_value_color_dialog_title">Изменить цвет строки</string>
    <string name="settings__theme_editor__component_meta_is_night_theme">Это ночная тема</string>
    <string name="settings__theme_editor__component_meta_is_borderless">Без контура</string>
    <string name="settings__theme_editor__component_meta_stylesheet_path">Путь таблицы стилей</string>
    <string name="snygg__rule_annotation__defines">Переменные</string>
    <string name="snygg__rule_annotation__font">Шрифт</string>
    <string name="snygg__rule_annotation__font_name">Название шрифта</string>
    <string name="snygg__rule_element__key">Кнопка</string>
    <string name="snygg__rule_element__key_hint">Подсказка кнопки</string>
    <string name="snygg__rule_element__clipboard_header">Заголовок буфера обмена</string>
    <string name="snygg__rule_element__clipboard_item">Элемент буфера обмена</string>
    <string name="snygg__rule_element__clipboard_item_popup">Всплывающее окно элемента буфера обмена</string>
    <string name="snygg__rule_element__extracted_landscape_input_layout">Схема горизонтального ввода</string>
    <string name="snygg__rule_element__extracted_landscape_input_field">Горизонтальное поле ввода</string>
    <string name="snygg__rule_element__extracted_landscape_input_action">Действие горизонтального ввода</string>
    <string name="snygg__rule_element__glide_trail">Непрерывный след</string>
    <string name="snygg__rule_element__incognito_mode_indicator">Индикатор режима инкогнито</string>
    <string name="snygg__rule_element__one_handed_panel">Панель для одной руки</string>
    <string name="snygg__rule_element__smartbar">Умная панель навигации</string>
    <string name="snygg__rule_element__smartbar_shared_actions_row">Ряд основных действий умной панели</string>
    <string name="snygg__rule_element__smartbar_shared_actions_toggle">Переключатель основных действий умной панели</string>
    <string name="snygg__rule_element__smartbar_extended_actions_row">Ряд расширенных действий умной панели</string>
    <string name="snygg__rule_element__smartbar_extended_actions_toggle">Переключатель расширенных действий умной панели</string>
    <string name="snygg__rule_element__smartbar_action_key">Кнопка действия умной панели</string>
    <string name="snygg__rule_element__smartbar_action_tile">Кнопка действия умной панели</string>
    <string name="snygg__rule_element__smartbar_actions_overflow">Порядок действий умной панели</string>
    <string name="snygg__rule_element__smartbar_actions_overflow_customize_button">Кнопка изменения порядка действий умной панели</string>
    <string name="snygg__rule_element__smartbar_actions_editor">Редактор действий умной панели</string>
    <string name="snygg__rule_element__smartbar_actions_editor_header">Заголовок редактора действий умной панели</string>
    <string name="snygg__rule_element__smartbar_actions_editor_subheader">Подзаголовок редактора действий умной панели</string>
    <string name="snygg__rule_element__smartbar_candidates_row">Ряд предложений умной панели</string>
    <string name="snygg__rule_element__smartbar_candidate_word">Слово-предложение умной панели</string>
    <string name="snygg__rule_element__smartbar_candidate_clip">Запись-предложение умной панели</string>
    <string name="snygg__rule_element__smartbar_candidate_spacer">Разделитель-предложение умной панели</string>
    <string name="snygg__rule_selector__pressed">При нажатии</string>
    <string name="snygg__rule_selector__focus">При фокусе</string>
    <string name="snygg__rule_selector__disabled">Отключено</string>
    <string name="snygg__property_name__background">Фон</string>
    <string name="snygg__property_name__foreground">Передний план</string>
    <string name="snygg__property_name__border_color">Цвет контура</string>
    <string name="snygg__property_name__border_style">Стиль контура</string>
    <string name="snygg__property_name__border_width">Ширина контура</string>
    <string name="snygg__property_name__font_family">Семейство шрифтов</string>
    <string name="snygg__property_name__font_size">Размер шрифта</string>
    <string name="snygg__property_name__font_style">Стиль шрифта</string>
    <string name="snygg__property_name__font_weight">Начертание шрифта</string>
    <string name="snygg__property_name__shadow_elevation">Высота тени</string>
    <string name="snygg__property_name__shape">Форма</string>
    <string name="snygg__property_name__var_primary">Основной цвет</string>
    <string name="snygg__property_name__var_primary_variant">Основной цвет (вариант)</string>
    <string name="snygg__property_name__var_secondary">Дополнительный цвет</string>
    <string name="snygg__property_name__var_secondary_variant">Дополнительный цвет (вариант)</string>
    <string name="snygg__property_name__var_background">Основной фон</string>
    <string name="snygg__property_name__var_surface">Обычная поверхность</string>
    <string name="snygg__property_name__var_surface_variant">Обычная поверхность (вариант)</string>
    <string name="snygg__property_name__var_on_primary">Передний план основного</string>
    <string name="snygg__property_name__var_on_secondary">Передний план дополнительного</string>
    <string name="snygg__property_name__var_on_background">Передний план фона</string>
    <string name="snygg__property_name__var_on_surface">Передний план поверхности</string>
    <string name="snygg__property_name__var_on_surface_variant">Передний план поверхности (вариант)</string>
    <string name="snygg__property_name__var_shape">Обычная форма</string>
    <string name="snygg__property_name__var_shape_variant">Обычная форма (вариант)</string>
    <string name="snygg__property_value__explicit_inherit">Наследование</string>
    <string name="snygg__property_value__defined_var">Ссылка на значение</string>
    <string name="snygg__property_value__solid_color">Сплошной цвет</string>
    <string name="snygg__property_value__material_you_light_color">Динамический цвет (из светлой темы)</string>
    <string name="snygg__property_value__material_you_dark_color">Динамический цвет (из тёмной темы)</string>
    <string name="snygg__property_value__rectangle_shape">Прямоугольная форма</string>
    <string name="snygg__property_value__circle_shape">Круглая форма</string>
    <string name="snygg__property_value__cut_corner_shape_dp">Форма со срезанием углов (dp)</string>
    <string name="snygg__property_value__cut_corner_shape_percent">Форма со срезанием углов (%)</string>
    <string name="snygg__property_value__rounded_corner_shape_dp">Форма с закруглением углов (dp)</string>
    <string name="snygg__property_value__rounded_corner_shape_percent">Форма с закруглением углов (%)</string>
    <string name="snygg__property_value__dp_size">Размер (dp)</string>
    <string name="snygg__property_value__sp_size">Размер (sp)</string>
    <string name="snygg__property_value__percentage_size">Размер (%)</string>
    <string name="settings__input_feedback__title" comment="Title of Input Feedback screen">Звук и вибрация</string>
    <string name="pref__input_feedback__group_audio__label" comment="Preference group title">Аудиоотклик / звуки</string>
    <string name="pref__input_feedback__audio_enabled__label" comment="Preference title">Использовать аудиоотклик</string>
    <string name="pref__input_feedback__audio_enabled__summary_disabled" comment="Preference summary">Не воспроизводить звуки при вводе вне зависимости от системных настроек</string>
    <string name="pref__input_feedback__audio_volume__label" comment="Preference title">Громкость звука при вводе</string>
    <string name="pref__input_feedback__audio_feat_key_press__label" comment="Preference title">Звук нажатия кнопки</string>
    <string name="pref__input_feedback__audio_feat_key_long_press__label" comment="Preference title">Звук долгого нажатия кнопки</string>
    <string name="pref__input_feedback__audio_feat_key_repeated_action__label" comment="Preference title">Звук автоповтора ввода кнопки</string>
    <string name="pref__input_feedback__audio_feat_gesture_swipe__label" comment="Preference title">Звук жестов смахиваний</string>
    <string name="pref__input_feedback__audio_feat_gesture_moving_swipe__label" comment="Preference title">Звук жестов перетаскивания</string>
    <string name="pref__input_feedback__group_haptic__label" comment="Preference group title">Виброотклик / вибрация</string>
    <string name="pref__input_feedback__haptic_enabled__label" comment="Preference title">Использовать виброотклик</string>
    <string name="pref__input_feedback__haptic_enabled__summary_disabled" comment="Preference summary">Не использовать вибрацию при вводе вне зависимости от системных настроек</string>
    <string name="pref__input_feedback__haptic_vibration_mode__label" comment="Preference title">Режим вибрации</string>
    <string name="pref__input_feedback__haptic_vibration_duration__label" comment="Preference title">Продолжительность вибрации</string>
    <string name="pref__input_feedback__haptic_vibration_strength__label" comment="Preference title">Сила вибрации</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_vibrator" comment="Preference summary">Для этой функции требуется вибромотор, который, по-видимому, отсутствует на данном устройстве</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_amplitude_ctrl" comment="Preference summary">Эта функция требует аппаратной поддержки управления амплитудой, которая отсутствует на вашем устройстве</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_unsupported_android_version" comment="Preference summary">Эта функция требует поддержки управления амплитудой, которая доступна только на Android 8.0 или новее</string>
    <string name="pref__input_feedback__haptic_feat_key_press__label" comment="Preference title">Вибрация нажатия кнопки</string>
    <string name="pref__input_feedback__haptic_feat_key_long_press__label" comment="Preference title">Вибрация долгого нажатия кнопки</string>
    <string name="pref__input_feedback__haptic_feat_key_repeated_action__label" comment="Preference title">Вибрация автоповтора ввода кнопки</string>
    <string name="pref__input_feedback__haptic_feat_gesture_swipe__label" comment="Preference title">Вибрация жестов смахивания</string>
    <string name="pref__input_feedback__haptic_feat_gesture_moving_swipe__label" comment="Preference title">Вибрация жестов перетаскивания</string>
    <string name="pref__input_feedback__any_feat_key_press__summary" comment="Preference summary">например кнопки, служебные клавиши или вкладки с эмодзи</string>
    <string name="pref__input_feedback__any_feat_key_long_press__summary" comment="Preference summary">например, всплывающее меню</string>
    <string name="pref__input_feedback__any_feat_key_repeated_action__summary" comment="Preference summary">например, кнопка «удалить»</string>
    <string name="pref__input_feedback__any_feat_gesture_swipe__summary" comment="Preference summary">не реализовано</string>
    <string name="pref__input_feedback__any_feat_gesture_moving_swipe__summary" comment="Preference summary">например, жест перемещения курсора</string>
    <string name="settings__keyboard__title" comment="Title of Keyboard preferences screen">Клавиатура</string>
    <string name="pref__keyboard__number_row__label" comment="Preference title">Цифровой ряд</string>
    <string name="pref__keyboard__number_row__summary" comment="Preference summary">Показывать цифровой над символьной раскладкой</string>
    <string name="pref__keyboard__hinted_number_row_mode__label" comment="Preference title">Подсказка в цифровом ряду</string>
    <string name="pref__keyboard__hinted_symbols_mode__label" comment="Preference title">Подсказки символов</string>
    <string name="pref__keyboard__utility_key_enabled__label" comment="Preference title">Показать служебную клавишу</string>
    <string name="pref__keyboard__utility_key_enabled__summary" comment="Preference summary">Показывает настраиваемую служебную клавишу рядом с пробелом</string>
    <string name="pref__keyboard__utility_key_action__label" comment="Preference title">Действие служебной клавиши</string>
    <string name="pref__keyboard__space_bar_mode__label" comment="Preference title">Надпись на пробеле</string>
    <string name="pref__keyboard__capitalization_behavior__label" comment="Preference title">Поведение кнопки изменения регистра</string>
    <string name="pref__keyboard__font_size_multiplier__label" comment="Preference title">Множитель размера шрифта</string>
    <string name="pref__keyboard__group_layout__label" comment="Preference group title">Внешний вид</string>
    <string name="pref__keyboard__one_handed_mode__label" comment="Preference title">Управление одной рукой</string>
    <string name="pref__keyboard__one_handed_mode_scale_factor__label" comment="Preference title">Ширина в режиме одной руки</string>
    <string name="pref__keyboard__landscape_input_ui_mode__label" comment="Preference value">На весь экран в альбомном режиме</string>
    <string name="pref__keyboard__height_factor__label" comment="Preference title">Высота клавиатуры</string>
    <string name="pref__keyboard__key_spacing__label" comment="Preference title">Расстояние между кнопками</string>
    <string name="pref__keyboard__bottom_offset__label" comment="Preference title">Отступ снизу</string>
    <string name="pref__keyboard__group_keypress__label" comment="Preference group title">Нажатие клавиши</string>
    <string name="pref__keyboard__popup_enabled__label" comment="Preference title">Превью символов</string>
    <string name="pref__keyboard__popup_enabled__summary" comment="Preference summary">Показывать всплывающую подсказку при нажатии кнопки</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__label" comment="Preference title">Акценты включают всплывающие окна с символами</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__summary" comment="Preference summary">Добавление всплывающих окон символов к акцентам макета по умолчанию</string>
    <string name="pref__keyboard__long_press_delay__label" comment="Preference title">Длительность долгого нажатия</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__label" comment="Preference title">Пробел переключает на буквы</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__summary" comment="Preference summary">Будучи в символьной или цифровой раскладке, автоматически переходить обратно на ввод букв</string>
    <string name="pref__keyboard__incognito_indicator__label" comment="Preference title">Индикатор инкогнито</string>
    <!-- Smartbar strings -->
    <string name="settings__smartbar__title" comment="Title of Smartbar screen">Умная панель</string>
    <string name="pref__smartbar__enabled__label" comment="Preference title">Активировать умную панель</string>
    <string name="pref__smartbar__enabled__summary" comment="Preference summary">Будет показано над клавиатурой</string>
    <string name="pref__smartbar__layout__label" comment="Preference title">Раскладка</string>
    <string name="pref__smartbar__group_layout_specific__label" comment="Preference group title">Параметры, специфичные для раскладки</string>
    <string name="pref__smartbar__flip_toggles__label" comment="Preference title">Поменять местами кнопки переключения</string>
    <string name="pref__smartbar__flip_toggles__summary" comment="Preference summary">Поменять местами переключатели строки действий</string>
    <string name="pref__smartbar__shared_actions_auto_expand_collapse__label" comment="Preference title">Автосворачивание/разворачивание</string>
    <string name="pref__smartbar__shared_actions_auto_expand_collapse__summary" comment="Preference summary">Автоматическое разворачивание/сворачивание основной строки действия в зависимости от текущего состояния</string>
    <string name="pref__smartbar__extended_actions_placement__label" comment="Preference title">Размещение ряда действий</string>
    <!-- Typing strings -->
    <string name="settings__typing__title" comment="Title of Typing screen">Набор текста</string>
    <string name="pref__suggestion__title" comment="Preference group title">Подсказки</string>
    <string name="pref__suggestion__enabled__label" comment="Preference title">Показывать предложения</string>
    <string name="pref__suggestion__enabled__summary" comment="Preference summary">Предлагать подсказки при наборе текста</string>
    <string name="pref__suggestion__display_mode__label" comment="Preference title">Режим отображения предложений</string>
    <string name="pref__suggestion__block_possibly_offensive__label" comment="Preference title">Блокировать возможные оскорбительные слова</string>
    <string name="pref__suggestion__api30_inline_suggestions_enabled__summary" comment="Preference summary">Показывать встроенные предложения, предоставляемые службами автозаполнения</string>
    <string name="pref__suggestion__incognito_mode__label" comment="Label of Incognito mode preference in Typing">Режим инкогнито</string>
    <string name="pref__correction__title" comment="Preference group title">Исправления</string>
    <string name="pref__correction__auto_capitalization__label" comment="Preference title">Заглавные автоматически</string>
    <string name="pref__correction__auto_capitalization__summary" comment="Preference summary">Использовать заглавные буквы в зависимости от текущего контекста ввода</string>
    <string name="pref__correction__auto_space_punctuation__label" comment="Preference title">Авто-пробел после знаков препинания</string>
    <string name="pref__correction__auto_space_punctuation__summary" comment="Preference summary">Автоматически вставлять пробел после знака препинания</string>
    <string name="pref__correction__remember_caps_lock_state__label" comment="Preference title">Запоминать состояние Caps Lock</string>
    <string name="pref__correction__remember_caps_lock_state__summary" comment="Preference summary">Caps Lock будет оставаться при переходе на другое текстовое поле</string>
    <string name="pref__correction__double_space_period__label" comment="Preference title">Замена двойного пробела</string>
    <string name="pref__correction__double_space_period__summary" comment="Preference summary">При двойном нажатии на пробел вставляется точка, за которой следует пробел</string>
    <string name="pref__spelling__title" comment="Preference group title">Орфография</string>
    <string name="pref__spelling__active_spellchecker__summary_disabled">Отключено для всей системы. В случае написания слов неправильно в текстовых полях не появятся красные подчёркивания. Нажмите, чтобы изменить.</string>
    <string name="pref__spelling__active_spellchecker__summary_none">Не настроена служба проверки орфографии текстовых полей. Нажмите, чтобы изменить.</string>
    <string name="pref__spelling__language_mode__label" comment="Label of Language mode pref">Язык</string>
    <string name="pref__spelling__use_contacts__label" comment="Label of Use contact list pref">Использовать имена из контактов</string>
    <string name="pref__spelling__use_contacts__summary" comment="Summary of Use contact list pref">Поиск имен в списке контактов</string>
    <string name="pref__spelling__use_udm_entries__label" comment="Label of Use user dictionary entries pref">Использовать записи из словаря пользователя</string>
    <string name="pref__spelling__use_udm_entries__summary" comment="Summary of Use user dictionary entries pref">Поиск записей в пользовательских словарях</string>
    <string name="settings__dictionary__title" comment="Title of the User dictionaries screen">Пользовательские словари</string>
    <string name="pref__dictionary__enable_system_user_dictionary__label" comment="Preference title">Использовать системный словарь пользователя</string>
    <string name="pref__dictionary__enable_system_user_dictionary__summary" comment="Preference summary">Предлагать слова, находящиеся в системном словаре пользователя</string>
    <string name="pref__dictionary__manage_system_user_dictionary__label" comment="Preference title">Управление системным словарём пользователя</string>
    <string name="pref__dictionary__manage_system_user_dictionary__summary" comment="Preference summary">Добавление, просмотр и удаление записей в системном пользовательском словаре</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__label" comment="Preference title">Использовать внутренний словарь пользователя</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__summary" comment="Preference summary">Предлагать слова, находящиеся во внутреннем словаре пользователя</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__label" comment="Preference title">Управление внутренним словарём пользователя</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__summary" comment="Preference summary">Добавление, просмотр и удаление записей во внутреннем пользовательском словаре</string>
    <string name="settings__udm__title_floris" comment="Title of the User Dictionary Manager activity for internal">Внутренний словарь пользователя</string>
    <string name="settings__udm__title_system" comment="Title of the User Dictionary Manager activity for system">Системный словарь пользователя</string>
    <string name="settings__udm__no_words_in_dictionary" comment="String to show if no words are present in the dictionary">Этот пользовательский словарь не содержит слов.</string>
    <string name="settings__udm__word_summary_freq" comment="Summary label for a word entry. The decimal placeholder inserts the frequency for the word it summarizes.">Частота: {freq}</string>
    <string name="settings__udm__word_summary_freq_shortcut" comment="Summary label for a word entry. The first placeholder inserts the frequency for the word it summarizes, the second placeholder the shortcut defined.">Частота: {freq} | Сокращение: {shortcut}</string>
    <string name="settings__udm__all_languages" comment="Label of the For all languages entry in the language list">Для всех языков</string>
    <string name="settings__udm__open_system_manager_ui" comment="Label of the Open system manager UI menu option">Открыть системный менеджер</string>
    <string name="settings__udm__dictionary_import_success" comment="Message for dictionary import success">Словарь пользователя успешно импортирован!</string>
    <string name="settings__udm__dictionary_export_success" comment="Message for dictionary export success">Словарь пользователя успешно экспортирован!</string>
    <string name="settings__udm__dialog__title_add" comment="Label for the title (when in adding mode) in the user dictionary add/edit dialog">Добавить слово</string>
    <string name="settings__udm__dialog__title_edit" comment="Label for the title (when in editing mode) in the user dictionary add/edit dialog">Изменить слово</string>
    <string name="settings__udm__dialog__word_label" comment="Label for the word in the user dictionary add/edit dialog">Слово</string>
    <string name="settings__udm__dialog__word_error_empty" comment="Error label for the word in the user dictionary add/edit dialog">Введите слово</string>
    <string name="settings__udm__dialog__word_error_invalid" comment="Error label for the word in the user dictionary add/edit dialog">Введите соответствие слов {regex}</string>
    <string name="settings__udm__dialog__freq_label" comment="Label for the frequency in the user dictionary add/edit dialog. The two decimal placeholders are the minimum and maximum frequency, both inclusive.">Частота (между {f_min} и {f_max})</string>
    <string name="settings__udm__dialog__freq_error_empty" comment="Error label for the frequency in the user dictionary add/edit dialog">Введите значение частоты</string>
    <string name="settings__udm__dialog__freq_error_invalid" comment="Error label for the frequency in the user dictionary add/edit dialog">Введите корректное число в указанных пределах</string>
    <string name="settings__udm__dialog__shortcut_label" comment="Label for the shortcut in the user dictionary add/edit dialog">Сокращение (необязательно)</string>
    <string name="settings__udm__dialog__shortcut_error_invalid" comment="Error label for the shortcut in the user dictionary add/edit dialog">Введите сочетание клавиш {regex}</string>
    <string name="settings__udm__dialog__locale_label" comment="Label for the language code in the user dictionary add/edit dialog">Код языка (необязательно)</string>
    <string name="settings__udm__dialog__locale_error_invalid" comment="Error label for the language code in the user dictionary add/edit dialog">Указанный код языка не соответствует ожидаемому синтаксису. Код должен быть либо только языковым (например, ru), либо языковым с указанием страны (например, ru_RU), либо языковым с указанием страны и письменности (например, ru_RU-script).</string>
    <string name="settings__gestures__title" comment="Title of Gestures screen">Жесты и непрерывный ввод</string>
    <string name="pref__glide__title" comment="Preference group title">Непрерывный ввод</string>
    <string name="pref__glide__enabled__label" comment="Preference title">Включить непрерывный ввод</string>
    <string name="pref__glide__enabled__summary" comment="Preference summary">Введите слово, проведя пальцем по буквам</string>
    <string name="pref__glide__show_trail__label" comment="Preference title">Показывать непрерывный след</string>
    <string name="pref__glide__show_trail__summary" comment="Preference summary">Исчезает после каждого слова</string>
    <string name="pref__glide_trail_fade_duration">Время исчезания непрерывного следа</string>
    <string name="pref__glide_preview_refresh_delay">Задержка обновления предварительного просмотра</string>
    <string name="pref__glide__show_preview">Предварительный просмотр при непрерывном вводе</string>
    <string name="pref__glide__immediate_backspace_deletes_word__label">Всегда удалять слово</string>
    <string name="pref__glide__immediate_backspace_deletes_word__summary">Нажатие кнопки \"Удалить\" после непрерывного ввода удаляет слово целиком</string>
    <string name="pref__gestures__general_title" comment="Preference group title">Общие жесты</string>
    <string name="pref__gestures__space_bar_title" comment="Preference group title">Жесты пробела</string>
    <string name="pref__gestures__other_title" comment="Preference group title">Другие жесты / Пороговые значения жестов</string>
    <string name="pref__gestures__swipe_up__label" comment="Preference title">Жест вверх</string>
    <string name="pref__gestures__swipe_down__label" comment="Preference title">Жест вниз</string>
    <string name="pref__gestures__swipe_left__label" comment="Preference title">Жест влево</string>
    <string name="pref__gestures__swipe_right__label" comment="Preference title">Жест вправо</string>
    <string name="pref__gestures__space_bar_swipe_up__label" comment="Preference title">Жест вверх на пробеле</string>
    <string name="pref__gestures__space_bar_swipe_left__label" comment="Preference title">Жест влево на пробеле</string>
    <string name="pref__gestures__space_bar_swipe_right__label" comment="Preference title">Жест вправо на пробеле</string>
    <string name="pref__gestures__space_bar_long_press__label" comment="Preference title">Долгое нажатие пробела</string>
    <string name="pref__gestures__delete_key_swipe_left__label" comment="Preference title">Жест влево на кнопке удаления</string>
    <string name="pref__gestures__delete_key_long_press__label" comment="Preference title">Долгое нажатие «Удалить»</string>
    <string name="pref__gestures__swipe_velocity_threshold__label" comment="Preference title">Порог скорости жеста</string>
    <string name="pref__gestures__swipe_distance_threshold__label" comment="Preference title">Порог срабатывания жеста</string>
    <string name="settings__other__title" comment="Title of Other settings">Прочее</string>
    <string name="pref__other__settings_theme__label" comment="Label of Settings theme preference in Other">Настройки темы</string>
    <string name="pref__other__settings_theme__auto_amoled" comment="Possible value of Settings theme preference in Other">Как в системе (AMOLED)</string>
    <string name="pref__other__settings_theme__light" comment="Possible value of Settings theme preference in Other">Светлая</string>
    <string name="pref__other__settings_theme__dark" comment="Possible value of Settings theme preference in Other">Темная</string>
    <string name="pref__other__settings_theme__amoled_dark" comment="Possible value of Settings theme preference in Other">Темная AMOLED</string>
    <string name="pref__other__settings_accent_color__label" comment="Label of accent color preference in Other">        Настройки цвета акцента
    </string>
    <string name="pref__other__settings_language__label" comment="Label of Settings language preference in Other">Настройки языка</string>
    <string name="pref__other__show_app_icon__label" comment="Label of Show app icon preference in Other">Показывать значок приложения а лаунчере</string>
    <string name="pref__other__show_app_icon__summary_atleast_q" comment="Summary of Show app icon preference in Other for Android 10+">Всегда включено на Android 10+ из-за системных ограничений</string>
    <!-- About UI strings -->
    <string name="about__title" comment="Title of About activity">О приложении</string>
    <string name="about__app_icon_content_description" comment="Content description of app icon in About">Значок приложения FlorisBoard</string>
    <string name="about__view_licenses" comment="Label of View licenses button in About">Лицензии открытого ПО</string>
    <string name="about__view_privacy_policy" comment="Label of View privacy policy button in About">Конфиденциальность</string>
    <string name="about__view_source_code" comment="Label of View source code button in About">Исходный код</string>
    <string name="about__license__title" comment="Title of Open-source licenses dialog">Лицензии открытого ПО</string>
    <string name="about__version__title" comment="Preference title">Версия</string>
    <string name="about__version_copied__title" comment="Title of the toast for copying the version string">Номер версии скопирован в буфер обмена</string>
    <string name="about__version_copied__error" comment="Title of the error toast for copying the version string">Произошла ошибка: {error_message}</string>
    <string name="about__changelog__title" comment="Preference title">История изменений</string>
    <string name="about__changelog__summary" comment="Preference summary">Что нового</string>
    <string name="about__repository__title" comment="Preference title">Репозиторий (GitHub)</string>
    <string name="about__repository__summary" comment="Preference summary">Исходный код, обсуждения, проблемы и информация</string>
    <string name="about__privacy_policy__title" comment="Preference title">Конфиденциальность</string>
    <string name="about__privacy_policy__summary" comment="Preference summary">Политика конфиденциальности этого проекта</string>
    <string name="about__project_license__title" comment="Preference title">Лицензия проекта</string>
    <string name="about__project_license__summary" comment="Preference summary">FlorisBoard выпускается под лицензией {license_name}</string>
    <string name="about__project_license__error_license_text_failed" comment="Error text for license text loading failure">Ошибка: невозможно загрузить текст лицензии.\nПричина: {error_message}</string>
    <string name="about__project_license__error_reason_asset_manager_null" comment="Error text if asset manager is null">Ссылка на диспетчер активов не определена</string>
    <string name="about__third_party_licenses__title" comment="Preference title">Лицензии третьих сторон</string>
    <string name="about__third_party_licenses__summary" comment="Preference summary">Лицензии сторонних библиотек, используемых в данном приложении</string>
    <!-- Setup UI strings -->
    <string name="setup__title" comment="Title of Setup">Добро пожаловать!</string>
    <string name="setup__intro_message" comment="Short intro message welcoming new users">Спасибо за установку {app_name}! Это краткое руководство проведёт вас через необходимые шаги для использования {app_name} на вашем устройстве.</string>
    <string name="setup__footer__privacy_policy" comment="Privacy policy label for URL">Конфиденциальность</string>
    <string name="setup__footer__repository" comment="Repository label for URL">Исходный код</string>
    <string name="setup__enable_ime__title">Включите {app_name}</string>
    <string name="setup__enable_ime__description">Android требует, чтобы каждая пользовательская клавиатура была включена отдельно, прежде чем вы сможете её использовать. Откройте системные <i>Настройки языка и ввода</i> и там включите \"{app_name}\".</string>
    <string name="setup__enable_ime__open_settings_btn">Открыть системные настройки</string>
    <string name="setup__select_ime__title">Выберите {app_name}</string>
    <string name="setup__select_ime__description">{app_name} активирована. Чтобы ею пользоваться, переключитесь на {app_name} в окне способа ввода!</string>
    <string name="setup__select_ime__switch_keyboard_btn">Переключить клавиатуру</string>
    <string name="setup__grant_notification_permission__title">Разрешить уведомления об ошибках</string>
    <string name="setup__grant_notification_permission__description">Начиная с Android 13, приложения должны
        запрашивать разрешение для отправки уведомлений. Florisboard будет использовать это только для отправки отчёта о сбое при вылете приложения.
        Данное разрешение всегда можно отозвать в настройках устройства.
    </string>
    <string name="setup__grant_notification_permission__btn">Предоставить разрешение</string>
    <string name="setup__finish_up__title">Готово</string>
    <string name="setup__finish_up__description_p1">Клавиатура {app_name} активирована и готова к дальнейшей настройке.</string>
    <string name="setup__finish_up__description_p2">Если столкнетесь с какими-либо трудностями, ошибками, сбоями или просто захотите предложить идею, загляните в репозиторий проекта в разделе «О программе»!</string>
    <string name="setup__finish_up__finish_btn">Перейти к настройке</string>
    <!-- Back up & Restore -->
    <string name="backup_and_restore__title">Резервное копирование и восстановление</string>
    <string name="backup_and_restore__back_up__title">Резервирование данных</string>
    <string name="backup_and_restore__back_up__summary">Создание резервной копии настроек</string>
    <string name="backup_and_restore__back_up__destination">Выберите место хранения</string>
    <string name="backup_and_restore__back_up__destination_file_sys">Локальное хранилище</string>
    <string name="backup_and_restore__back_up__destination_share_intent">Стороннее приложение через меню \'Поделиться\'</string>
    <string name="backup_and_restore__back_up__files">Выберите сохраняемые данные</string>
    <string name="backup_and_restore__back_up__files_jetpref_datastore">Настройки</string>
    <string name="backup_and_restore__back_up__files_ime_keyboard">Клавиатурные расширения</string>
    <string name="backup_and_restore__back_up__files_ime_spelling">Расширения для правописания / словари</string>
    <string name="backup_and_restore__back_up__files_ime_theme">Расширения оформления</string>
    <string name="backup_and_restore__back_up__files_clipboard_history">История буфера обмена</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_text_items">Текст</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_image_items">Изображения</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_video_items">Видео</string>
    <string name="backup_and_restore__back_up__success">Резервное копирование выполнено!</string>
    <string name="backup_and_restore__back_up__failure">Невозможно создать резервную копию: {error_message}</string>
    <string name="backup_and_restore__restore__title">Восстановление данных</string>
    <string name="backup_and_restore__restore__summary">Восстановление настроек из резервной копии</string>
    <string name="backup_and_restore__restore__files">Выберите восстанавливаемые данные</string>
    <string name="backup_and_restore__restore__metadata">Выберите файл резервной копии</string>
    <string name="backup_and_restore__restore__metadata_warn_different_version">Этот файл резервной копии был создан в версии приложения, отличной от текущей поддерживаемой. Имейте в виду, что могут возникнуть незначительные проблемы или некоторые настройки могут быть восстановлены неправильно из-за различий в функционале.</string>
    <string name="backup_and_restore__restore__metadata_warn_different_vendor">Файл резервной копии был создан в стороннем приложении, которое поддерживается не полностью. Возможна потеря данных, восстанавливайте на свой страх и риск!</string>
    <string name="backup_and_restore__restore__metadata_error_invalid_metadata">Этот файл резервной копии содержит недопустимые метаданные. Либо он повреждён, либо неправильно изменён. Восстановление из этого файла невозможно, выберите другой.</string>
    <string name="backup_and_restore__restore__metadata_error_nothing_to_restore">Этот файл резервной копии не содержит данных для восстановления, выберите другой.</string>
    <string name="backup_and_restore__restore__mode">Режим восстановления</string>
    <string name="backup_and_restore__restore__mode_merge">Слияние с текущими данными</string>
    <string name="backup_and_restore__restore__mode_erase_and_overwrite">Стереть и перезаписать текущие данные</string>
    <string name="backup_and_restore__restore__success">Данные восстановлены!</string>
    <string name="backup_and_restore__restore__failure">Невозможно восстановить данные: {error_message}</string>
    <!-- Crash Dialog strings -->
    <string name="crash_dialog__title" comment="Title of crash dialog">Сообщение об ошибке FlorisBoard</string>
    <string name="crash_dialog__description" comment="Description of crash dialog">Извините за неудобства, но в FlorisBoard произошёл сбой из-за непредвиденной ошибки.</string>
    <string name="crash_dialog__report_instructions" comment="Issue tracker report instructions for the crash dialog. The %s placeholder is the name of the crash report template and always in English/LTR.">Если хотите сообщить об этой ошибке, сначала проверьте трекер проблем на GitHub, не сообщалось ли уже о вашем сбое.\nЕсли нет, скопируйте сгенерированный журнал ошибки и откройте новую проблему. Используйте шаблон \"%s\" и заполните описание, шаги для воспроизведения ошибки и вставьте сгенерированный журнал в конце. Это поможет сделать FlorisBoard лучше и стабильнее для всех. Спасибо!</string>
    <string name="crash_dialog__copy_to_clipboard" comment="Label of Copy to clipboard button in crash dialog">Копировать в системный буфер обмена</string>
    <string name="crash_dialog__copy_to_clipboard_success" comment="Label of Copy to clipboard success message in crash dialog">Скопировано в системный буфер обмена</string>
    <string name="crash_dialog__copy_to_clipboard_failure" comment="Label of Copy to clipboard failure message in crash dialog">Невозможно скопировать в системный буфер обмена: компонент менеджера буфера обмена не найден</string>
    <string name="crash_dialog__open_issue_tracker" comment="Label of Open issue tracker button in crash dialog">Открыть трекер проблем (GitHub)</string>
    <string name="crash_dialog__close" comment="Label of Close button in crash dialog">Закрыть</string>
    <string name="crash_notification_channel__title" comment="Title of crash notification channel">Отчёты об ошибках FlorisBoard</string>
    <string name="crash_once_notification__title" comment="Title of the notification for a single crash">FlorisBoard перестал работать…</string>
    <string name="crash_once_notification__body" comment="Body of the notification for a single crash">Нажмите для подробностей</string>
    <string name="crash_multiple_notification__title" comment="Title of the notification for consecutive crashes">Кажется, FlorisBoard постоянно перестаёт работать…</string>
    <string name="crash_multiple_notification__body" comment="Body of the notification for consecutive crashes">Возвращение к предыдущей клавиатуре, чтобы остановить бесконечный цикл сбоя. Нажмите, чтобы просмотреть детали об ошибке.</string>
    <!-- Clipboard strings -->
    <string name="clipboard__header_title">Буфер обмена</string>
    <string name="clipboard__disabled__title">История буфера обмена отключена</string>
    <string name="clipboard__disabled__message">История буфера обмена {app_name} позволяет быстро сохранять и получать доступ к тексту и изображениям, которые вы копируете, с возможностью закреплять элементы, настраивать автоматическую очистку и устанавливать лимит количества записей.</string>
    <string name="clipboard__disabled__enable_button">Включить историю буфера обмена</string>
    <string name="clipboard__empty__title">Буфер обмена пуст</string>
    <string name="clipboard__empty__message">Как только вы скопируете текст или изображение, они появятся здесь.</string>
    <string name="clipboard__locked__title">Буфер обмена заблокирован</string>
    <string name="clipboard__locked__message">Для доступа к истории буфера обмена необходимо разблокировать устройство.</string>
    <string name="clipboard__group_pinned">Закреплено</string>
    <string name="clipboard__group_recent">Недавнее</string>
    <string name="clipboard__group_other">Разное</string>
    <string name="clipboard__item_description_email">Эл. почта</string>
    <string name="clipboard__item_description_url">Ссылка</string>
    <string name="clipboard__item_description_phone">Телефон</string>
    <string name="clip__clear_history">Очистить историю</string>
    <string name="clip__unpin_item">Открепить</string>
    <string name="clip__pin_item">Закрепить</string>
    <string name="clip__delete_item">Удалить</string>
    <string name="clip__paste_item">Вставить</string>
    <string name="clip__back_to_text_input">Вернуться к вводу текста</string>
    <string name="clip__cant_paste">Приложение не позволяет вставлять такие данные.</string>
    <string name="clipboard__cleared_primary_clip">Очищена основная запись</string>
    <string name="clipboard__cleared_history">Очищена история</string>
    <string name="clipboard__cleared_full_history">Очищена полная история</string>
    <string name="clipboard__confirm_clear_history__message">Очистить историю буфера обмена?</string>
    <string name="settings__clipboard__title">Буфер обмена</string>
    <string name="pref__clipboard__use_internal_clipboard__label">Использовать внутренний буфер обмена</string>
    <string name="pref__clipboard__use_internal_clipboard__summary">Использовать внутренний буфер обмена вместо системного</string>
    <string name="pref__clipboard__sync_from_system_clipboard__label">Единый буфер обмена</string>
    <string name="pref__clipboard__sync_from_system_clipboard__summary">Изменения в буфере обмена системы отражаются в буфере Floris</string>
    <string name="pref__clipboard__sync_to_system_clipboard__label">Синхронизировать с системным буфером обмена</string>
    <string name="pref__clipboard__sync_to_system_clipboard__summary">Буфер обмена Floris так же обновляет системный буфер обмена</string>
    <string name="pref__clipboard__group_clipboard_suggestion__label">Предлагать буфер обмена</string>
    <string name="pref__clipboard__suggestion_enabled__label" comment="Preference title">Предлагать содержимое буфера обмена</string>
    <string name="pref__clipboard__suggestion_enabled__summary" comment="Preference summary">Предлагать ранее скопированное содержимое буфера обмена</string>
    <string name="pref__clipboard__suggestion_timeout__label" comment="Preference title; Translators: This should form a sentence together with `pref__clipboard__suggestion_timeout__summary` and is the first part">Ограничить предложения буфера обмена до</string>
    <string name="pref__clipboard__suggestion_timeout__summary" comment="Preference summary; Translators: This should form a sentence together with `pref__clipboard__suggestion_timeout__label` and is the second part">Элементы, скопированные за последние {v} с</string>
    <string name="pref__clipboard__group_clipboard_history__label">История буфера обмена</string>
    <string name="pref__clipboard__enable_clipboard_history__label">Включить историю буфера обмена</string>
    <string name="pref__clipboard__enable_clipboard_history__summary">Сохранение записей буфера обмена для быстрого доступа</string>
    <string name="pref__clipboard__clean_up_old__label">Очистить старые элементы</string>
    <string name="pref__clipboard__clean_up_after__label">Очистить старые элементы через</string>
    <string name="pref__clipboard__auto_clean_sensitive__label">Автоматическая очистка чувствительных элементов</string>
    <string name="pref__clipboard__auto_clean_sensitive_after__label">Автоматическая очистка чувствительных элементов спустя</string>
    <string name="pref__clipboard__limit_history_size__label">Ограничить размер истории</string>
    <string name="pref__clipboard__max_history_size__label">Максимальный размер истории</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__label">Очистка основного клипа влияет на историю</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__summary">При очистке основного клипа также удаляется последняя запись в истории</string>
    <string name="send_to_clipboard__unknown_error">Неизвестная ошибка. Попробуйте еще раз!</string>
    <string name="send_to_clipboard__type_not_supported_error">Данный формат файла не поддерживается.</string>
    <string name="send_to_clipboard__android_version_to_old_error">Требуется более новая версия Android для этой функции.
    
    </string>
    <string name="send_to_clipboard__description__copied_image_to_clipboard">Изображение ниже скопировано в буфер обмена.</string>
    <!-- Devtools strings -->
    <string name="devtools__title" comment="Title of Devtools screen. Translators: treat this string as 'Developer tools' for translation, except a similar short term is available for your language.">Инструменты разработчика</string>
    <string name="devtools__enabled__label" comment="Label of Enable developer tools in Devtools">Включить инструменты разработчика</string>
    <string name="devtools__enabled__summary" comment="Summary of Enable developer tools in Devtools">Инструменты, специально разработанные для отладки и устранения проблем</string>
    <string name="devtools__show_primary_clip__label" comment="Label of Show primary clip in Devtools">Показать основную запись</string>
    <string name="devtools__show_primary_clip__summary" comment="Summary of Show primary clip in Devtools">Наложение текущей основной записи буфера обмена</string>
    <string name="devtools__show_input_state_overlay__label" comment="Label of Show input cache overlay in Devtools">Показывать состояние ввода наложением</string>
    <string name="devtools__show_input_state_overlay__summary" comment="Summary of Show input cache overlay in Devtools">Показывать наложением текущее состояние ввода для отладки</string>
    <string name="devtools__show_spelling_overlay__label" comment="Label of Show spelling overlay in Devtools">Показывать орфографию наложением</string>
    <string name="devtools__show_spelling_overlay__summary" comment="Summary of Show spelling overlay in Devtools">Показывать наложением текущие результаты проверки орфографии для отладки</string>
    <string name="devtools__show_inline_autofill_overlay__label">Показать встроенное окно автозаполнения</string>
    <string name="devtools__show_inline_autofill_overlay__summary">Отображает текущие результаты автозаполнения строки для отладки</string>
    <string name="devtools__show_key_touch_boundaries__label" comment="Label of Show key touch boundaries in Devtools">Показывать границы нажатия клавиш</string>
    <string name="devtools__show_key_touch_boundaries__summary" comment="Summary of Show key touch boundaries in Devtools">Обводить границы нажатия клавиш красным контуром</string>
    <string name="devtools__show_drag_and_drop_helpers__label" comment="Label of Show drag and drop helpers in Devtools">Показывать вспомогательные элементы перетаскивания</string>
    <string name="devtools__show_drag_and_drop_helpers__summary" comment="Summary of Show drag and drop helpers in Devtools">Показывать невидимые вспомогательные элементы на экранах перетаскивания для отладки</string>
    <string name="devtools__clear_udm_internal_database__label" comment="Label of Clear internal user dictionary database in Devtools">Очистить внутренний словарь пользователя</string>
    <string name="devtools__clear_udm_internal_database__summary" comment="Summary of Clear internal user dictionary database in Devtools">Удаление всех слов из базы внутреннего пользовательского словаря</string>
    <string name="devtools__reset_flag__label" comment="Label of Reset flag preferences in Devtools">Сбросить флаг \"{flag_name}\"</string>
    <string name="devtools__reset_flag_is_ime_set_up__summary" comment="Summary of Reset is IME set up flag in Devtools">Действие отладки для повторного отображения экрана настройки</string>
    <string name="devtools__test_crash_report__label" comment="Label of Test Crash Report in Devtools">Тест отчёта об ошибке</string>
    <string name="devtools__test_crash_report__summary" comment="Summary of Test Crash Report in Devtools">Отладочное действие для преднамеренного создания сбоя</string>
    <string name="devtools__group_android__title" comment="Title of Android group in Devtools">Системные инструменты Android</string>
    <string name="devtools__android_settings_global__title" comment="Title of Android settings (global) screen">Глобальные настройки Android</string>
    <string name="devtools__android_settings_secure__title" comment="Title of Android settings (secure) screen">Настройки безопасности Android</string>
    <string name="devtools__android_settings_system__title" comment="Title of Android settings (system) screen">Системные настройки Android</string>
    <string name="devtools__android_locales__title" comment="Title of Android locales screen">Языки системы</string>
    <string name="devtools__debuglog__title">Журнал отладки</string>
    <string name="devtools__debuglog__copied_to_clipboard">Журнал отладки скопирован в буфер обмена</string>
    <string name="devtools__debuglog__copy_log">Скопировать журнал</string>
    <string name="devtools__debuglog__copy_for_github">Скопировать журнал (формат GitHub)</string>
    <string name="devtools__debuglog__loading">Загрузка…</string>
    <!-- Extension strings -->
    <string name="ext__home__title">Расширения и дополнения</string>
    <string name="ext__list__ext_theme">Расширения темы</string>
    <string name="ext__list__ext_keyboard">Расширения клавиатуры</string>
    <string name="ext__list__ext_languagepack">Языковые пакеты</string>
    <string name="ext__meta__authors">Авторы</string>
    <string name="ext__meta__components">Встроенные компоненты</string>
    <string name="ext__meta__components_theme">Встроенные темы</string>
    <string name="ext__meta__components_language_pack">Связанные языковые пакеты</string>
    <string name="ext__meta__components_none_found">В этом архиве расширения нет встроенных компонентов.</string>
    <string name="ext__meta__description">Описание</string>
    <string name="ext__meta__homepage">Главная страница</string>
    <string name="ext__meta__id">ID</string>
    <string name="ext__meta__issue_tracker">Известные проблемы</string>
    <string name="ext__meta__keywords">Ключевые слова</string>
    <string name="ext__meta__label">Метка</string>
    <string name="ext__meta__license">Лицензия</string>
    <string name="ext__meta__maintainers">Сопровождающие</string>
    <string name="ext__meta__maintainers_by">От: {maintainers}</string>
    <string name="ext__meta__title">Заголовок</string>
    <string name="ext__meta__version">Версия</string>
    <string name="ext__error__not_found_title">Расширение не найдено</string>
    <string name="ext__error__not_found_description">Не найдено расширения с ID \"{id}\"</string>
    <string name="ext__editor__title_create_any">Создать расширение</string>
    <string name="ext__editor__title_create_keyboard">Создать расширение клавиатуры</string>
    <string name="ext__editor__title_create_theme">Создать расширение темы</string>
    <string name="ext__editor__title_edit_any">Редактировать расширение</string>
    <string name="ext__editor__title_edit_keyboard">Редактировать расширение клавиатуры</string>
    <string name="ext__editor__title_edit_theme">Редактировать расширение темы</string>
    <string name="ext__editor__metadata__title">Управление метаданными</string>
    <string name="ext__editor__metadata__title_invalid">Неправильные метаданные</string>
    <string name="ext__editor__metadata__message_invalid">Неправильные метаданные для этого расширения, проверьте редактор метаданных для получения подробной информации!</string>
    <string name="ext__editor__dependencies__title">Управление зависимостями</string>
    <string name="ext__editor__files__title">Управление файлами архивов</string>
    <string name="ext__editor__create_component__title">Создать компонент</string>
    <string name="ext__editor__create_component__title_theme">Создать тему</string>
    <string name="ext__editor__create_component__from_empty">Пустой</string>
    <string name="ext__editor__create_component__from_existing">Из существующей</string>
    <string name="ext__editor__create_component__from_empty_warning">Создание и настройка пустого компонента может быть сложной задачей, если вы новичок в {app_name} или не знакомы со спецификой. В таком случае лучше скопируйте существующий компонент и измените его по своему вкусу.</string>
    <string name="ext__editor__edit_component__title">Редактировать компонент</string>
    <string name="ext__editor__edit_component__title_theme">Редактировать компонент темы</string>
    <string name="ext__export__success">Расширение успешно экспортировано!</string>
    <string name="ext__export__failure">Ошибка экспорта расширения: {error_message}</string>
    <string name="ext__import__success">Расширение успешно импортировано!</string>
    <string name="ext__import__failure">Ошибка импорта расширения: {error_message}</string>
    <string name="ext__import__ext_any" comment="Title of Importer screen for import of any supported FlorisBoard extension">Импорт расширения</string>
    <string name="ext__import__ext_keyboard" comment="Title of Importer screen for keyboard extension import">Импорт расширения клавиатуры</string>
    <string name="ext__import__ext_theme" comment="Title of Importer screen for theme extension import">Импорт расширения темы</string>
    <string name="ext__import__ext_languagepack" comment="Title of Importer screen for language pack extension import">Импорт расширения языкового пакета</string>
    <string name="ext__import__file_skip" comment="Label when a file cannot be imported in the current context. The actual reason string is in a separate text view below this string.">Файл не может быть импортирован. Причина:</string>
    <string name="ext__import__file_skip_unsupported" comment="Reason string when file is unsupported">Неподдерживаемый или неизвестный тип файла.</string>
    <string name="ext__import__file_skip_ext_core" comment="Reason string when ext has core extension ID">Невозможно заменить или обновить пакеты расширений по умолчанию. Если вы хотите использовать более новую версию основного пакета расширений, необходимо обновить само приложение.</string>
    <string name="ext__import__file_skip_ext_corrupted" comment="Reason string when file seems to be ext but is corrupted">Файл похож на архив расширения, но анализ данных не прошел. Архив поврежден или вовсе не от расширения.</string>
    <string name="ext__import__file_skip_ext_incorrect_type" comment="Reason string when file is of incorrect extension serial type">Ожидался архив расширения серийного типа \"{expected_serial_type}\", оказался \"{actual_serial_type}\".</string>
    <string name="ext__import__file_skip_ext_not_supported" comment="Reason string when file is loaded in incorrect context">Ожидался медиафайл (изображение, аудио, шрифт и т.д.), но обнаружен архив с расширением.</string>
    <string name="ext__import__file_skip_media_not_supported" comment="Reason string when file is loaded in incorrect context">Ожидался архив с расширением, но найден медиафайл (изображение, аудио, шрифт и т.д.).</string>
    <string name="ext__import__error_unexpected_exception" comment="Label when an error occurred during import. The error message will be appended below this text view">Во время импорта произошла непредвиденная ошибка. Были предоставлены следующие данные:</string>
    <string name="ext__validation__enter_package_name">Укажите название пакета</string>
    <string name="ext__validation__error_package_name">Название пакета не соответствует regex-у {id_regex}</string>
    <string name="ext__validation__enter_version">Укажите версию</string>
    <string name="ext__validation__enter_title">Укажите заголовок</string>
    <string name="ext__validation__enter_maintainer">Укажите хотя бы одного разработчика</string>
    <string name="ext__validation__enter_license">Укажите лицензию</string>
    <string name="ext__validation__enter_component_id">Укажите ID компонента</string>
    <string name="ext__validation__error_component_id">Укажите ID компонента, соответствующее {component_id_regex}</string>
    <string name="ext__validation__enter_component_label">Укажите метку компонента</string>
    <string name="ext__validation__hint_component_label_to_long">Метка компонента длинна, что может привести к наложению в интерфейсе</string>
    <string name="ext__validation__error_author">Укажите хотя бы одного автора</string>
    <string name="ext__validation__error_stylesheet_path_blank">Путь к стилю не должен быть пустым</string>
    <string name="ext__validation__error_stylesheet_path">Укажите правильный путь к стилю, соответствующий {stylesheet_path_regex}</string>
    <string name="ext__validation__enter_property">Укажите название переменной</string>
    <string name="ext__validation__error_property">Укажите правильное название переменной, соответствующее {variable_name_regex}</string>
    <string name="ext__validation__enter_color">Укажите строку цвета</string>
    <string name="ext__validation__error_color">Укажите правильную строку цвета</string>
    <string name="ext__validation__enter_dp_size">Укажите размер в dp</string>
    <string name="ext__validation__enter_valid_number">Укажите правильное число</string>
    <string name="ext__validation__enter_positive_number">Укажите положительное число (&gt;=0)</string>
    <string name="ext__validation__enter_percent_size">Укажите размер в процентах</string>
    <string name="ext__validation__enter_number_between_0_100">Укажите положительное число от 0 до 100</string>
    <string name="ext__validation__hint_value_above_50_percent">Любое значение выше 50% будет работать так же, как и 50%, попробуйте уменьшить размер в процентах</string>
    <string name="ext__update_box__internet_permission_hint">Так как это приложение не имеет доступа к Интернету, обновления для установленных расширений придётся проверять вручную.</string>
    <string name="ext__update_box__search_for_updates">Проверить обновления</string>
    <string name="ext__addon_management_box__managing_placeholder">Управление {extensions}</string>
    <string name="ext__addon_management_box__addon_manager_info">Все задачи, связанные с импортом, экспортом, созданием, персонализацией и удалением расширений могут быть выполнены через централизованный менеджер расширений.</string>
    <string name="ext__addon_management_box__go_to_page">Перейти к {ext_home_title}</string>
    <string name="ext__home__info">Вы можете скачать и установить расширения через магазин расширений FlorisBoard или импортировать любые файлы расширений, скачанные из интернета.</string>
    <string name="ext__home__visit_store">Магазин расширений</string>
    <string name="ext__home__manage_extensions">Установленные расширения</string>
    <string name="ext__list__view_details">Подробности</string>
    <string name="ext__check_updates__title">Проверить обновления</string>
    <!-- Action strings -->
    <string name="action__add">Добавить</string>
    <string name="action__apply">Применить</string>
    <string name="action__back_up">Резервирование</string>
    <string name="action__cancel">Отмена</string>
    <string name="action__create">Создать</string>
    <string name="action__default">Сбросить</string>
    <string name="action__delete">Удалить</string>
    <string name="action__delete_confirm_title">Подтвердить удаление</string>
    <string name="action__delete_confirm_message">Вы уверены, что хотите удалить \"{name}\"? Это действие не может быть отменено после выполнения.</string>
    <string name="action__reset_confirm_title">Подтвердить сброс</string>
    <string name="action__reset_confirm_message">Вы уверены, что хотите сбросить \"{name}\"? Это действие не может быть отменено после выполнения.</string>
    <string name="action__discard">Отменить</string>
    <string name="action__discard_confirm_title">Несохранённые изменения</string>
    <string name="action__discard_confirm_message">Вы уверены, что хотите отменить несохранённые изменения? Это действие нельзя отменить после выполнения.</string>
    <string name="action__edit">Изменить</string>
    <string name="action__export">Экспорт</string>
    <string name="action__import">Импорт</string>
    <string name="action__no">Нет</string>
    <string name="action__ok">OK</string>
    <string name="action__restore">Восстановление</string>
    <string name="action__save">Сохранить</string>
    <string name="action__select">Выбрать</string>
    <string name="action__select_dir">Выбрать папку</string>
    <string name="action__select_dirs">Выбрать папки</string>
    <string name="action__select_file">Выбрать файл</string>
    <string name="action__select_files">Выбрать файлы</string>
    <string name="action__yes">Да</string>
    <!-- Error strings (generic) -->
    <string name="error__title">Ошибка</string>
    <string name="error__details">Сведения</string>
    <string name="error__invalid">Недействителен</string>
    <string name="error__snackbar_message">Что-то пошло не так</string>
    <string name="error__snackbar_message_template">Что-то пошло не так: {error_message}</string>
    <!-- General strings -->
    <string name="general__example_given" comment="This string is used where an example will be inserted in the {example} block. When translating, use the abbreviation used in your language for 'example given'.">например, {example}</string>
    <string name="general__no_browser_app_found_for_url">Не найдено браузера для использования URL {url}</string>
    <string name="general__select_dropdown_value_placeholder">&#45; не выбрано &#45;</string>
    <string name="general__unlimited">Без ограничений</string>
    <!-- Screen orientation strings -->
    <string name="screen_orientation__portrait">Портретная</string>
    <string name="screen_orientation__landscape">Альбомная</string>
    <string name="screen_orientation__vertical">Вертикальная</string>
    <string name="screen_orientation__horizontal">Горизонтальная</string>
    <!-- State strings -->
    <string name="state__disabled">Отключено</string>
    <string name="state__enabled">Включено</string>
    <string name="state__no_dir_selected">Каталог не выбран</string>
    <string name="state__no_dirs_selected">Каталоги не выбраны</string>
    <string name="state__no_file_selected">Файл не выбран</string>
    <string name="state__no_files_selected">Файлы не выбраны</string>
    <!-- Enum label and description strings -->
    <string name="enum__candidates_display_mode__classic" comment="Enum value label">Классика (3 колонки)</string>
    <string name="enum__candidates_display_mode__dynamic" comment="Enum value label">Динамическая ширина</string>
    <string name="enum__candidates_display_mode__dynamic_scrollable" comment="Enum value label">Динамическая ширина и прокрутка</string>
    <string name="enum__capitalization_behavior__capslock_by_double_tap" comment="Enum value label">Активировать Capslock двойным нажатием</string>
    <string name="enum__capitalization_behavior__capslock_by_cycle" comment="Enum value label">Переключать режимы регистра с каждым нажатием</string>
    <string name="enum__display_kbd_after_dialogs__always" comment="Enum value label">Отображать всегда</string>
    <string name="enum__display_kbd_after_dialogs__always__description" comment="Enum value description">Отображать клавиатуру всегда после закрытия любого диалогового окна редактора</string>
    <string name="enum__display_kbd_after_dialogs__never" comment="Enum value label">Не показывать</string>
    <string name="enum__display_kbd_after_dialogs__never__description" comment="Enum value description">Не отображать клавиатуру после закрытия любого диалогового окна редактора</string>
    <string name="enum__display_kbd_after_dialogs__remember" comment="Enum value label">Запомнить последнее состояние</string>
    <string name="enum__display_kbd_after_dialogs__remember__description" comment="Enum value description">Показывать клавиатуру только после закрытия любого диалогового окна редактора, если она была видна ранее</string>
    <string name="enum__display_language_names_in__system_locale" comment="Enum value label">Переведенные</string>
    <string name="enum__display_language_names_in__system_locale__description" comment="Enum value description">Подписи в приложении и интерфейсе клавиатуры указаны на языке, используемом в системе по умолчанию</string>
    <string name="enum__display_language_names_in__native_locale" comment="Enum value label">В исходном виде</string>
    <string name="enum__display_language_names_in__native_locale__description" comment="Enum value description">Подписи в приложении и интерфейсе клавиатуры приводятся на родных языках</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_prepend" comment="Enum value label">Автоматическая сортировка (добавление в начало)</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_prepend__description" comment="Enum value description">Автоматическое изменение порядка расположения эмодзи в зависимости от их использования. Новые эмодзи добавляются в начало.</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_append" comment="Enum value label">Автоматическая сортировка (добавление в конец)</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_append__description" comment="Enum value description">Автоматическое изменение порядка расположения эмодзи в зависимости от их использования. Новые эмодзи добавляются в конец.</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_prepend" comment="Enum value label">Ручная сортировка (добавление в начало)</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_prepend__description" comment="Enum value description">Не происходит автоматической перестановки эмодзи в зависимости от их использования. 
Новые эмодзи добавляются в начало.</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_append" comment="Enum value label">Ручная сортировка (добавление в конец)</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_append__description" comment="Enum value description">Не происходит автоматической перестановки эмодзи в зависимости от их использования. 
Новые эмодзи добавляются в конец.</string>
    <string name="enum__emoji_skin_tone__default" comment="Enum value label">Цвет кожи {emoji} по умолчанию</string>
    <string name="enum__emoji_skin_tone__light_skin_tone" comment="Enum value label">Светлый цвет кожи {emoji}</string>
    <string name="enum__emoji_skin_tone__medium_light_skin_tone" comment="Enum value label">Светловатый цвет кожи {emoji}</string>
    <string name="enum__emoji_skin_tone__medium_skin_tone" comment="Enum value label">Нейтральный цвет кожи {emoji}</string>
    <string name="enum__emoji_skin_tone__medium_dark_skin_tone" comment="Enum value label">Темноватый цвет кожи {emoji}</string>
    <string name="enum__emoji_skin_tone__dark_skin_tone" comment="Enum value label">Тёмный цвет кожи {emoji}</string>
    <string name="enum__emoji_hair_style__default" comment="Enum value label">{emoji} Обычные волосы</string>
    <string name="enum__emoji_hair_style__red_hair" comment="Enum value label">{emoji} Рыжие волосы</string>
    <string name="enum__emoji_hair_style__curly_hair" comment="Enum value label">{emoji} Вьющиеся волосы</string>
    <string name="enum__emoji_hair_style__white_hair" comment="Enum value label">{emoji} Светлые волосы</string>
    <string name="enum__emoji_hair_style__bald" comment="Enum value label">{emoji} Без волос</string>
    <string name="enum__emoji_suggestion_type__leading_colon">Начальное двоеточие</string>
    <string name="enum__emoji_suggestion_type__leading_colon__description" comment="Keep the :emoji_name while translating, this is a syntax guide">Предлагайте эмодзи, используя синтаксис :emoji_name</string>
    <string name="enum__emoji_suggestion_type__inline_text">Встроенный текст</string>
    <string name="enum__emoji_suggestion_type__inline_text__description">Предлагает эмодзи, просто набрав название эмодзи в виде слова</string>
    <string name="enum__extended_actions_placement__above_candidates" comment="Enum value label">Вышестоящие предложение</string>
    <string name="enum__extended_actions_placement__above_candidates__description" comment="Enum value description">Размещает строку расширенных действий между пользовательским интерфейсом приложения и строкой предложений</string>
    <string name="enum__extended_actions_placement__below_candidates" comment="Enum value label">Нижестоящие предложение</string>
    <string name="enum__extended_actions_placement__below_candidates__description" comment="Enum value description">Размещает строку расширенных действий между строкой предложений и текстовой клавиатурой</string>
    <string name="enum__extended_actions_placement__overlay_app_ui" comment="Enum value label">Поверх интерфейса приложения</string>
    <string name="enum__extended_actions_placement__overlay_app_ui__description" comment="Enum value description">Размещает строку расширенных действий в виде накладки над пользовательским интерфейсом приложения, не влияя на высоту пользовательского интерфейса клавиатуры. Обратите внимание, что такое размещение может привести к тому, что поле ввода приложения будет частично перегружено</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly" comment="Enum value label">Использовать вибромотор напрямую</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly__description" comment="Enum value description">{app_name} будет напрямую взаимодействовать с аппаратным вибромотором. Это даёт больший контроль над продолжительностью и силой вибрации, но вибрация может быть не такой плавной и оптимизированной, как при использовании интерфейса тактильной обратной связи.</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface" comment="Enum value label">Использовать интерфейс тактильной обратной связи</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface__description" comment="Enum value description">{app_name} будет использовать системный интерфейс тактильной обратной связи для запуска заданной вибраций при нажатии кнопок. Может работать исключительно хорошо на одних устройствах, но не работать или работать очень плохо на других.</string>
    <string name="enum__key_hint_mode__accent_priority" comment="Enum value label">Приоритет акценту</string>
    <string name="enum__key_hint_mode__accent_priority__description" comment="Enum value description">Начальный символ, выбираемый долгим нажатием, всегда является основным акцентом или символом подсказки, если основной акцент недоступен</string>
    <string name="enum__key_hint_mode__hint_priority" comment="Enum value label">Приоритет подсказке</string>
    <string name="enum__key_hint_mode__hint_priority__description" comment="Enum value description">Начальный символ, выбираемый долгим нажатием, всегда является символом подсказки или основным акцентом, если символ подсказки недоступен</string>
    <string name="enum__key_hint_mode__smart_priority" comment="Enum value label">Умный приоритет</string>
    <string name="enum__key_hint_mode__smart_priority__description" comment="Enum value description">Начальный символ, выбираемый долгим нажатием, динамически определяется как основной акцент или символ подсказки, основываясь на текущем языке и раскладке</string>
    <string name="enum__incognito_display_mode__replace_shared_actions_toggle" comment="Enum value label">Заменять значок переключения общих действий на индикатор инкогнито</string>
    <string name="enum__incognito_display_mode__display_behind_keyboard" comment="Enum value label">Отображать индикатор инкогнито за клавиатурой</string>
    <string name="enum__incognito_mode__force_off" comment="Enum value label">Всегда выключен</string>
    <string name="enum__incognito_mode__force_off__description" comment="Enum value description">Режим инкогнито будет отключен всегда, независимо от переданных опций целевого приложения. Быстрое действие инкогнито в Smartbar будет недоступно при использовании этой опции.</string>
    <string name="enum__incognito_mode__force_on" comment="Enum value label">Всегда включен</string>
    <string name="enum__incognito_mode__force_on__description" comment="Enum value description">Режим инкогнито будет включен всегда, независимо от переданных опций целевого приложения. Быстрое действие инкогнито в Smartbar не будет доступно при использовании этой опции.</string>
    <string name="enum__incognito_mode__dynamic_on_off" comment="Enum value label">Динамическое переключение</string>
    <string name="enum__incognito_mode__dynamic_on_off__description" comment="Enum value description">Рекомендуемый вариант. Режим инкогнито будет включён либо в зависимости от предпочтений целевого приложения (автоматически), либо путём нажатия кнопки инкогнито на умной панели (вручную).</string>
    <string name="enum__input_feedback_activation_mode__audio_respect_system_settings" comment="Enum value label">Воспроизводить звуки при вводе в зависимости от системных настроек</string>
    <string name="enum__input_feedback_activation_mode__audio_ignore_system_settings" comment="Enum value label">Воспроизводить звуки при вводе вне зависимости от системных настроек</string>
    <string name="enum__input_feedback_activation_mode__haptic_respect_system_settings" comment="Enum value label">Использовать вибрацию при вводе в зависимости от системных настроек</string>
    <string name="enum__input_feedback_activation_mode__haptic_ignore_system_settings" comment="Enum value label">Использовать вибрацию при вводе вне зависимости от системных настроек</string>
    <string name="enum__input_shift_state__unshifted" comment="Enum value label">Без сдвига</string>
    <string name="enum__input_shift_state__shifted_manual" comment="Enum value label">Сдвиг (вручную)</string>
    <string name="enum__input_shift_state__shifted_automatic" comment="Enum value label">Сдвиг (авто)</string>
    <string name="enum__input_shift_state__caps_lock" comment="Enum value label">Caps Lock</string>
    <string name="enum__landscape_input_ui_mode__never_show" comment="Enum value label">Не показывать</string>
    <string name="enum__landscape_input_ui_mode__always_show" comment="Enum value label">Всегда показывать</string>
    <string name="enum__landscape_input_ui_mode__dynamically_show" comment="Enum value label">Автоматически</string>
    <string name="enum__one_handed_mode__start" comment="Enum value label">Для левши</string>
    <string name="enum__one_handed_mode__end" comment="Enum value label">Для правши</string>
    <string name="enum__shape_corner__top_start" comment="Enum value label">Верх начало</string>
    <string name="enum__shape_corner__top_end" comment="Enum value label">Верх конец</string>
    <string name="enum__shape_corner__bottom_end" comment="Enum value label">Низ конец</string>
    <string name="enum__shape_corner__bottom_start" comment="Enum value label">Низ начало</string>
    <string name="enum__smartbar_layout__suggestions_only" comment="Enum value label">Только предложения</string>
    <string name="enum__smartbar_layout__suggestions_only__description" comment="Enum value description">Показывает только строку с предложениями, без каких-либо действий со строкой/тумблером или закрепленными действиями</string>
    <string name="enum__smartbar_layout__actions_only" comment="Enum value label">Только действия</string>
    <string name="enum__smartbar_layout__actions_only__description" comment="Enum value description">Показывает только строку действий, без строки предложений или явно закрепленного действия</string>
    <string name="enum__smartbar_layout__suggestions_action_shared" comment="Enum value label">Предложения &amp; Общие действия</string>
    <string name="enum__smartbar_layout__suggestions_action_shared__description" comment="Enum value description">Общий переключаемый ряд предложений и действий, с закрепленными действиями</string>
    <string name="enum__smartbar_layout__suggestions_actions_extended" comment="Enum value label">Предложения &amp; Расширенные действия</string>
    <string name="enum__smartbar_layout__suggestions_actions_extended__description" comment="Enum value description">Статическая строка предложений и дополнительная строка действий с возможностью переключения, с закрепленными действиями</string>
    <string name="enum__snygg_level__basic" comment="Enum value label">Базовый</string>
    <string name="enum__snygg_level__basic__description" comment="Enum value description">Отображаются только свойства цвета, переводятся свойства и правила.</string>
    <string name="enum__snygg_level__advanced" comment="Enum value label">Ещё</string>
    <string name="enum__snygg_level__advanced__description" comment="Enum value description">Отображаются все свойства, переводятся свойства и правила.</string>
    <string name="enum__snygg_level__developer" comment="Enum value label">Разработчик</string>
    <string name="enum__snygg_level__developer__description" comment="Enum value description">Отображаются все свойства. Свойства и правила отображаются так, как прописано в самом файле таблицы стилей.</string>
    <string name="enum__space_bar_mode__nothing" comment="Enum value label">Без надписи</string>
    <string name="enum__space_bar_mode__current_language" comment="Enum value label">Текущий язык</string>
    <string name="enum__space_bar_mode__space_bar_key" comment="Enum value label">␣</string>
    <string name="enum__spelling_language_mode__use_system_languages" comment="Enum value label">Использовать системные языки</string>
    <string name="enum__spelling_language_mode__use_keyboard_subtypes" comment="Enum value label">Использовать раскладки клавиатуры</string>
    <string name="enum__swipe_action__no_action" comment="Enum value label">Не задано</string>
    <string name="enum__swipe_action__cycle_to_previous_keyboard_mode" comment="Enum value label">Перейти к предыдущему режиму клавиатуры</string>
    <string name="enum__swipe_action__cycle_to_next_keyboard_mode" comment="Enum value label">Перейти к следующему режиму клавиатуры</string>
    <string name="enum__swipe_action__delete_character" comment="Enum value label">Удалить символ перед курсором</string>
    <string name="enum__swipe_action__delete_characters_precisely" comment="Enum value label">Точное удаление символов</string>
    <string name="enum__swipe_action__delete_word" comment="Enum value label">Удалить слово перед курсором</string>
    <string name="enum__swipe_action__delete_words_precisely" comment="Enum value label">Точное удаление слова</string>
    <string name="enum__swipe_action__hide_keyboard" comment="Enum value label">Скрыть клавиатуру</string>
    <string name="enum__swipe_action__insert_space" comment="Enum value label">Вставить пробел</string>
    <string name="enum__swipe_action__move_cursor_up" comment="Enum value label">Переместить курсор вверх</string>
    <string name="enum__swipe_action__move_cursor_down" comment="Enum value label">Переместить курсор вниз</string>
    <string name="enum__swipe_action__move_cursor_left" comment="Enum value label">Переместить курсор влево</string>
    <string name="enum__swipe_action__move_cursor_right" comment="Enum value label">Переместить курсор вправо</string>
    <string name="enum__swipe_action__move_cursor_start_of_line" comment="Enum value label">Переместить курсор в начало строки</string>
    <string name="enum__swipe_action__move_cursor_end_of_line" comment="Enum value label">Переместить курсор в конец строки</string>
    <string name="enum__swipe_action__move_cursor_start_of_page" comment="Enum value label">Переместить курсор в начало страницы</string>
    <string name="enum__swipe_action__move_cursor_end_of_page" comment="Enum value label">Переместить курсор в конец страницы</string>
    <string name="enum__swipe_action__switch_to_clipboard_context" comment="Enum value label">Открыть буфер обмена/историю</string>
    <string name="enum__swipe_action__shift" comment="Enum value label">Shift</string>
    <string name="enum__swipe_action__redo" comment="Enum value label">Повторить</string>
    <string name="enum__swipe_action__undo" comment="Enum value label">Отменить</string>
    <string name="enum__swipe_action__select_characters_precisely" comment="Enum value label">Точное выделение символов</string>
    <string name="enum__swipe_action__select_words_precisely" comment="Enum value label">Точное выделение слов</string>
    <string name="enum__swipe_action__show_input_method_picker" comment="Enum value label">Показать окно способа ввода</string>
    <string name="enum__swipe_action__switch_to_prev_keyboard" comment="Enum value label">Переключиться на предыдущую клавиатуру</string>
    <string name="enum__swipe_action__switch_to_prev_subtype" comment="Enum value label">Переключиться на предыдущую раскладку</string>
    <string name="enum__swipe_action__switch_to_next_subtype" comment="Enum value label">Переключиться на следующую раскладку</string>
    <string name="enum__swipe_action__toggle_smartbar_visibility" comment="Enum value label">Переключить видимость умной панели</string>
    <string name="enum__theme_mode__always_day" comment="Enum value label">Всегда светлая</string>
    <string name="enum__theme_mode__always_night" comment="Enum value label">Всегда тёмная</string>
    <string name="enum__theme_mode__follow_system" comment="Enum value label">Как в системе</string>
    <string name="enum__theme_mode__follow_time" comment="Enum value label">По расписанию</string>
    <string name="enum__utility_key_action__switch_to_emojis" comment="Enum value label">Переключиться на эмодзи</string>
    <string name="enum__utility_key_action__switch_language" comment="Enum value label">Переключить язык</string>
    <string name="enum__utility_key_action__switch_keyboard_app" comment="Enum value label">Переключить приложение клавиатуры</string>
    <string name="enum__utility_key_action__dynamic_switch_language_emojis" comment="Enum value label">Динамическое: переключиться на эмодзи / переключить язык</string>
    <!-- Unit strings (symbols) -->
    <!-- Unit strings (written words) -->
    <plurals name="unit__hours__written">
        <item quantity="one">{v} час</item>
        <item quantity="few">{v} часа</item>
        <item quantity="many">{v} часов</item>
        <item quantity="other">{v} часов</item>
    </plurals>
    <plurals name="unit__minutes__written">
        <item quantity="one">{v} минута</item>
        <item quantity="few">{v} минуты</item>
        <item quantity="many">{v} минут</item>
        <item quantity="other">{v} минут</item>
    </plurals>
    <plurals name="unit__seconds__written">
        <item quantity="one">{v} секунда</item>
        <item quantity="few">{v} секунды</item>
        <item quantity="many">{v} секунд</item>
        <item quantity="other">{v} секунд</item>
    </plurals>
    <plurals name="unit__items__written">
        <item quantity="one">{v} элемент</item>
        <item quantity="few">{v} элемента</item>
        <item quantity="many">{v} элементов</item>
        <item quantity="other">{v} элементов</item>
    </plurals>
    <plurals name="unit__characters__written">
        <item quantity="one">{v} символ</item>
        <item quantity="few">{v} символа</item>
        <item quantity="many">{v} символов</item>
        <item quantity="other">{v} символов</item>
    </plurals>
    <plurals name="unit__candidates__written">
        <item quantity="one">{v} предложение</item>
        <item quantity="few">{v} предложения</item>
        <item quantity="many">{v} предложений</item>
        <item quantity="other">{v} предложений</item>
    </plurals>
</resources>
