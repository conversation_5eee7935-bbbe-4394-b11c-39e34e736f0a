{"formatVersion": 1, "database": {"version": 1, "identityHash": "787af4a2df15bf9d2c0597519d3fb273", "entities": [{"tableName": "words", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `word` TEXT NOT NULL, `frequency` INTEGER NOT NULL, `locale` TEXT, `shortcut` TEXT)", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "word", "columnName": "word", "affinity": "TEXT", "notNull": true}, {"fieldPath": "freq", "columnName": "frequency", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "locale", "columnName": "locale", "affinity": "TEXT", "notNull": false}, {"fieldPath": "shortcut", "columnName": "shortcut", "affinity": "TEXT", "notNull": false}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": true}, "indices": [{"name": "index_words__id", "unique": false, "columnNames": ["_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_words__id` ON `${TABLE_NAME}` (`_id`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '787af4a2df15bf9d2c0597519d3fb273')"]}}