["C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\fonts/MaterialIcons-Regular.otf", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\shaders/ink_sparkle.frag", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.json", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\AssetManifest.bin", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\FontManifest.json", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\flutter_assets\\NOTICES.Z", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\armeabi-v7a\\app.so", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\arm64-v8a\\app.so", "C:\\Users\\<USER>\\Desktop\\native keyboard\\k\\florisboard\\build\\app\\intermediates\\flutter\\release\\x86_64\\app.so"]