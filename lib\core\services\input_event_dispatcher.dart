import 'dart:async';

import '../../data/models/key_data.dart';
import '../constants/key_codes.dart';
import '../constants/app_constants.dart';
import '../preferences/preferences_manager.dart';
import 'ime_service.dart';
import 'input_feedback_controller.dart';

class InputEventDispatcher {
  final Map<KeyCode, PressedKeyInfo> _pressedKeys = {};
  final IMEService _imeService = IMEService();
  final InputFeedbackController _feedbackController = InputFeedbackController();

  Future<PressedKeyInfo?> sendDown(KeyData keyData) async {
    if (_pressedKeys.containsKey(keyData.code)) {
      return null; // Key already pressed
    }

    final pressedKeyInfo = PressedKeyInfo(
      keyData: keyData,
      timestamp: DateTime.now(),
    );

    // Provide immediate feedback
    await _feedbackController.performHapticFeedback(keyData);
    await _feedbackController.performAudioFeedback(keyData);

    // Start long press timer
    pressedKeyInfo.longPressTimer = Timer(
      Duration(milliseconds: _getLongPressDelay(keyData)),
      () => _handleLongPress(keyData),
    );

    // Start repeat timer for repeatable keys
    if (_isRepeatableKey(keyData)) {
      pressedKeyInfo.repeatTimer = Timer.periodic(
        Duration(milliseconds: _getRepeatDelay(keyData)),
        (timer) => _handleRepeat(keyData),
      );
    }

    _pressedKeys[keyData.code] = pressedKeyInfo;

    // Notify key down
    await _imeService.onKeyDown(keyData);

    return pressedKeyInfo;
  }

  Future<void> sendUp(KeyData keyData) async {
    final pressedKeyInfo = _pressedKeys.remove(keyData.code);
    if (pressedKeyInfo == null) return;

    // Cancel timers
    pressedKeyInfo.longPressTimer?.cancel();
    pressedKeyInfo.repeatTimer?.cancel();

    if (!pressedKeyInfo.wasLongPressed && !pressedKeyInfo.wasRepeated) {
      // Normal key press
      await _imeService.onKeyUp(keyData);
    } else {
      // Long press or repeat - cancel the key
      await _imeService.onKeyCancel(keyData);
    }
  }

  Future<void> sendDownUp(KeyData keyData) async {
    await _imeService.onKeyDown(keyData);
    await _imeService.onKeyUp(keyData);

    // Provide feedback for immediate press
    await _feedbackController.performHapticFeedback(keyData);
    await _feedbackController.performAudioFeedback(keyData);
  }

  void _handleLongPress(KeyData keyData) {
    final pressedKeyInfo = _pressedKeys[keyData.code];
    if (pressedKeyInfo != null) {
      pressedKeyInfo.wasLongPressed = true;
      _imeService.onKeyLongPress(keyData);

      // Provide long press feedback
      _feedbackController.performHapticFeedback(keyData, intensity: 1.5);
    }
  }

  void _handleRepeat(KeyData keyData) {
    final pressedKeyInfo = _pressedKeys[keyData.code];
    if (pressedKeyInfo != null) {
      pressedKeyInfo.wasRepeated = true;
      _imeService.onKeyRepeat(keyData);
    }
  }

  int _getLongPressDelay(KeyData keyData) {
    return PreferencesManager.instance.keyboard.longPressDelay;
  }

  int _getRepeatDelay(KeyData keyData) {
    switch (keyData.code) {
      case KeyCode.DELETE:
        return 50; // Fast repeat for delete
      case KeyCode.SPACE:
        return 100;
      case KeyCode.ARROW_LEFT:
      case KeyCode.ARROW_RIGHT:
      case KeyCode.ARROW_UP:
      case KeyCode.ARROW_DOWN:
        return 80; // Medium repeat for navigation
      default:
        return 150;
    }
  }

  bool _isRepeatableKey(KeyData keyData) {
    return keyData.code.isRepeatable;
  }

  void cancelAllKeys() {
    for (final pressedKeyInfo in _pressedKeys.values) {
      pressedKeyInfo.longPressTimer?.cancel();
      pressedKeyInfo.repeatTimer?.cancel();
    }
    _pressedKeys.clear();
  }

  void dispose() {
    cancelAllKeys();
  }

  // Gesture handling
  Future<void> handleSwipeGesture(
      KeyData keyData, SwipeDirection direction) async {
    final gesturePrefs = PreferencesManager.instance.gestures;
    SwipeAction action;

    // Determine action based on key and direction
    if (keyData.code == KeyCode.SPACE) {
      switch (direction) {
        case SwipeDirection.left:
          action = gesturePrefs.swipeLeft;
          break;
        case SwipeDirection.right:
          action = gesturePrefs.swipeRight;
          break;
        case SwipeDirection.up:
          action = gesturePrefs.swipeUp;
          break;
        case SwipeDirection.down:
          action = gesturePrefs.swipeDown;
          break;
      }
    } else {
      // Default swipe actions for other keys
      switch (direction) {
        case SwipeDirection.left:
          action = SwipeAction.DELETE_WORD;
          break;
        case SwipeDirection.right:
          action = SwipeAction.SPACE;
          break;
        case SwipeDirection.up:
          action = SwipeAction.SHIFT;
          break;
        case SwipeDirection.down:
          action = SwipeAction.HIDE_KEYBOARD;
          break;
      }
    }

    await _executeSwipeAction(action);

    // Provide gesture feedback
    await _feedbackController.performGestureFeedback(direction);
  }

  Future<void> _executeSwipeAction(SwipeAction action) async {
    switch (action) {
      case SwipeAction.NO_ACTION:
        break;
      case SwipeAction.HIDE_KEYBOARD:
        await IMEService.hideKeyboard();
        break;
      case SwipeAction.DELETE_WORD:
        await _imeService.deleteWord();
        break;
      case SwipeAction.SPACE:
        await IMEService.commitText(' ');
        break;
      case SwipeAction.MOVE_CURSOR_LEFT:
        await IMEService.moveCursor(-1);
        break;
      case SwipeAction.MOVE_CURSOR_RIGHT:
        await IMEService.moveCursor(1);
        break;
      case SwipeAction.SHIFT:
        // This would be handled by KeyboardManager
        break;
      case SwipeAction.UNDO:
        // Implement undo functionality
        break;
      case SwipeAction.REDO:
        // Implement redo functionality
        break;
      case SwipeAction.SELECT_WORD:
        await _imeService.selectCurrentWord();
        break;
      case SwipeAction.SHOW_INPUT_METHOD_PICKER:
        // Show input method picker
        break;
      default:
        // Handle other actions
        break;
    }
  }
}

class PressedKeyInfo {
  final KeyData keyData;
  final DateTime timestamp;
  Timer? longPressTimer;
  Timer? repeatTimer;
  bool wasLongPressed = false;
  bool wasRepeated = false;

  PressedKeyInfo({
    required this.keyData,
    required this.timestamp,
  });

  Duration get pressDuration => DateTime.now().difference(timestamp);
}
