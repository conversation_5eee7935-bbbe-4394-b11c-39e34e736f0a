import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/managers/emoji_manager.dart';
import '../../../core/constants/app_constants.dart';

class MediaInputLayout extends StatelessWidget {
  const MediaInputLayout({
    super.key,
    required this.onEmojiSelected,
    required this.onModeChange,
  });

  final Function(String) onEmojiSelected;
  final Function(KeyboardMode) onModeChange;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<EmojiManager, EmojiState>(
      builder: (context, state) {
        return Column(
          children: [
            // Category tabs
            Container(
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Back button
                  IconButton(
                    onPressed: () => onModeChange(KeyboardMode.characters),
                    icon: const Icon(Icons.keyboard, size: 20),
                    tooltip: 'Back to keyboard',
                  ),

                  // Category tabs
                  Expanded(
                    child: ListView(
                      scrollDirection: Axis.horizontal,
                      children: [
                        _buildCategoryTab(
                            context, 'recent', '🕒', state.selectedCategory),
                        _buildCategoryTab(
                            context, 'smileys', '😀', state.selectedCategory),
                        _buildCategoryTab(
                            context, 'animals', '🐶', state.selectedCategory),
                        _buildCategoryTab(
                            context, 'food', '🍎', state.selectedCategory),
                      ],
                    ),
                  ),

                  // Search button
                  IconButton(
                    onPressed: () => _showEmojiSearch(context),
                    icon: const Icon(Icons.search, size: 20),
                    tooltip: 'Search emojis',
                  ),
                ],
              ),
            ),

            // Emoji grid
            Expanded(
              child: _buildEmojiGrid(context, state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildCategoryTab(BuildContext context, String category, String icon,
      String selectedCategory) {
    final isSelected = category == selectedCategory;

    return GestureDetector(
      onTap: () => context.read<EmojiManager>().selectCategory(category),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          color: isSelected
              ? Theme.of(context).colorScheme.primary.withValues(alpha: 0.1)
              : Colors.transparent,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Text(
          icon,
          style: TextStyle(
            fontSize: 20,
            color: isSelected
                ? Theme.of(context).colorScheme.primary
                : Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ),
    );
  }

  Widget _buildEmojiGrid(BuildContext context, EmojiState state) {
    final emojis = context
        .read<EmojiManager>()
        .getEmojisForCategory(state.selectedCategory);

    if (emojis.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.emoji_emotions_outlined,
              size: 48,
              color: Theme.of(context)
                  .colorScheme
                  .onSurface
                  .withValues(alpha: 0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No emojis in this category',
              style: TextStyle(
                color: Theme.of(context)
                    .colorScheme
                    .onSurface
                    .withValues(alpha: 0.5),
              ),
            ),
          ],
        ),
      );
    }

    return GridView.builder(
      padding: const EdgeInsets.all(8),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 8,
        childAspectRatio: 1,
        crossAxisSpacing: 4,
        mainAxisSpacing: 4,
      ),
      itemCount: emojis.length,
      itemBuilder: (context, index) {
        final emoji = emojis[index];
        return GestureDetector(
          onTap: () {
            onEmojiSelected(emoji.unicode);
            context.read<EmojiManager>().addToHistory(emoji);
          },
          child: Container(
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surface,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outline,
                width: 0.5,
              ),
            ),
            child: Center(
              child: Text(
                emoji.unicode,
                style: const TextStyle(fontSize: 24),
              ),
            ),
          ),
        );
      },
    );
  }

  void _showEmojiSearch(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Emojis'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                hintText: 'Type to search...',
                border: OutlineInputBorder(),
              ),
              onChanged: (query) {
                // Implement emoji search
                // TODO: Update search results
                context.read<EmojiManager>().searchEmojis(query);
              },
            ),
            const SizedBox(height: 16),
            const Text('Search results will appear here'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
