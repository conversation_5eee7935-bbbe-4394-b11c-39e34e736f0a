import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/key_data.dart';
import '../../../core/managers/theme_manager.dart';
import '../../../core/managers/keyboard_manager.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/constants/key_codes.dart';

class KeyWidget extends StatefulWidget {
  const KeyWidget({
    super.key,
    required this.keyData,
    required this.onPressed,
    required this.onLongPressed,
    required this.onSwiped,
  });

  final KeyData keyData;
  final VoidCallback onPressed;
  final VoidCallback onLongPressed;
  final Function(SwipeDirection) onSwiped;

  @override
  State<KeyWidget> createState() => _KeyWidgetState();
}

class _KeyWidgetState extends State<KeyWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 100),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeManager, ThemeState>(
      builder: (context, themeState) {
        return BlocBuilder<KeyboardManager, KeyboardState>(
          builder: (context, keyboardState) {
            return GestureDetector(
              onTapDown: _onTapDown,
              onTapUp: _onTapUp,
              onTapCancel: _onTapCancel,
              onLongPress: widget.onLongPressed,
              onPanUpdate: _onPanUpdate,
              child: AnimatedBuilder(
                animation: _scaleAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _scaleAnimation.value,
                    child: Container(
                      height: double.infinity,
                      decoration: BoxDecoration(
                        color: _getKeyColor(themeState, keyboardState),
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: themeState.currentTheme.colorScheme.outline,
                          width: 0.5,
                        ),
                        boxShadow: _isPressed
                            ? []
                            : [
                                BoxShadow(
                                  color: Colors.black.withOpacity(0.1),
                                  blurRadius: 2,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                      ),
                      child: Center(
                        child: _buildKeyContent(themeState, keyboardState),
                      ),
                    ),
                  );
                },
              ),
            );
          },
        );
      },
    );
  }

  Widget _buildKeyContent(ThemeState themeState, KeyboardState keyboardState) {
    String displayText = _getDisplayText(keyboardState);
    IconData? icon = _getKeyIcon();

    if (icon != null) {
      return Icon(
        icon,
        color: themeState.currentTheme.colorScheme.onSurface,
        size: 20,
      );
    }

    return Text(
      displayText,
      style: TextStyle(
        color: themeState.currentTheme.colorScheme.onSurface,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }

  String _getDisplayText(KeyboardState keyboardState) {
    if (widget.keyData.label != null) {
      String text = widget.keyData.label!;

      // Apply shift state for character keys
      if (widget.keyData.type == KeyType.character && keyboardState.isShifted) {
        return text.toUpperCase();
      }

      return text;
    }

    // Fallback to key code display name
    return widget.keyData.code.displayName;
  }

  IconData? _getKeyIcon() {
    switch (widget.keyData.code) {
      case KeyCode.DELETE:
        return Icons.backspace_outlined;
      case KeyCode.ENTER:
        return Icons.keyboard_return;
      case KeyCode.SHIFT:
        return Icons.keyboard_arrow_up;
      case KeyCode.SETTINGS:
        return Icons.settings;
      case KeyCode.EMOJI_SWITCH:
        return Icons.emoji_emotions;
      case KeyCode.CLIPBOARD_SWITCH:
        return Icons.content_paste;
      default:
        return null;
    }
  }

  Color _getKeyColor(ThemeState themeState, KeyboardState keyboardState) {
    Color baseColor;

    switch (widget.keyData.type) {
      case KeyType.function:
      case KeyType.modifier:
        baseColor = themeState.currentTheme.colorScheme.primary;
        break;
      case KeyType.character:
      case KeyType.numeric:
        baseColor = themeState.currentTheme.colorScheme.surface;
        break;
      default:
        baseColor = themeState.currentTheme.colorScheme.surface;
    }

    // Apply pressed state
    if (_isPressed) {
      return baseColor.withOpacity(0.7);
    }

    // Apply special states
    if (widget.keyData.code == KeyCode.SHIFT && keyboardState.isShifted) {
      return themeState.currentTheme.colorScheme.primary;
    }

    return baseColor;
  }

  void _onTapDown(TapDownDetails details) {
    setState(() {
      _isPressed = true;
    });
    _animationController.forward();
  }

  void _onTapUp(TapUpDetails details) {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
    widget.onPressed();
  }

  void _onTapCancel() {
    setState(() {
      _isPressed = false;
    });
    _animationController.reverse();
  }

  void _onPanUpdate(DragUpdateDetails details) {
    // Simple swipe detection
    const threshold = 20.0;
    final delta = details.delta;

    if (delta.dx.abs() > threshold || delta.dy.abs() > threshold) {
      SwipeDirection direction;

      if (delta.dx.abs() > delta.dy.abs()) {
        // Horizontal swipe
        direction = delta.dx > 0 ? SwipeDirection.RIGHT : SwipeDirection.LEFT;
      } else {
        // Vertical swipe
        direction = delta.dy > 0 ? SwipeDirection.DOWN : SwipeDirection.UP;
      }

      widget.onSwiped(direction);
    }
  }
}
