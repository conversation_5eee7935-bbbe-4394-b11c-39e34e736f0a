/*
 * Copyright (C) 2025 The FlorisBoard Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.florisboard.lib.color.schemes

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color


private val primaryLight = Color(0xFF006876)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFA1EFFF)
private val onPrimaryContainerLight = Color(0xFF004E59)
private val secondaryLight = Color(0xFF4A6268)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFCDE7ED)
private val onSecondaryContainerLight = Color(0xFF334A50)
private val tertiaryLight = Color(0xFF545D7E)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFDBE1FF)
private val onTertiaryContainerLight = Color(0xFF3C4665)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF93000A)
private val backgroundLight = Color(0xFFF5FAFC)
private val onBackgroundLight = Color(0xFF171D1E)
private val surfaceLight = Color(0xFFF5FAFC)
private val onSurfaceLight = Color(0xFF171D1E)
private val surfaceVariantLight = Color(0xFFDBE4E6)
private val onSurfaceVariantLight = Color(0xFF3F484A)
private val outlineLight = Color(0xFF6F797B)
private val outlineVariantLight = Color(0xFFBFC8CA)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2B3133)
private val inverseOnSurfaceLight = Color(0xFFECF2F3)
private val inversePrimaryLight = Color(0xFF83D3E3)
private val surfaceDimLight = Color(0xFFD5DBDC)
private val surfaceBrightLight = Color(0xFFF5FAFC)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFEFF5F6)
private val surfaceContainerLight = Color(0xFFE9EFF0)
private val surfaceContainerHighLight = Color(0xFFE3E9EB)
private val surfaceContainerHighestLight = Color(0xFFDEE3E5)

private val primaryDark = Color(0xFF83D3E3)
private val onPrimaryDark = Color(0xFF00363E)
private val primaryContainerDark = Color(0xFF004E59)
private val onPrimaryContainerDark = Color(0xFFA1EFFF)
private val secondaryDark = Color(0xFFB1CBD1)
private val onSecondaryDark = Color(0xFF1C3439)
private val secondaryContainerDark = Color(0xFF334A50)
private val onSecondaryContainerDark = Color(0xFFCDE7ED)
private val tertiaryDark = Color(0xFFBCC5EB)
private val onTertiaryDark = Color(0xFF262F4D)
private val tertiaryContainerDark = Color(0xFF3C4665)
private val onTertiaryContainerDark = Color(0xFFDBE1FF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF0E1416)
private val onBackgroundDark = Color(0xFFDEE3E5)
private val surfaceDark = Color(0xFF0E1416)
private val onSurfaceDark = Color(0xFFDEE3E5)
private val surfaceVariantDark = Color(0xFF3F484A)
private val onSurfaceVariantDark = Color(0xFFBFC8CA)
private val outlineDark = Color(0xFF899295)
private val outlineVariantDark = Color(0xFF3F484A)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFDEE3E5)
private val inverseOnSurfaceDark = Color(0xFF2B3133)
private val inversePrimaryDark = Color(0xFF006876)
private val surfaceDimDark = Color(0xFF0E1416)
private val surfaceBrightDark = Color(0xFF343A3C)
private val surfaceContainerLowestDark = Color(0xFF090F10)
private val surfaceContainerLowDark = Color(0xFF171D1E)
private val surfaceContainerDark = Color(0xFF1B2122)
private val surfaceContainerHighDark = Color(0xFF252B2C)
private val surfaceContainerHighestDark = Color(0xFF303637)

val cyanLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val cyanDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)
