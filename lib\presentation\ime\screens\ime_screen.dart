import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/managers/keyboard_manager.dart';
import '../../../core/managers/theme_manager.dart';
import '../../../core/constants/app_constants.dart';
import '../layouts/text_input_layout.dart';
import '../layouts/media_input_layout.dart';
import '../layouts/clipboard_input_layout.dart';

class IMEScreen extends StatefulWidget {
  const IMEScreen({super.key});

  @override
  State<IMEScreen> createState() => _IMEScreenState();
}

class _IMEScreenState extends State<IMEScreen> {
  final TextEditingController _textController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Keyboard Preview'),
        backgroundColor: Theme.of(context).colorScheme.surface,
      ),
      body: Column(
        children: [
          // Text input area for testing
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const Text(
                    'Type here to test the keyboard:',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  const SizedBox(height: 16),
                  Expanded(
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: TextField(
                        controller: _textController,
                        maxLines: null,
                        expands: true,
                        decoration: const InputDecoration(
                          border: InputBorder.none,
                          hintText: 'Start typing...',
                        ),
                        style: const TextStyle(fontSize: 16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _textController.clear(),
                          child: const Text('Clear'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: () => _insertSampleText(),
                          child: const Text('Sample Text'),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          
          // Keyboard area
          BlocBuilder<KeyboardManager, KeyboardState>(
            builder: (context, keyboardState) {
              return BlocBuilder<ThemeManager, ThemeState>(
                builder: (context, themeState) {
                  return Container(
                    height: 280,
                    decoration: BoxDecoration(
                      color: themeState.currentTheme.colorScheme.background,
                      border: Border(
                        top: BorderSide(
                          color: themeState.currentTheme.colorScheme.outline,
                          width: 1,
                        ),
                      ),
                    ),
                    child: _buildKeyboardLayout(keyboardState),
                  );
                },
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildKeyboardLayout(KeyboardState keyboardState) {
    switch (keyboardState.currentMode) {
      case KeyboardMode.CHARACTERS:
      case KeyboardMode.SYMBOLS:
      case KeyboardMode.NUMERIC:
        return TextInputLayout(
          onTextInput: _handleTextInput,
          onKeyAction: _handleKeyAction,
        );
      case KeyboardMode.MEDIA:
        return MediaInputLayout(
          onEmojiSelected: _handleEmojiSelected,
          onModeChange: _handleModeChange,
        );
      case KeyboardMode.CLIPBOARD:
        return ClipboardInputLayout(
          onClipboardItemSelected: _handleClipboardItemSelected,
          onModeChange: _handleModeChange,
        );
      default:
        return TextInputLayout(
          onTextInput: _handleTextInput,
          onKeyAction: _handleKeyAction,
        );
    }
  }

  void _handleTextInput(String text) {
    final currentText = _textController.text;
    final selection = _textController.selection;
    
    final newText = currentText.replaceRange(
      selection.start,
      selection.end,
      text,
    );
    
    final newSelection = TextSelection.collapsed(
      offset: selection.start + text.length,
    );
    
    _textController.value = TextEditingValue(
      text: newText,
      selection: newSelection,
    );
  }

  void _handleKeyAction(KeyAction action) {
    switch (action) {
      case KeyAction.DELETE:
        _handleDelete();
        break;
      case KeyAction.ENTER:
        _handleTextInput('\n');
        break;
      case KeyAction.SPACE:
        _handleTextInput(' ');
        break;
      case KeyAction.TAB:
        _handleTextInput('\t');
        break;
    }
  }

  void _handleDelete() {
    final currentText = _textController.text;
    final selection = _textController.selection;
    
    if (selection.start > 0) {
      final newText = currentText.replaceRange(
        selection.start - 1,
        selection.end,
        '',
      );
      
      final newSelection = TextSelection.collapsed(
        offset: selection.start - 1,
      );
      
      _textController.value = TextEditingValue(
        text: newText,
        selection: newSelection,
      );
    }
  }

  void _handleEmojiSelected(String emoji) {
    _handleTextInput(emoji);
  }

  void _handleClipboardItemSelected(String text) {
    _handleTextInput(text);
  }

  void _handleModeChange(KeyboardMode mode) {
    context.read<KeyboardManager>().switchMode(mode);
  }

  void _insertSampleText() {
    const sampleText = '''Hello! This is a sample text to test the Kirat Keyboard.

You can use this keyboard to:
• Type letters, numbers, and symbols
• Insert emojis 😀🎉✨
• Access clipboard history
• Use swipe gestures
• Customize themes and settings

The keyboard supports multiple layouts and languages, with advanced features like:
- Word suggestions
- Auto-correction
- Haptic feedback
- Custom themes
- Extension support

Try switching between different modes using the keyboard buttons!''';

    _textController.text = sampleText;
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }
}

enum KeyAction {
  DELETE,
  ENTER,
  SPACE,
  TAB,
}
