import 'dart:async';
import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EmojiManager extends Cubit<EmojiState> {
  EmojiManager() : super(const EmojiState.initial());

  final Map<String, List<Emoji>> _categories = {};
  final EmojiHistory _history = EmojiHistory();
  final Map<String, int> _usage = {};

  Future<void> initialize() async {
    await _loadEmojiData();
    await _loadHistory();
    await _loadUsageData();
    
    emit(state.copyWith(
      categories: Map.from(_categories),
      recentEmojis: _history.recent,
      frequentEmojis: _getFrequentEmojis(),
    ));
  }

  Future<void> _loadEmojiData() async {
    try {
      // For now, use a simple predefined set of emojis
      _categories['recent'] = [];
      _categories['smileys'] = [
        const Emoji(unicode: '😀', name: 'grinning face', keywords: ['happy', 'smile'], category: 'smileys'),
        const Emoji(unicode: '😃', name: 'grinning face with big eyes', keywords: ['happy', 'smile'], category: 'smileys'),
        const Emoji(unicode: '😄', name: 'grinning face with smiling eyes', keywords: ['happy', 'smile'], category: 'smileys'),
        const Emoji(unicode: '😁', name: 'beaming face with smiling eyes', keywords: ['happy', 'smile'], category: 'smileys'),
        const Emoji(unicode: '😆', name: 'grinning squinting face', keywords: ['happy', 'laugh'], category: 'smileys'),
        const Emoji(unicode: '😅', name: 'grinning face with sweat', keywords: ['happy', 'sweat'], category: 'smileys'),
        const Emoji(unicode: '🤣', name: 'rolling on the floor laughing', keywords: ['laugh', 'funny'], category: 'smileys'),
        const Emoji(unicode: '😂', name: 'face with tears of joy', keywords: ['laugh', 'cry'], category: 'smileys'),
        const Emoji(unicode: '🙂', name: 'slightly smiling face', keywords: ['smile'], category: 'smileys'),
        const Emoji(unicode: '🙃', name: 'upside-down face', keywords: ['silly'], category: 'smileys'),
      ];
      
      _categories['animals'] = [
        const Emoji(unicode: '🐶', name: 'dog face', keywords: ['dog', 'pet'], category: 'animals'),
        const Emoji(unicode: '🐱', name: 'cat face', keywords: ['cat', 'pet'], category: 'animals'),
        const Emoji(unicode: '🐭', name: 'mouse face', keywords: ['mouse'], category: 'animals'),
        const Emoji(unicode: '🐹', name: 'hamster', keywords: ['hamster'], category: 'animals'),
        const Emoji(unicode: '🐰', name: 'rabbit face', keywords: ['rabbit'], category: 'animals'),
        const Emoji(unicode: '🦊', name: 'fox', keywords: ['fox'], category: 'animals'),
        const Emoji(unicode: '🐻', name: 'bear', keywords: ['bear'], category: 'animals'),
        const Emoji(unicode: '🐼', name: 'panda', keywords: ['panda'], category: 'animals'),
      ];
      
      _categories['food'] = [
        const Emoji(unicode: '🍎', name: 'red apple', keywords: ['apple', 'fruit'], category: 'food'),
        const Emoji(unicode: '🍌', name: 'banana', keywords: ['banana', 'fruit'], category: 'food'),
        const Emoji(unicode: '🍊', name: 'tangerine', keywords: ['orange', 'fruit'], category: 'food'),
        const Emoji(unicode: '🍓', name: 'strawberry', keywords: ['strawberry', 'fruit'], category: 'food'),
        const Emoji(unicode: '🥝', name: 'kiwi fruit', keywords: ['kiwi', 'fruit'], category: 'food'),
        const Emoji(unicode: '🍅', name: 'tomato', keywords: ['tomato'], category: 'food'),
        const Emoji(unicode: '🥕', name: 'carrot', keywords: ['carrot'], category: 'food'),
        const Emoji(unicode: '🌽', name: 'ear of corn', keywords: ['corn'], category: 'food'),
      ];
    } catch (e) {
      print('Failed to load emoji data: $e');
    }
  }

  Future<void> _loadHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('emoji_history');
      if (historyJson != null) {
        _history.loadFromJson(historyJson);
      }
    } catch (e) {
      print('Failed to load emoji history: $e');
    }
  }

  Future<void> _loadUsageData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usageJson = prefs.getString('emoji_usage');
      if (usageJson != null) {
        final Map<String, dynamic> usageData = json.decode(usageJson);
        _usage.clear();
        usageData.forEach((key, value) {
          _usage[key] = value as int;
        });
      }
    } catch (e) {
      print('Failed to load emoji usage data: $e');
    }
  }

  Future<void> _saveHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('emoji_history', _history.toJson());
    } catch (e) {
      print('Failed to save emoji history: $e');
    }
  }

  Future<void> _saveUsageData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString('emoji_usage', json.encode(_usage));
    } catch (e) {
      print('Failed to save emoji usage data: $e');
    }
  }

  List<Emoji> getEmojisForCategory(String category) {
    if (category == 'recent') {
      return _history.recent;
    }
    return _categories[category] ?? [];
  }

  Future<void> addToHistory(Emoji emoji) async {
    _history.add(emoji);
    _incrementUsage(emoji);
    
    await _saveHistory();
    await _saveUsageData();
    
    emit(state.copyWith(
      recentEmojis: _history.recent,
      frequentEmojis: _getFrequentEmojis(),
    ));
  }

  void _incrementUsage(Emoji emoji) {
    _usage[emoji.unicode] = (_usage[emoji.unicode] ?? 0) + 1;
  }

  List<Emoji> _getFrequentEmojis() {
    final sortedUsage = _usage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));
    
    final frequentEmojis = <Emoji>[];
    for (final entry in sortedUsage.take(24)) {
      final emoji = _findEmojiByUnicode(entry.key);
      if (emoji != null) {
        frequentEmojis.add(emoji);
      }
    }
    
    return frequentEmojis;
  }

  Emoji? _findEmojiByUnicode(String unicode) {
    for (final category in _categories.values) {
      for (final emoji in category) {
        if (emoji.unicode == unicode) {
          return emoji;
        }
      }
    }
    return null;
  }

  List<Emoji> searchEmojis(String query) {
    if (query.isEmpty) return [];
    
    final results = <Emoji>[];
    final lowerQuery = query.toLowerCase();
    
    for (final category in _categories.values) {
      for (final emoji in category) {
        if (emoji.name.toLowerCase().contains(lowerQuery) ||
            emoji.keywords.any((keyword) => keyword.toLowerCase().contains(lowerQuery))) {
          results.add(emoji);
        }
      }
    }
    
    return results;
  }

  void selectCategory(String category) {
    emit(state.copyWith(selectedCategory: category));
  }

  Future<void> clearHistory() async {
    _history.clear();
    await _saveHistory();
    emit(state.copyWith(recentEmojis: []));
  }

  Future<void> clearUsageData() async {
    _usage.clear();
    await _saveUsageData();
    emit(state.copyWith(frequentEmojis: []));
  }
}

class Emoji extends Equatable {
  const Emoji({
    required this.unicode,
    required this.name,
    required this.keywords,
    required this.category,
    this.skinTones = const [],
  });

  final String unicode;
  final String name;
  final List<String> keywords;
  final String category;
  final List<String> skinTones;

  Map<String, dynamic> toJson() {
    return {
      'unicode': unicode,
      'name': name,
      'keywords': keywords,
      'category': category,
      'skinTones': skinTones,
    };
  }

  factory Emoji.fromJson(Map<String, dynamic> json) {
    return Emoji(
      unicode: json['unicode'] as String,
      name: json['name'] as String,
      keywords: (json['keywords'] as List).cast<String>(),
      category: json['category'] as String,
      skinTones: (json['skinTones'] as List?)?.cast<String>() ?? [],
    );
  }

  @override
  List<Object?> get props => [unicode, name, keywords, category, skinTones];
}

class EmojiHistory {
  final List<Emoji> _recent = [];
  final int _maxSize = 24;

  List<Emoji> get recent => List.unmodifiable(_recent);

  void add(Emoji emoji) {
    // Remove if already exists
    _recent.removeWhere((e) => e.unicode == emoji.unicode);
    
    // Add to beginning
    _recent.insert(0, emoji);
    
    // Trim to max size
    if (_recent.length > _maxSize) {
      _recent.removeRange(_maxSize, _recent.length);
    }
  }

  void clear() {
    _recent.clear();
  }

  String toJson() {
    return json.encode(_recent.map((e) => e.toJson()).toList());
  }

  void loadFromJson(String jsonString) {
    try {
      final List<dynamic> data = json.decode(jsonString);
      _recent.clear();
      _recent.addAll(data.map((e) => Emoji.fromJson(e)).toList());
    } catch (e) {
      print('Failed to load emoji history from JSON: $e');
    }
  }
}

class EmojiState extends Equatable {
  const EmojiState({
    required this.categories,
    required this.recentEmojis,
    required this.frequentEmojis,
    required this.searchResults,
    required this.selectedCategory,
    required this.isLoading,
    this.error,
  });

  const EmojiState.initial()
      : categories = const {},
        recentEmojis = const [],
        frequentEmojis = const [],
        searchResults = const [],
        selectedCategory = 'recent',
        isLoading = false,
        error = null;

  final Map<String, List<Emoji>> categories;
  final List<Emoji> recentEmojis;
  final List<Emoji> frequentEmojis;
  final List<Emoji> searchResults;
  final String selectedCategory;
  final bool isLoading;
  final String? error;

  EmojiState copyWith({
    Map<String, List<Emoji>>? categories,
    List<Emoji>? recentEmojis,
    List<Emoji>? frequentEmojis,
    List<Emoji>? searchResults,
    String? selectedCategory,
    bool? isLoading,
    String? error,
  }) {
    return EmojiState(
      categories: categories ?? this.categories,
      recentEmojis: recentEmojis ?? this.recentEmojis,
      frequentEmojis: frequentEmojis ?? this.frequentEmojis,
      searchResults: searchResults ?? this.searchResults,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [
        categories,
        recentEmojis,
        frequentEmojis,
        searchResults,
        selectedCategory,
        isLoading,
        error,
      ];
}
