{"$schema": "https://json-schema.org/draft/2020-12/schema", "$id": "https://schemas.florisboard.org/snygg/v2/stylesheet", "$comment": "THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.", "title": "Snygg Stylesheet Specification", "description": "This document describes the Snygg stylesheet specification.", "type": "object", "properties": {"$schema": {"type": "string", "const": "https://schemas.florisboard.org/snygg/v2/stylesheet"}}, "patternProperties": {"^@defines$": {"$ref": "#/$defs/snygg-defines-property-set"}, "^@font `(?<fontName>[a-zA-Z0-9\\s-]+)`$": {"$ref": "#/$defs/snygg-font-property-set"}, "^(?<elementName>[a-zA-Z0-9-]+)(?<attributesRaw>(?:\\[(?<attrKey>[a-zA-Z0-9-]+)=(?<attrRawValues>(?:`[^`]+`|(?:0|-?[1-9][0-9]*)[.]{2}(?:0|-?[1-9][0-9]*)|(?:0|-?[1-9][0-9]*))(?:,(?:`[^`]+`|(?:0|-?[1-9][0-9]*)[.]{2}(?:0|-?[1-9][0-9]*)|(?:0|-?[1-9][0-9]*)))*)])+)?(?<selectorRaw>:pressed|:focus|:hover|:disabled)?$": {"$ref": "#/$defs/snygg-element-property-set"}}, "additionalProperties": false, "$defs": {"snygg-uri-value": {"type": "string", "pattern": "^\\s*uri[(]\\s*(?<enclosedUri>`flex:/[^`]+`)\\s*[)]\\s*$"}, "snygg-font-style-value": {"type": "string", "enum": ["normal", "italic"]}, "snygg-font-weight-value": {"type": "string", "enum": ["thin", "extra-light", "light", "normal", "medium", "semi-bold", "bold", "extra-bold", "black", "100", "200", "300", "400", "500", "600", "700", "800", "900"]}, "snygg-static-color-value": {"oneOf": [{"type": "string", "pattern": "^\\s*rgba[(]\\s*(?<r>25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(?<g>25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(?<b>25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(?<a>1(?:[.]0)?|0(?:[.][0-9]*)?|[.][0-9]+)\\s*[)]\\s*$"}, {"type": "string", "pattern": "^\\s*rgb[(]\\s*(?<r>25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(?<g>25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\s*,\\s*(?<b>25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9]?[0-9])\\s*[)]\\s*$"}, {"type": "string", "pattern": "^\\s*(?<transparent>transparent)\\s*$"}, {"type": "string", "pattern": "^\\s*(?<hex>#[a-fA-F0-9]{6})\\s*$"}, {"type": "string", "pattern": "^\\s*(?<hex>#[a-fA-F0-9]{8})\\s*$"}]}, "snygg-dynamic-light-color-value": {"type": "string", "pattern": "^\\s*dynamic-light-color[(]\\s*(?<name>primary|onPrimary|primaryContainer|onPrimaryContainer|inversePrimary|secondary|onSecondary|secondaryContainer|onSecondaryContainer|tertiary|onTertiary|tertiaryContainer|onTertiaryContainer|background|onBackground|onSurface|surfaceVariant|onSurfaceVariant|surfaceTint|inverseSurface|inverseOnSurface|error|onError|errorContainer|onErrorContainer|outline|outlineVariant|scrim|surfaceBright|surfaceDim|surfaceContainer|surfaceContainerHigh|surfaceContainerHighest|surfaceContainerLow|surfaceContainerLowest)\\s*[)]\\s*$"}, "snygg-dynamic-dark-color-value": {"type": "string", "pattern": "^\\s*dynamic-dark-color[(]\\s*(?<name>primary|onPrimary|primaryContainer|onPrimaryContainer|inversePrimary|secondary|onSecondary|secondaryContainer|onSecondaryContainer|tertiary|onTertiary|tertiaryContainer|onTertiaryContainer|background|onBackground|onSurface|surfaceVariant|onSurfaceVariant|surfaceTint|inverseSurface|inverseOnSurface|error|onError|errorContainer|onErrorContainer|outline|outlineVariant|scrim|surfaceBright|surfaceDim|surfaceContainer|surfaceContainerHigh|surfaceContainerHighest|surfaceContainerLow|surfaceContainerLowest)\\s*[)]\\s*$"}, "snygg-content-scale-value": {"type": "string", "enum": ["crop", "fill-bounds", "fill-height", "fill-width", "fit", "inside", "none"]}, "snygg-dp-size-value": {"type": "string", "pattern": "^\\s*(?<size>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?|[.][0-9]+)dp\\s*$"}, "snygg-generic-font-family-value": {"type": "string", "enum": ["system", "sans-serif", "serif", "monospace", "cursive"]}, "snygg-custom-font-family-value": {"type": "string", "pattern": "^\\s*(?<enclosedFontName>`(?<fontName>[a-zA-Z0-9\\s_-]+)`)\\s*$"}, "snygg-sp-size-value": {"type": "string", "pattern": "^\\s*(?<size>[1-9][0-9]*(?:[.][0-9]*)?)sp\\s*$"}, "snygg-padding-value": {"oneOf": [{"type": "string", "pattern": "^\\s*(?<paddingStart>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s* \\s*(?<paddingTop>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s* \\s*(?<paddingEnd>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s* \\s*(?<paddingBottom>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*$"}, {"type": "string", "pattern": "^\\s*(?<paddingHorizontal>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s* \\s*(?<paddingVertical>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*$"}, {"type": "string", "pattern": "^\\s*(?<paddingAll>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*$"}]}, "snygg-rectangle-shape-value": {"type": "string", "pattern": "^\\s*rectangle[(]\\s*[)]\\s*$"}, "snygg-circle-shape-value": {"type": "string", "pattern": "^\\s*circle[(]\\s*[)]\\s*$"}, "snygg-rounded-corner-dp-shape-value": {"type": "string", "pattern": "^\\s*rounded-corner[(]\\s*(?<cornerSizeTopStart>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*,\\s*(?<cornerSizeTopEnd>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*,\\s*(?<cornerSizeBottomEnd>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*,\\s*(?<cornerSizeBottomStart>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*[)]\\s*$"}, "snygg-rounded-corner-percent-shape-value": {"type": "string", "pattern": "^\\s*rounded-corner[(]\\s*(?<cornerSizeTopStart>100|[1-9]?[0-9])%\\s*,\\s*(?<cornerSizeTopEnd>100|[1-9]?[0-9])%\\s*,\\s*(?<cornerSizeBottomEnd>100|[1-9]?[0-9])%\\s*,\\s*(?<cornerSizeBottomStart>100|[1-9]?[0-9])%\\s*[)]\\s*$"}, "snygg-cut-corner-dp-shape-value": {"type": "string", "pattern": "^\\s*cut-corner[(]\\s*(?<cornerSizeTopStart>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*,\\s*(?<cornerSizeTopEnd>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*,\\s*(?<cornerSizeBottomEnd>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*,\\s*(?<cornerSizeBottomStart>(?:0|[1-9][0-9]*)(?:[.][0-9]*)?)dp\\s*[)]\\s*$"}, "snygg-cut-corner-percent-shape-value": {"type": "string", "pattern": "^\\s*cut-corner[(]\\s*(?<cornerSizeTopStart>100|[1-9]?[0-9])%\\s*,\\s*(?<cornerSizeTopEnd>100|[1-9]?[0-9])%\\s*,\\s*(?<cornerSizeBottomEnd>100|[1-9]?[0-9])%\\s*,\\s*(?<cornerSizeBottomStart>100|[1-9]?[0-9])%\\s*[)]\\s*$"}, "snygg-yes-value": {"type": "string", "const": "yes"}, "snygg-no-value": {"type": "string", "const": "no"}, "snygg-text-align-value": {"type": "string", "enum": ["left", "right", "center", "justify", "start", "end"]}, "snygg-text-decoration-line-value": {"type": "string", "enum": ["none", "underline", "line-through"]}, "snygg-text-max-lines-value": {"type": "string", "pattern": "^\\s*(?<textMaxLines>none|[1-9][0-9]*)\\s*$"}, "snygg-text-overflow-value": {"type": "string", "enum": ["clip", "ellipsis", "visible"]}, "snygg-inherit-value": {"type": "string", "const": "inherit"}, "snygg-defined-var-value": {"type": "string", "pattern": "^\\s*var[(]\\s*(?<varKey>--[a-zA-Z0-9-]+)\\s*[)]\\s*$"}, "snygg-defines-property-set": {"type": "object", "patternProperties": {"--[a-zA-Z0-9-]+": {"oneOf": [{"$ref": "#/$defs/snygg-uri-value"}, {"$ref": "#/$defs/snygg-font-style-value"}, {"$ref": "#/$defs/snygg-font-weight-value"}, {"$ref": "#/$defs/snygg-static-color-value"}, {"$ref": "#/$defs/snygg-dynamic-light-color-value"}, {"$ref": "#/$defs/snygg-dynamic-dark-color-value"}, {"$ref": "#/$defs/snygg-content-scale-value"}, {"$ref": "#/$defs/snygg-dp-size-value"}, {"$ref": "#/$defs/snygg-generic-font-family-value"}, {"$ref": "#/$defs/snygg-custom-font-family-value"}, {"$ref": "#/$defs/snygg-sp-size-value"}, {"$ref": "#/$defs/snygg-padding-value"}, {"$ref": "#/$defs/snygg-rectangle-shape-value"}, {"$ref": "#/$defs/snygg-circle-shape-value"}, {"$ref": "#/$defs/snygg-rounded-corner-dp-shape-value"}, {"$ref": "#/$defs/snygg-rounded-corner-percent-shape-value"}, {"$ref": "#/$defs/snygg-cut-corner-dp-shape-value"}, {"$ref": "#/$defs/snygg-cut-corner-percent-shape-value"}, {"$ref": "#/$defs/snygg-yes-value"}, {"$ref": "#/$defs/snygg-no-value"}, {"$ref": "#/$defs/snygg-text-align-value"}, {"$ref": "#/$defs/snygg-text-decoration-line-value"}, {"$ref": "#/$defs/snygg-text-max-lines-value"}, {"$ref": "#/$defs/snygg-text-overflow-value"}, {"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}]}}, "additionalProperties": false}, "snygg-font-property-set": {"type": "array", "items": {"type": "object", "properties": {"src": {"oneOf": [{"$ref": "#/$defs/snygg-uri-value"}]}, "font-style": {"oneOf": [{"$ref": "#/$defs/snygg-font-style-value"}]}, "font-weight": {"oneOf": [{"$ref": "#/$defs/snygg-font-weight-value"}]}}, "additionalProperties": false, "required": ["src"]}}, "snygg-element-property-set": {"type": "object", "properties": {"background": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-static-color-value"}, {"$ref": "#/$defs/snygg-dynamic-light-color-value"}, {"$ref": "#/$defs/snygg-dynamic-dark-color-value"}]}, "foreground": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-static-color-value"}, {"$ref": "#/$defs/snygg-dynamic-light-color-value"}, {"$ref": "#/$defs/snygg-dynamic-dark-color-value"}]}, "background-image": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-uri-value"}]}, "content-scale": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-content-scale-value"}]}, "border-color": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-static-color-value"}, {"$ref": "#/$defs/snygg-dynamic-light-color-value"}, {"$ref": "#/$defs/snygg-dynamic-dark-color-value"}]}, "border-style": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}]}, "border-width": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-dp-size-value"}]}, "font-family": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-generic-font-family-value"}, {"$ref": "#/$defs/snygg-custom-font-family-value"}]}, "font-size": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-sp-size-value"}]}, "font-style": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-font-style-value"}]}, "font-weight": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-font-weight-value"}]}, "letter-spacing": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-sp-size-value"}]}, "line-height": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-sp-size-value"}]}, "margin": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-padding-value"}]}, "padding": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-padding-value"}]}, "shadow-color": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-static-color-value"}, {"$ref": "#/$defs/snygg-dynamic-light-color-value"}, {"$ref": "#/$defs/snygg-dynamic-dark-color-value"}]}, "shadow-elevation": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-dp-size-value"}]}, "shape": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-rectangle-shape-value"}, {"$ref": "#/$defs/snygg-circle-shape-value"}, {"$ref": "#/$defs/snygg-rounded-corner-dp-shape-value"}, {"$ref": "#/$defs/snygg-rounded-corner-percent-shape-value"}, {"$ref": "#/$defs/snygg-cut-corner-dp-shape-value"}, {"$ref": "#/$defs/snygg-cut-corner-percent-shape-value"}]}, "clip": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-yes-value"}, {"$ref": "#/$defs/snygg-no-value"}]}, "text-align": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-text-align-value"}]}, "text-decoration-line": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-text-decoration-line-value"}]}, "text-max-lines": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-text-max-lines-value"}]}, "text-overflow": {"oneOf": [{"$ref": "#/$defs/snygg-inherit-value"}, {"$ref": "#/$defs/snygg-defined-var-value"}, {"$ref": "#/$defs/snygg-text-overflow-value"}]}}, "additionalProperties": false}}}