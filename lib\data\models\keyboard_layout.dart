import 'package:equatable/equatable.dart';

import '../../core/constants/app_constants.dart';
import '../../core/constants/key_codes.dart';
import 'key_data.dart';

class KeyboardLayout extends Equatable {
  const KeyboardLayout({
    required this.id,
    required this.name,
    required this.mode,
    required this.rows,
    this.language = 'en',
    this.country = 'US',
    this.properties = const {},
    this.authors = const [],
    this.description,
  });

  final String id;
  final String name;
  final KeyboardMode mode;
  final List<KeyboardRow> rows;
  final String language;
  final String country;
  final Map<String, dynamic> properties;
  final List<String> authors;
  final String? description;

  @override
  List<Object?> get props => [
        id,
        name,
        mode,
        rows,
        language,
        country,
        properties,
        authors,
        description,
      ];
}

class KeyboardRow extends Equatable {
  const KeyboardRow({
    required this.keys,
    this.height = 1.0,
    this.properties = const {},
  });

  final List<KeyData> keys;
  final double height;
  final Map<String, dynamic> properties;

  @override
  List<Object?> get props => [keys, height, properties];
}

// Layout arrangement for different screen sizes
class LayoutArrangement extends Equatable {
  const LayoutArrangement({
    required this.id,
    required this.mode,
    required this.rows,
    this.properties = const {},
  });

  final String id;
  final KeyboardMode mode;
  final List<LayoutRow> rows;
  final Map<String, dynamic> properties;

  @override
  List<Object?> get props => [id, mode, rows, properties];
}

class LayoutRow extends Equatable {
  const LayoutRow({
    required this.keys,
    this.height = 1.0,
    this.properties = const {},
  });

  final List<LayoutKey> keys;
  final double height;
  final Map<String, dynamic> properties;

  @override
  List<Object?> get props => [keys, height, properties];
}

class LayoutKey extends Equatable {
  const LayoutKey({
    required this.spec,
    this.width = 1.0,
    this.properties = const {},
  });

  final String spec;
  final double width;
  final Map<String, dynamic> properties;

  @override
  List<Object?> get props => [spec, width, properties];
}

// Predefined layouts
class PredefinedLayouts {
  static KeyboardLayout get qwertyCharacters => const KeyboardLayout(
        id: 'qwerty_characters',
        name: 'QWERTY Characters',
        mode: KeyboardMode.characters,
        language: 'en',
        country: 'US',
        rows: [
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.Q, type: KeyType.character, label: 'q'),
              KeyData(code: KeyCode.W, type: KeyType.character, label: 'w'),
              KeyData(code: KeyCode.E, type: KeyType.character, label: 'e'),
              KeyData(code: KeyCode.R, type: KeyType.character, label: 'r'),
              KeyData(code: KeyCode.T, type: KeyType.character, label: 't'),
              KeyData(code: KeyCode.Y, type: KeyType.character, label: 'y'),
              KeyData(code: KeyCode.U, type: KeyType.character, label: 'u'),
              KeyData(code: KeyCode.I, type: KeyType.character, label: 'i'),
              KeyData(code: KeyCode.O, type: KeyType.character, label: 'o'),
              KeyData(code: KeyCode.P, type: KeyType.character, label: 'p'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.A, type: KeyType.character, label: 'a'),
              KeyData(code: KeyCode.S, type: KeyType.character, label: 's'),
              KeyData(code: KeyCode.D, type: KeyType.character, label: 'd'),
              KeyData(code: KeyCode.F, type: KeyType.character, label: 'f'),
              KeyData(code: KeyCode.G, type: KeyType.character, label: 'g'),
              KeyData(code: KeyCode.H, type: KeyType.character, label: 'h'),
              KeyData(code: KeyCode.J, type: KeyType.character, label: 'j'),
              KeyData(code: KeyCode.K, type: KeyType.character, label: 'k'),
              KeyData(code: KeyCode.L, type: KeyType.character, label: 'l'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData.SHIFT,
              KeyData(code: KeyCode.Z, type: KeyType.character, label: 'z'),
              KeyData(code: KeyCode.X, type: KeyType.character, label: 'x'),
              KeyData(code: KeyCode.C, type: KeyType.character, label: 'c'),
              KeyData(code: KeyCode.V, type: KeyType.character, label: 'v'),
              KeyData(code: KeyCode.B, type: KeyType.character, label: 'b'),
              KeyData(code: KeyCode.N, type: KeyType.character, label: 'n'),
              KeyData(code: KeyCode.M, type: KeyType.character, label: 'm'),
              KeyData.DELETE,
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(
                  code: KeyCode.DIGIT_1, type: KeyType.numeric, label: '123'),
              KeyData.EMOJI_SWITCH,
              KeyData.SPACE,
              KeyData(
                  code: KeyCode.PERIOD, type: KeyType.character, label: '.'),
              KeyData.ENTER,
            ],
          ),
        ],
      );

  static KeyboardLayout get qwertySymbols => const KeyboardLayout(
        id: 'qwerty_symbols',
        name: 'QWERTY Symbols',
        mode: KeyboardMode.symbols,
        language: 'en',
        country: 'US',
        rows: [
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.DIGIT_1, type: KeyType.numeric, label: '1'),
              KeyData(code: KeyCode.DIGIT_2, type: KeyType.numeric, label: '2'),
              KeyData(code: KeyCode.DIGIT_3, type: KeyType.numeric, label: '3'),
              KeyData(code: KeyCode.DIGIT_4, type: KeyType.numeric, label: '4'),
              KeyData(code: KeyCode.DIGIT_5, type: KeyType.numeric, label: '5'),
              KeyData(code: KeyCode.DIGIT_6, type: KeyType.numeric, label: '6'),
              KeyData(code: KeyCode.DIGIT_7, type: KeyType.numeric, label: '7'),
              KeyData(code: KeyCode.DIGIT_8, type: KeyType.numeric, label: '8'),
              KeyData(code: KeyCode.DIGIT_9, type: KeyType.numeric, label: '9'),
              KeyData(code: KeyCode.DIGIT_0, type: KeyType.numeric, label: '0'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.MINUS, type: KeyType.character, label: '-'),
              KeyData(code: KeyCode.SLASH, type: KeyType.character, label: '/'),
              KeyData(code: KeyCode.COLON, type: KeyType.character, label: ':'),
              KeyData(
                  code: KeyCode.SEMICOLON, type: KeyType.character, label: ';'),
              KeyData(
                  code: KeyCode.LEFT_PAREN,
                  type: KeyType.character,
                  label: '('),
              KeyData(
                  code: KeyCode.RIGHT_PAREN,
                  type: KeyType.character,
                  label: ')'),
              KeyData(
                  code: KeyCode.DOLLAR, type: KeyType.character, label: '\$'),
              KeyData(
                  code: KeyCode.AMPERSAND, type: KeyType.character, label: '&'),
              KeyData(code: KeyCode.AT, type: KeyType.character, label: '@'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(
                  code: KeyCode.MODE_CHANGE,
                  type: KeyType.function,
                  label: '=\\<'),
              KeyData(
                  code: KeyCode.PERIOD, type: KeyType.character, label: '.'),
              KeyData(code: KeyCode.COMMA, type: KeyType.character, label: ','),
              KeyData(
                  code: KeyCode.QUESTION, type: KeyType.character, label: '?'),
              KeyData(
                  code: KeyCode.EXCLAMATION,
                  type: KeyType.character,
                  label: '!'),
              KeyData(
                  code: KeyCode.APOSTROPHE,
                  type: KeyType.character,
                  label: "'"),
              KeyData(code: KeyCode.QUOTE, type: KeyType.character, label: '"'),
              KeyData.DELETE,
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(
                  code: KeyCode.MODE_CHANGE,
                  type: KeyType.function,
                  label: 'ABC'),
              KeyData.EMOJI_SWITCH,
              KeyData.SPACE,
              KeyData(
                  code: KeyCode.PERIOD, type: KeyType.character, label: '.'),
              KeyData.ENTER,
            ],
          ),
        ],
      );

  static KeyboardLayout get numericLayout => const KeyboardLayout(
        id: 'numeric',
        name: 'Numeric',
        mode: KeyboardMode.numeric,
        language: 'en',
        country: 'US',
        rows: [
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.DIGIT_1, type: KeyType.numeric, label: '1'),
              KeyData(code: KeyCode.DIGIT_2, type: KeyType.numeric, label: '2'),
              KeyData(code: KeyCode.DIGIT_3, type: KeyType.numeric, label: '3'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.DIGIT_4, type: KeyType.numeric, label: '4'),
              KeyData(code: KeyCode.DIGIT_5, type: KeyType.numeric, label: '5'),
              KeyData(code: KeyCode.DIGIT_6, type: KeyType.numeric, label: '6'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(code: KeyCode.DIGIT_7, type: KeyType.numeric, label: '7'),
              KeyData(code: KeyCode.DIGIT_8, type: KeyType.numeric, label: '8'),
              KeyData(code: KeyCode.DIGIT_9, type: KeyType.numeric, label: '9'),
            ],
          ),
          KeyboardRow(
            keys: [
              KeyData(
                  code: KeyCode.PERIOD, type: KeyType.character, label: '.'),
              KeyData(code: KeyCode.DIGIT_0, type: KeyType.numeric, label: '0'),
              KeyData.DELETE,
            ],
          ),
        ],
      );

  static List<KeyboardLayout> get allLayouts => [
        qwertyCharacters,
        qwertySymbols,
        numericLayout,
      ];
}
