import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/managers/smartbar_manager.dart';
import '../layouts/text_input_layout.dart';

class SmartbarWidget extends StatelessWidget {
  const SmartbarWidget({
    super.key,
    required this.onSuggestionTap,
    required this.onActionTap,
  });

  final Function(String) onSuggestionTap;
  final Function(SmartbarAction) onActionTap;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<SmartbarManager, SmartbarState>(
      builder: (context, state) {
        if (!state.enabled) {
          return const SizedBox.shrink();
        }

        return Container(
          height: 40,
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            border: Border(
              bottom: BorderSide(
                color: Theme.of(context).colorScheme.outline,
                width: 1,
              ),
            ),
          ),
          child: Row(
            children: [
              // Quick actions
              IconButton(
                onPressed: () => onActionTap(SmartbarAction.clipboard),
                icon: const Icon(Icons.content_paste, size: 20),
                tooltip: 'Clipboard',
              ),
              IconButton(
                onPressed: () => onActionTap(SmartbarAction.emoji),
                icon: const Icon(Icons.emoji_emotions, size: 20),
                tooltip: 'Emojis',
              ),

              // Suggestions
              Expanded(
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: state.suggestions.length,
                  itemBuilder: (context, index) {
                    final suggestion = state.suggestions[index];
                    return Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: ActionChip(
                        label: Text(
                          suggestion.text,
                          style: const TextStyle(fontSize: 14),
                        ),
                        onPressed: () => onSuggestionTap(suggestion.text),
                        backgroundColor: Theme.of(context)
                            .colorScheme
                            .surfaceContainerHighest,
                      ),
                    );
                  },
                ),
              ),

              // Settings
              IconButton(
                onPressed: () => onActionTap(SmartbarAction.settings),
                icon: const Icon(Icons.settings, size: 20),
                tooltip: 'Settings',
              ),
            ],
          ),
        );
      },
    );
  }
}
