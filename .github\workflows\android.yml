name: FlorisBoard CI

on:
  push:
    branches: [ main ]
    paths-ignore:
      - ".github/ISSUE_TEMPLATE/**"
      - ".github/FUNDING.yml"
      - ".editorconfig"
      - "fastlane/**"
      - "CONTRIBUTING.md"
      - "CODE_OF_CONDUCT.md"
      - "LICENSE"
      - "README.md"
      - "ROADMAP.md"
  pull_request:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4
        with:
          submodules: recursive

      - uses: gradle/actions/wrapper-validation@v3
      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: 17
          distribution: temurin
      - name: Set up CMake and Ninja
        uses: lukka/get-cmake@latest
      - name: Build with Gradle
        run: ./gradlew clean assembleDebug
      - uses: actions/upload-artifact@v4
        with:
          name: app-debug.apk
          path: app/build/outputs/apk/debug/app-debug.apk
