import 'dart:async';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../constants/app_constants.dart';
import '../preferences/preferences_manager.dart';
import '../services/ime_service.dart';

class SmartbarManager extends Cubit<SmartbarState> {
  SmartbarManager() : super(const SmartbarState.initial());

  final SuggestionEngine _suggestionEngine = SuggestionEngine();
  Timer? _suggestionTimer;

  Future<void> initialize() async {
    await _suggestionEngine.initialize();
    emit(state.copyWith(enabled: PreferencesManager.instance.smartbar.enabled));
  }

  void updateSuggestions(String currentWord, String context) {
    if (!state.enabled || !PreferencesManager.instance.smartbar.suggestionsEnabled) {
      return;
    }

    _suggestionTimer?.cancel();
    _suggestionTimer = Timer(const Duration(milliseconds: 100), () async {
      final suggestions = await _suggestionEngine.getSuggestions(currentWord, context);
      emit(state.copyWith(suggestions: suggestions));
    });
  }

  void applySuggestion(SuggestionCandidate suggestion) {
    IMEService.commitText(suggestion.text);
    _suggestionEngine.recordSelection(suggestion);
    emit(state.copyWith(suggestions: []));
  }

  void clearSuggestions() {
    emit(state.copyWith(suggestions: []));
  }

  void setEnabled(bool enabled) {
    emit(state.copyWith(enabled: enabled));
    PreferencesManager.instance.smartbar.enabled = enabled;
  }

  @override
  Future<void> close() {
    _suggestionTimer?.cancel();
    return super.close();
  }
}

class SuggestionCandidate extends Equatable {
  const SuggestionCandidate({
    required this.text,
    required this.confidence,
    required this.type,
    this.metadata = const {},
  });

  final String text;
  final double confidence;
  final SuggestionType type;
  final Map<String, dynamic> metadata;

  @override
  List<Object?> get props => [text, confidence, type, metadata];
}

class SmartbarState extends Equatable {
  const SmartbarState({
    required this.enabled,
    required this.suggestions,
    required this.isLoading,
    this.error,
  });

  const SmartbarState.initial()
      : enabled = true,
        suggestions = const [],
        isLoading = false,
        error = null;

  final bool enabled;
  final List<SuggestionCandidate> suggestions;
  final bool isLoading;
  final String? error;

  SmartbarState copyWith({
    bool? enabled,
    List<SuggestionCandidate>? suggestions,
    bool? isLoading,
    String? error,
  }) {
    return SmartbarState(
      enabled: enabled ?? this.enabled,
      suggestions: suggestions ?? this.suggestions,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [enabled, suggestions, isLoading, error];
}

// Simple suggestion engine
class SuggestionEngine {
  final Map<String, List<String>> _dictionary = {};
  final Map<String, int> _wordFrequency = {};

  Future<void> initialize() async {
    await _loadDictionary();
    await _loadUserData();
  }

  Future<void> _loadDictionary() async {
    try {
      // For now, use a simple predefined dictionary
      _dictionary['the'] = ['quick', 'best', 'most', 'only'];
      _dictionary['and'] = ['the', 'then', 'now', 'also'];
      _dictionary['to'] = ['be', 'do', 'go', 'see'];
      _dictionary['of'] = ['the', 'course', 'all', 'them'];
      _dictionary['a'] = ['new', 'good', 'great', 'little'];
      _dictionary['in'] = ['the', 'order', 'case', 'fact'];
      
      // Common words with frequencies
      _wordFrequency['the'] = 1000000;
      _wordFrequency['and'] = 800000;
      _wordFrequency['to'] = 700000;
      _wordFrequency['of'] = 600000;
      _wordFrequency['a'] = 500000;
      _wordFrequency['in'] = 400000;
      _wordFrequency['is'] = 350000;
      _wordFrequency['it'] = 300000;
      _wordFrequency['you'] = 250000;
      _wordFrequency['that'] = 200000;
      _wordFrequency['he'] = 180000;
      _wordFrequency['was'] = 160000;
      _wordFrequency['for'] = 140000;
      _wordFrequency['on'] = 120000;
      _wordFrequency['are'] = 100000;
    } catch (e) {
      print('Failed to load dictionary: $e');
    }
  }

  Future<void> _loadUserData() async {
    // Load user's personal dictionary and frequency data
  }

  Future<List<SuggestionCandidate>> getSuggestions(String currentWord, String context) async {
    final suggestions = <SuggestionCandidate>[];
    
    if (currentWord.isEmpty) {
      // Next word predictions based on context
      suggestions.addAll(await _getNextWordPredictions(context));
    } else {
      // Word completions and corrections
      suggestions.addAll(await _getWordCompletions(currentWord));
      suggestions.addAll(await _getWordCorrections(currentWord));
    }
    
    // Sort by confidence and return top suggestions
    suggestions.sort((a, b) => b.confidence.compareTo(a.confidence));
    return suggestions.take(3).toList();
  }

  Future<List<SuggestionCandidate>> _getWordCompletions(String prefix) async {
    final completions = <SuggestionCandidate>[];
    
    for (final word in _wordFrequency.keys) {
      if (word.startsWith(prefix.toLowerCase()) && word != prefix.toLowerCase()) {
        final frequency = _wordFrequency[word] ?? 0;
        final confidence = _calculateCompletionConfidence(prefix, word, frequency);
        
        completions.add(SuggestionCandidate(
          text: word,
          confidence: confidence,
          type: SuggestionType.WORD_COMPLETION,
        ));
      }
    }
    
    return completions;
  }

  Future<List<SuggestionCandidate>> _getWordCorrections(String word) async {
    final corrections = <SuggestionCandidate>[];
    
    for (final dictWord in _wordFrequency.keys) {
      final distance = _levenshteinDistance(word.toLowerCase(), dictWord);
      if (distance <= 2 && distance > 0) {
        final frequency = _wordFrequency[dictWord] ?? 0;
        final confidence = _calculateCorrectionConfidence(word, dictWord, distance, frequency);
        
        corrections.add(SuggestionCandidate(
          text: dictWord,
          confidence: confidence,
          type: SuggestionType.CORRECTION,
        ));
      }
    }
    
    return corrections;
  }

  Future<List<SuggestionCandidate>> _getNextWordPredictions(String context) async {
    // Simple bigram-based next word prediction
    final words = context.toLowerCase().split(' ');
    if (words.isEmpty) return [];
    
    final lastWord = words.last;
    final predictions = <SuggestionCandidate>[];
    
    // Get common next words
    final commonNextWords = _getCommonNextWords(lastWord);
    
    for (final word in commonNextWords) {
      predictions.add(SuggestionCandidate(
        text: word,
        confidence: 0.5,
        type: SuggestionType.NEXT_WORD_PREDICTION,
      ));
    }
    
    return predictions;
  }

  List<String> _getCommonNextWords(String word) {
    return _dictionary[word] ?? ['the', 'and', 'to', 'of'];
  }

  double _calculateCompletionConfidence(String prefix, String word, int frequency) {
    final lengthRatio = prefix.length / word.length;
    final frequencyScore = (frequency / 1000000).clamp(0.0, 1.0);
    return (lengthRatio * 0.7 + frequencyScore * 0.3).clamp(0.0, 1.0);
  }

  double _calculateCorrectionConfidence(String input, String correction, int distance, int frequency) {
    final distanceScore = 1.0 - (distance / input.length);
    final frequencyScore = (frequency / 1000000).clamp(0.0, 1.0);
    return (distanceScore * 0.8 + frequencyScore * 0.2).clamp(0.0, 1.0);
  }

  int _levenshteinDistance(String s1, String s2) {
    final matrix = List.generate(s1.length + 1, 
        (i) => List.generate(s2.length + 1, (j) => 0));

    for (int i = 0; i <= s1.length; i++) {
      matrix[i][0] = i;
    }
    for (int j = 0; j <= s2.length; j++) {
      matrix[0][j] = j;
    }

    for (int i = 1; i <= s1.length; i++) {
      for (int j = 1; j <= s2.length; j++) {
        final cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }

    return matrix[s1.length][s2.length];
  }

  void recordSelection(SuggestionCandidate suggestion) {
    // Record user selection for learning
    _wordFrequency[suggestion.text] = (_wordFrequency[suggestion.text] ?? 0) + 1;
  }
}
