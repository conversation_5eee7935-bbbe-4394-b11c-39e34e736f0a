import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/managers/keyboard_manager.dart';
import '../../../core/managers/smartbar_manager.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/constants/key_codes.dart';
import '../../../data/models/key_data.dart';
import '../widgets/smartbar_widget.dart';
import '../widgets/keyboard_layout_widget.dart';
import '../../ime/screens/ime_screen.dart';

class TextInputLayout extends StatelessWidget {
  const TextInputLayout({
    super.key,
    required this.onTextInput,
    required this.onKeyAction,
  });

  final Function(String) onTextInput;
  final Function(KeyAction) onKeyAction;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<KeyboardManager, KeyboardState>(
      builder: (context, state) {
        if (state.currentLayout == null) {
          return const Center(
            child: CircularProgressIndicator(),
          );
        }

        return Column(
          children: [
            // Smartbar (suggestions and actions)
            SmartbarWidget(
              onSuggestionTap: (suggestion) {
                onTextInput(suggestion);
              },
              onActionTap: (action) {
                _handleSmartbarAction(context, action);
              },
            ),

            // Main keyboard
            Expanded(
              child: KeyboardLayoutWidget(
                layout: state.currentLayout!,
                onKeyPressed: (keyData) {
                  _handleKeyPress(context, keyData);
                },
                onKeyLongPressed: (keyData) {
                  _handleLongPress(context, keyData);
                },
                onKeySwiped: (keyData, direction) {
                  _handleSwipe(context, keyData, direction);
                },
              ),
            ),
          ],
        );
      },
    );
  }

  void _handleKeyPress(BuildContext context, KeyData keyData) {
    final keyboardManager = context.read<KeyboardManager>();

    // Handle the key press through the manager
    keyboardManager.handleKeyPress(keyData);

    // Also handle it locally for the preview
    switch (keyData.type) {
      case KeyType.CHARACTER:
      case KeyType.NUMERIC:
        if (keyData.label != null) {
          String text = keyData.label!;

          // Apply shift state
          final keyboardState = keyboardManager.state;
          if (keyboardState.isShifted && keyData.type == KeyType.CHARACTER) {
            text = text.toUpperCase();
          }

          onTextInput(text);

          // Auto-unshift after character input (unless caps lock)
          if (keyboardState.inputShiftState ==
              InputShiftState.SHIFTED_AUTOMATIC) {
            // This would be handled by the keyboard manager
          }
        }
        break;
      case KeyType.FUNCTION:
        _handleFunctionKey(context, keyData);
        break;
      case KeyType.MODIFIER:
        // Modifier keys are handled by the keyboard manager
        break;
      default:
        break;
    }
  }

  void _handleFunctionKey(BuildContext context, KeyData keyData) {
    switch (keyData.code) {
      case KeyCode.SPACE:
        onKeyAction(KeyAction.SPACE);
        break;
      case KeyCode.ENTER:
        onKeyAction(KeyAction.ENTER);
        break;
      case KeyCode.DELETE:
        onKeyAction(KeyAction.DELETE);
        break;
      case KeyCode.TAB:
        onKeyAction(KeyAction.TAB);
        break;
      case KeyCode.MODE_CHANGE:
        _handleModeChange(context);
        break;
      case KeyCode.EMOJI_SWITCH:
        context.read<KeyboardManager>().switchMode(KeyboardMode.media);
        break;
      case KeyCode.CLIPBOARD_SWITCH:
        context.read<KeyboardManager>().switchMode(KeyboardMode.clipboard);
        break;
      case KeyCode.SETTINGS:
        // Open settings - this would be handled by platform channel
        break;
      default:
        break;
    }
  }

  void _handleModeChange(BuildContext context) {
    final keyboardManager = context.read<KeyboardManager>();
    final currentMode = keyboardManager.state.currentMode;

    KeyboardMode newMode;
    switch (currentMode) {
      case KeyboardMode.characters:
        newMode = KeyboardMode.symbols;
        break;
      case KeyboardMode.symbols:
        newMode = KeyboardMode.characters;
        break;
      case KeyboardMode.numeric:
        newMode = KeyboardMode.characters;
        break;
      default:
        newMode = KeyboardMode.characters;
    }

    keyboardManager.switchMode(newMode);
  }

  void _handleLongPress(BuildContext context, KeyData keyData) {
    final keyboardManager = context.read<KeyboardManager>();
    keyboardManager.handleKeyLongPress(keyData);

    // Handle long press actions for preview
    switch (keyData.code) {
      case KeyCode.SPACE:
        // Show language switcher or cursor movement
        _showSpaceLongPressOptions(context);
        break;
      case KeyCode.DELETE:
        // Delete word
        _deleteWord(context);
        break;
      default:
        // Show popup alternatives if available
        if (keyData.popup != null) {
          _showKeyPopup(context, keyData);
        }
        break;
    }
  }

  void _handleSwipe(
      BuildContext context, KeyData keyData, SwipeDirection direction) {
    final keyboardManager = context.read<KeyboardManager>();
    keyboardManager.handleSwipeGesture(keyData, direction);

    // Handle swipe actions for preview
    switch (direction) {
      case SwipeDirection.up:
        if (keyData.code == KeyCode.SPACE) {
          // Move cursor up or show language picker
        }
        break;
      case SwipeDirection.down:
        // Hide keyboard or other action
        break;
      case SwipeDirection.left:
        if (keyData.code == KeyCode.SPACE) {
          // Move cursor left
          _moveCursor(context, -1);
        }
        break;
      case SwipeDirection.right:
        if (keyData.code == KeyCode.SPACE) {
          // Move cursor right
          _moveCursor(context, 1);
        }
        break;
    }
  }

  void _handleSmartbarAction(BuildContext context, SmartbarAction action) {
    switch (action) {
      case SmartbarAction.clipboard:
        context.read<KeyboardManager>().switchMode(KeyboardMode.clipboard);
        break;
      case SmartbarAction.emoji:
        context.read<KeyboardManager>().switchMode(KeyboardMode.media);
        break;
      case SmartbarAction.settings:
        // Open settings
        break;
    }
  }

  void _showSpaceLongPressOptions(BuildContext context) {
    // This would show a popup with language options or cursor movement
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Space Options'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: Icon(Icons.language),
              title: Text('Switch Language'),
            ),
            ListTile(
              leading: Icon(Icons.keyboard_arrow_left),
              title: Text('Move Cursor'),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _deleteWord(BuildContext context) {
    // This would delete the last word
    // For now, just delete multiple characters
    for (int i = 0; i < 5; i++) {
      onKeyAction(KeyAction.DELETE);
    }
  }

  void _showKeyPopup(BuildContext context, KeyData keyData) {
    if (keyData.popup == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Alternatives for "${keyData.label}"'),
        content: Wrap(
          children: keyData.popup!.main
              .map(
                (altKey) => TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                    if (altKey.label != null) {
                      onTextInput(altKey.label!);
                    }
                  },
                  child: Text(altKey.label ?? ''),
                ),
              )
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }

  void _moveCursor(BuildContext context, int offset) {
    // This would move the cursor in the text field
    // For the preview, we can't actually move the cursor
    // but in a real IME this would work
  }
}

enum SmartbarAction {
  clipboard,
  emoji,
  settings,
}
