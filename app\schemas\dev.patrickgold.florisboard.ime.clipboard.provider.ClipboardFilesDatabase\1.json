{"formatVersion": 1, "database": {"version": 1, "identityHash": "d573e2ae2cbe7026957bc8d8fda291f3", "entities": [{"tableName": "clipboard_files", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER NOT NULL, `_display_name` TEXT NOT NULL, `_size` INTEGER NOT NULL, `mimeTypes` TEXT NOT NULL, PRIMARY KEY(`_id`))", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "displayName", "columnName": "_display_name", "affinity": "TEXT", "notNull": true}, {"fieldPath": "size", "columnName": "_size", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeTypes", "columnName": "mimeTypes", "affinity": "TEXT", "notNull": true}], "primaryKey": {"columnNames": ["_id"], "autoGenerate": false}, "indices": [{"name": "index_clipboard_files__id", "unique": false, "columnNames": ["_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_clipboard_files__id` ON `${TABLE_NAME}` (`_id`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, 'd573e2ae2cbe7026957bc8d8fda291f3')"]}}