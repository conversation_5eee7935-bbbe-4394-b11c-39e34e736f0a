<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">FlorisBoard</string>
    <!-- One-handed strings -->
    <!-- Media strings -->
    <string name="media__tab__emojis" comment="Tab description for emojis in the media UI">ⵉⵎⵓⵊⵉⵜⵏ</string>
    <string name="media__tab__emoticons" comment="Tab description for emoticons in the media UI">ⵜⵉⵙⵢⴰⴼⴽⵓⵏⵉⵏ</string>
    <string name="media__tab__kaomoji" comment="Tab description for kaomoji in the media UI">ⴽⴰⵡⵎⵓⵊⵉ</string>
    <!-- Emoji strings -->
    <string name="emoji__category__food_drink" comment="Emoji category name">ⵓⵜⵛⵉ &amp; ⵜⵉⵙⵙⵉ</string>
    <string name="emoji__category__travel_places" comment="Emoji category name">ⴰⵎⵓⴷⴷⵓ &amp; ⵉⴷⵖⴰⵔⵏ</string>
    <string name="emoji__category__objects" comment="Emoji category name">ⵜⵉⵖⴰⵡⵙⵉⵡⵉⵏ</string>
    <string name="emoji__category__flags" comment="Emoji category name">ⵉⵛⵏⵢⴰⵍⵏ</string>
    <!-- Quick action strings -->
    <!-- Incognito mode strings -->
    <!-- Settings UI strings -->
    <string name="settings__title" comment="Title of Settings">ⵜⵉⵙⵖⴰⵍ</string>
    <string name="settings__home__title" comment="Title of the Home screen">ⴱⵔⵔⴽ ⴳ {app_name}</string>
    <string name="settings__keyboard__title" comment="Title of Keyboard preferences screen">ⴰⵏⴰⵙⵉⵡ</string>
    <!-- Smartbar strings -->
    <string name="pref__smartbar__enabled__summary" comment="Preference summary">ⵔⴰⴷ ⵓⵎⴰⵏⵏ ⴳ ⵓⴼⵍⵍⴰ ⵏ ⵓⵏⴰⵙⵉⵡ</string>
    <!-- Typing strings -->
    <string name="pref__suggestion__title" comment="Preference group title">ⵉⵙⵓⵎⵔⵏ</string>
    <!-- About UI strings -->
    <string name="about__title" comment="Title of About activity">ⵅⴼ</string>
    <!-- Setup UI strings -->
    <string name="setup__title" comment="Title of Setup">ⴱⵔⵔⴽ!</string>
    <!-- Back up & Restore -->
    <!-- Crash Dialog strings -->
    <string name="crash_dialog__close" comment="Label of Close button in crash dialog">ⵔⴳⵍ</string>
    <!-- Clipboard strings -->
    <string name="clip__delete_item">ⴽⴽⵙ</string>
    <!-- Devtools strings -->
    <!-- Extension strings -->
    <!-- Action strings -->
    <!-- Error strings (generic) -->
    <!-- General strings -->
    <!-- Screen orientation strings -->
    <!-- State strings -->
    <!-- Enum label and description strings -->
    <!-- Unit strings (symbols) -->
    <!-- Unit strings (written words) -->
</resources>
