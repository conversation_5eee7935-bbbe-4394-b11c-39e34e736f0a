{"$": "ime.extension.theme", "meta": {"id": "org.florisboard.themes", "version": "0.2.0", "title": "FlorisBoard default themes", "description": "Default themes (both day and night) for the keyboard UI", "maintainers": ["patrickgold <<EMAIL>>", "lm41 <<EMAIL>>"], "license": "apache-2.0"}, "themes": [{"id": "floris_day", "label": "Floris Day", "authors": ["pat<PERSON><PERSON>"], "isNight": false}, {"id": "floris_day_borderless", "label": "<PERSON><PERSON><PERSON> (Borderless)", "authors": ["pat<PERSON><PERSON>"], "isNight": false}, {"id": "floris_night", "label": "Floris Night", "authors": ["pat<PERSON><PERSON>"], "isNight": true}, {"id": "floris_night_borderless", "label": "<PERSON><PERSON><PERSON> (Borderless)", "authors": ["pat<PERSON><PERSON>"], "isNight": true}, {"id": "floris_pure_night", "label": "Floris Pure Night", "authors": ["serebit"], "isNight": true}, {"id": "floris_pure_night_borderless", "label": "<PERSON><PERSON><PERSON> (Borderless)", "authors": ["serebit"], "isNight": true}]}