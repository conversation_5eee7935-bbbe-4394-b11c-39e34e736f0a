<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
              xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:fitsSystemWindows="true"
    android:theme="@style/CrashDialogTheme">

    <Toolbar
        android:id="@+id/crash_dialog_toolbar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/colorPrimaryDark"
        android:elevation="4dp"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1">
        <LinearLayout android:layout_width="match_parent" android:layout_height="wrap_content"
                      android:orientation="vertical">
            <TextView
                android:id="@+id/description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:text="@string/crash_dialog__description"/>

            <TextView
                android:id="@+id/report_instructions"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp"
                android:text="@string/crash_dialog__report_instructions"/>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@android:color/darker_gray"
                android:layout_marginTop="8dp"
                android:layout_marginBottom="8dp"/>

            <TextView
                android:id="@+id/stacktrace"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:padding="8dp"/>
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:id="@+id/footer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="2dp">

        <Button
            android:id="@+id/copy_to_clipboard"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:padding="8dp"
            android:text="@string/crash_dialog__copy_to_clipboard" tools:ignore="ButtonStyle"/>

        <Button
            android:id="@+id/open_bug_report_form"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:padding="8dp"
            android:text="@string/crash_dialog__open_issue_tracker" tools:ignore="ButtonStyle"/>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/close_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="2dp">

        <Button
            android:id="@+id/close"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:padding="8dp"
            android:text="@string/crash_dialog__close"/>
    </LinearLayout>


</LinearLayout>
