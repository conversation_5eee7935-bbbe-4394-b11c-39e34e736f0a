/*
 * Copyright (C) 2021-2025 The FlorisBoard Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package dev.patrickgold.florisboard.app.apptheme

import androidx.compose.ui.graphics.Color

/* Legacy Colors
val Green500 = Color(0xFF4CAF50)
val Green700 = Color(0xFF388E3C)
val Orange700 = Color(0xFFF57C00)
val Orange900 = Color(0xFFE65100)
*/

//Colors created with the material theme builder

val primaryLight = Color(0xFF006E1C)
val onPrimaryLight = Color(0xFFFFFFFF)
val primaryContainerLight = Color(0xFF58BC5B)
val onPrimaryContainerLight = Color(0xFF002204)
val secondaryLight = Color(0xFF005E16)
val onSecondaryLight = Color(0xFFFFFFFF)
val secondaryContainerLight = Color(0xFF2E8534)
val onSecondaryContainerLight = Color(0xFFFFFFFF)
val tertiaryLight = Color(0xFF964900)
val onTertiaryLight = Color(0xFFFFFFFF)
val tertiaryContainerLight = Color(0xFFFF8926)
val onTertiaryContainerLight = Color(0xFF341500)
val errorLight = Color(0xFFBA1A1A)
val onErrorLight = Color(0xFFFFFFFF)
val errorContainerLight = Color(0xFFFFDAD6)
val onErrorContainerLight = Color(0xFF410002)
val backgroundLight = Color(0xFFF5FBEF)
val onBackgroundLight = Color(0xFF171D16)
val surfaceLight = Color(0xFFF5FBEF)
val onSurfaceLight = Color(0xFF171D16)
val surfaceVariantLight = Color(0xFFDAE6D4)
val onSurfaceVariantLight = Color(0xFF3F4A3C)
val outlineLight = Color(0xFF6F7A6B)
val outlineVariantLight = Color(0xFFBECAB9)
val scrimLight = Color(0xFF000000)
val inverseSurfaceLight = Color(0xFF2C322A)
val inverseOnSurfaceLight = Color(0xFFEDF3E7)
val inversePrimaryLight = Color(0xFF78DC77)
val surfaceDimLight = Color(0xFFD6DCD0)
val surfaceBrightLight = Color(0xFFF5FBEF)
val surfaceContainerLowestLight = Color(0xFFFFFFFF)
val surfaceContainerLowLight = Color(0xFFF0F6EA)
val surfaceContainerLight = Color(0xFFEAF0E4)
val surfaceContainerHighLight = Color(0xFFE4EADE)
val surfaceContainerHighestLight = Color(0xFFDEE4D9)

val primaryDark = Color(0xFF78DC77)
val onPrimaryDark = Color(0xFF00390A)
val primaryContainerDark = Color(0xFF43A648)
val onPrimaryContainerDark = Color(0xFF000000)
val secondaryDark = Color(0xFF82DB7E)
val onSecondaryDark = Color(0xFF00390A)
val secondaryContainerDark = Color(0xFF2D8433)
val onSecondaryContainerDark = Color(0xFFFFFFFF)
val tertiaryDark = Color(0xFFFFB786)
val onTertiaryDark = Color(0xFF502400)
val tertiaryContainerDark = Color(0xFFEA7600)
val onTertiaryContainerDark = Color(0xFF030100)
val errorDark = Color(0xFFFFB4AB)
val onErrorDark = Color(0xFF690005)
val errorContainerDark = Color(0xFF93000A)
val onErrorContainerDark = Color(0xFFFFDAD6)
val backgroundDark = Color(0xFF0F120E)
val onBackgroundDark = Color(0xFFDEE4D9)
val surfaceDark = Color(0xFF0F120E)
val onSurfaceDark = Color(0xFFDEE4D9)
val surfaceVariantDark = Color(0xFF3F4A3C)
val onSurfaceVariantDark = Color(0xFFBECAB9)
val outlineDark = Color(0xFF899484)
val outlineVariantDark = Color(0xFF3F4A3C)
val scrimDark = Color(0xFF000000)
val inverseSurfaceDark = Color(0xFFDEE4D9)
val inverseOnSurfaceDark = Color(0xFF2C322A)
val inversePrimaryDark = Color(0xFF006E1C)
val surfaceDimDark = Color(0xFF0F150E)
val surfaceBrightDark = Color(0xFF353B33)
val surfaceContainerLowestDark = Color(0xFF0A1009)
val surfaceContainerLowDark = Color(0xFF171D16)
val surfaceContainerDark = Color(0xFF1B211A)
val surfaceContainerHighDark = Color(0xFF262C24)
val surfaceContainerHighestDark = Color(0xFF30362E)

val amoledDark = Color(0xFF000000)
