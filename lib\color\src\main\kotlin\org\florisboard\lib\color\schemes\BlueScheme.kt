/*
 * Copyright (C) 2025 The FlorisBoard Contributors
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.florisboard.lib.color.schemes

import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.ui.graphics.Color

private val primaryLight = Color(0xFF36618E)
private val onPrimaryLight = Color(0xFFFFFFFF)
private val primaryContainerLight = Color(0xFFD1E4FF)
private val onPrimaryContainerLight = Color(0xFF194975)
private val secondaryLight = Color(0xFF535F70)
private val onSecondaryLight = Color(0xFFFFFFFF)
private val secondaryContainerLight = Color(0xFFD7E3F7)
private val onSecondaryContainerLight = Color(0xFF3B4858)
private val tertiaryLight = Color(0xFF6B5778)
private val onTertiaryLight = Color(0xFFFFFFFF)
private val tertiaryContainerLight = Color(0xFFF2DAFF)
private val onTertiaryContainerLight = Color(0xFF523F5F)
private val errorLight = Color(0xFFBA1A1A)
private val onErrorLight = Color(0xFFFFFFFF)
private val errorContainerLight = Color(0xFFFFDAD6)
private val onErrorContainerLight = Color(0xFF93000A)
private val backgroundLight = Color(0xFFF8F9FF)
private val onBackgroundLight = Color(0xFF191C20)
private val surfaceLight = Color(0xFFF8F9FF)
private val onSurfaceLight = Color(0xFF191C20)
private val surfaceVariantLight = Color(0xFFDFE2EB)
private val onSurfaceVariantLight = Color(0xFF43474E)
private val outlineLight = Color(0xFF73777F)
private val outlineVariantLight = Color(0xFFC3C7CF)
private val scrimLight = Color(0xFF000000)
private val inverseSurfaceLight = Color(0xFF2E3135)
private val inverseOnSurfaceLight = Color(0xFFEFF0F7)
private val inversePrimaryLight = Color(0xFFA0CAFD)
private val surfaceDimLight = Color(0xFFD8DAE0)
private val surfaceBrightLight = Color(0xFFF8F9FF)
private val surfaceContainerLowestLight = Color(0xFFFFFFFF)
private val surfaceContainerLowLight = Color(0xFFF2F3FA)
private val surfaceContainerLight = Color(0xFFECEEF4)
private val surfaceContainerHighLight = Color(0xFFE6E8EE)
private val surfaceContainerHighestLight = Color(0xFFE1E2E8)

private val primaryDark = Color(0xFFA0CAFD)
private val onPrimaryDark = Color(0xFF003258)
private val primaryContainerDark = Color(0xFF194975)
private val onPrimaryContainerDark = Color(0xFFD1E4FF)
private val secondaryDark = Color(0xFFBBC7DB)
private val onSecondaryDark = Color(0xFF253140)
private val secondaryContainerDark = Color(0xFF3B4858)
private val onSecondaryContainerDark = Color(0xFFD7E3F7)
private val tertiaryDark = Color(0xFFD6BEE4)
private val onTertiaryDark = Color(0xFF3B2948)
private val tertiaryContainerDark = Color(0xFF523F5F)
private val onTertiaryContainerDark = Color(0xFFF2DAFF)
private val errorDark = Color(0xFFFFB4AB)
private val onErrorDark = Color(0xFF690005)
private val errorContainerDark = Color(0xFF93000A)
private val onErrorContainerDark = Color(0xFFFFDAD6)
private val backgroundDark = Color(0xFF111418)
private val onBackgroundDark = Color(0xFFE1E2E8)
private val surfaceDark = Color(0xFF111418)
private val onSurfaceDark = Color(0xFFE1E2E8)
private val surfaceVariantDark = Color(0xFF43474E)
private val onSurfaceVariantDark = Color(0xFFC3C7CF)
private val outlineDark = Color(0xFF8D9199)
private val outlineVariantDark = Color(0xFF43474E)
private val scrimDark = Color(0xFF000000)
private val inverseSurfaceDark = Color(0xFFE1E2E8)
private val inverseOnSurfaceDark = Color(0xFF2E3135)
private val inversePrimaryDark = Color(0xFF36618E)
private val surfaceDimDark = Color(0xFF111418)
private val surfaceBrightDark = Color(0xFF36393E)
private val surfaceContainerLowestDark = Color(0xFF0B0E13)
private val surfaceContainerLowDark = Color(0xFF191C20)
private val surfaceContainerDark = Color(0xFF1D2024)
private val surfaceContainerHighDark = Color(0xFF272A2F)
private val surfaceContainerHighestDark = Color(0xFF32353A)

val blueLightScheme = lightColorScheme(
    primary = primaryLight,
    onPrimary = onPrimaryLight,
    primaryContainer = primaryContainerLight,
    onPrimaryContainer = onPrimaryContainerLight,
    secondary = secondaryLight,
    onSecondary = onSecondaryLight,
    secondaryContainer = secondaryContainerLight,
    onSecondaryContainer = onSecondaryContainerLight,
    tertiary = tertiaryLight,
    onTertiary = onTertiaryLight,
    tertiaryContainer = tertiaryContainerLight,
    onTertiaryContainer = onTertiaryContainerLight,
    error = errorLight,
    onError = onErrorLight,
    errorContainer = errorContainerLight,
    onErrorContainer = onErrorContainerLight,
    background = backgroundLight,
    onBackground = onBackgroundLight,
    surface = surfaceLight,
    onSurface = onSurfaceLight,
    surfaceVariant = surfaceVariantLight,
    onSurfaceVariant = onSurfaceVariantLight,
    outline = outlineLight,
    outlineVariant = outlineVariantLight,
    scrim = scrimLight,
    inverseSurface = inverseSurfaceLight,
    inverseOnSurface = inverseOnSurfaceLight,
    inversePrimary = inversePrimaryLight,
    surfaceDim = surfaceDimLight,
    surfaceBright = surfaceBrightLight,
    surfaceContainerLowest = surfaceContainerLowestLight,
    surfaceContainerLow = surfaceContainerLowLight,
    surfaceContainer = surfaceContainerLight,
    surfaceContainerHigh = surfaceContainerHighLight,
    surfaceContainerHighest = surfaceContainerHighestLight,
)

val blueDarkScheme = darkColorScheme(
    primary = primaryDark,
    onPrimary = onPrimaryDark,
    primaryContainer = primaryContainerDark,
    onPrimaryContainer = onPrimaryContainerDark,
    secondary = secondaryDark,
    onSecondary = onSecondaryDark,
    secondaryContainer = secondaryContainerDark,
    onSecondaryContainer = onSecondaryContainerDark,
    tertiary = tertiaryDark,
    onTertiary = onTertiaryDark,
    tertiaryContainer = tertiaryContainerDark,
    onTertiaryContainer = onTertiaryContainerDark,
    error = errorDark,
    onError = onErrorDark,
    errorContainer = errorContainerDark,
    onErrorContainer = onErrorContainerDark,
    background = backgroundDark,
    onBackground = onBackgroundDark,
    surface = surfaceDark,
    onSurface = onSurfaceDark,
    surfaceVariant = surfaceVariantDark,
    onSurfaceVariant = onSurfaceVariantDark,
    outline = outlineDark,
    outlineVariant = outlineVariantDark,
    scrim = scrimDark,
    inverseSurface = inverseSurfaceDark,
    inverseOnSurface = inverseOnSurfaceDark,
    inversePrimary = inversePrimaryDark,
    surfaceDim = surfaceDimDark,
    surfaceBright = surfaceBrightDark,
    surfaceContainerLowest = surfaceContainerLowestDark,
    surfaceContainerLow = surfaceContainerLowDark,
    surfaceContainer = surfaceContainerDark,
    surfaceContainerHigh = surfaceContainerHighDark,
    surfaceContainerHighest = surfaceContainerHighestDark,
)
