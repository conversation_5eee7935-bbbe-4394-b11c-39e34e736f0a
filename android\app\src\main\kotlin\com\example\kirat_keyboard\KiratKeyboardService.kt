package com.example.kirat_keyboard

import android.inputmethodservice.InputMethodService
import android.view.View
import io.flutter.embedding.android.FlutterView
import io.flutter.embedding.engine.FlutterEngine
import io.flutter.embedding.engine.FlutterEngineCache
import io.flutter.embedding.engine.dart.DartExecutor
import io.flutter.plugin.common.MethodChannel

class KiratKeyboardService : InputMethodService() {
    private var flutterEngine: FlutterEngine? = null
    private var flutterView: FlutterView? = null
    private var methodChannel: MethodChannel? = null

    companion object {
        private const val ENGINE_ID = "kirat_keyboard_engine"
        private const val CHANNEL_NAME = "kirat_keyboard/ime"
    }

    override fun onCreate() {
        super.onCreate()
        initializeFlutterEngine()
    }

    private fun initializeFlutterEngine() {
        // Create or get cached Flutter engine
        flutterEngine = FlutterEngineCache.getInstance().get(ENGINE_ID)
        
        if (flutterEngine == null) {
            flutterEngine = FlutterEngine(this)
            
            // Start executing Dart code
            flutterEngine!!.dartExecutor.executeDartEntrypoint(
                DartExecutor.DartEntrypoint.createDefault()
            )
            
            // Cache the engine for reuse
            FlutterEngineCache.getInstance().put(ENGINE_ID, flutterEngine!!)
        }

        // Set up method channel for communication with Flutter
        methodChannel = MethodChannel(
            flutterEngine!!.dartExecutor.binaryMessenger,
            CHANNEL_NAME
        )
        
        setupMethodChannel()
    }

    private fun setupMethodChannel() {
        methodChannel?.setMethodCallHandler { call, result ->
            when (call.method) {
                "commitText" -> {
                    val text = call.argument<String>("text")
                    if (text != null) {
                        currentInputConnection?.commitText(text, 1)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Text cannot be null", null)
                    }
                }
                "deleteSurroundingText" -> {
                    val beforeLength = call.argument<Int>("beforeLength") ?: 1
                    val afterLength = call.argument<Int>("afterLength") ?: 0
                    currentInputConnection?.deleteSurroundingText(beforeLength, afterLength)
                    result.success(null)
                }
                "sendKeyEvent" -> {
                    val keyCode = call.argument<Int>("keyCode")
                    if (keyCode != null) {
                        // Handle key events
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Key code cannot be null", null)
                    }
                }
                "getInputConnection" -> {
                    val connection = currentInputConnection
                    if (connection != null) {
                        result.success(mapOf("available" to true))
                    } else {
                        result.success(mapOf("available" to false))
                    }
                }
                "setComposingText" -> {
                    val text = call.argument<String>("text")
                    val newCursorPosition = call.argument<Int>("newCursorPosition") ?: 1
                    if (text != null) {
                        currentInputConnection?.setComposingText(text, newCursorPosition)
                        result.success(null)
                    } else {
                        result.error("INVALID_ARGUMENT", "Text cannot be null", null)
                    }
                }
                "finishComposingText" -> {
                    currentInputConnection?.finishComposingText()
                    result.success(null)
                }
                "clearComposingText" -> {
                    currentInputConnection?.setComposingText("", 1)
                    result.success(null)
                }
                "getTextBeforeCursor" -> {
                    val length = call.argument<Int>("length") ?: 100
                    val text = currentInputConnection?.getTextBeforeCursor(length, 0)
                    result.success(text?.toString())
                }
                "getTextAfterCursor" -> {
                    val length = call.argument<Int>("length") ?: 100
                    val text = currentInputConnection?.getTextAfterCursor(length, 0)
                    result.success(text?.toString())
                }
                "moveCursor" -> {
                    val offset = call.argument<Int>("offset") ?: 0
                    // Move cursor by sending selection change
                    result.success(null)
                }
                "selectText" -> {
                    val start = call.argument<Int>("start") ?: 0
                    val end = call.argument<Int>("end") ?: 0
                    currentInputConnection?.setSelection(start, end)
                    result.success(null)
                }
                "hideKeyboard" -> {
                    requestHideSelf(0)
                    result.success(null)
                }
                "openSettings" -> {
                    // Open keyboard settings
                    result.success(null)
                }
                else -> {
                    result.notImplemented()
                }
            }
        }
    }

    override fun onCreateInputView(): View? {
        if (flutterView == null) {
            flutterView = FlutterView(this)
            flutterEngine?.let { engine ->
                flutterView!!.attachToFlutterEngine(engine)
            }
        }
        return flutterView
    }

    override fun onDestroy() {
        flutterView?.detachFromFlutterEngine()
        flutterView = null
        super.onDestroy()
    }

    override fun onStartInputView(info: android.view.inputmethod.EditorInfo?, restarting: Boolean) {
        super.onStartInputView(info, restarting)
        // Notify Flutter about input start
        methodChannel?.invokeMethod("onStartInputView", mapOf(
            "inputType" to info?.inputType,
            "restarting" to restarting
        ))
    }

    override fun onFinishInputView(finishingInput: Boolean) {
        super.onFinishInputView(finishingInput)
        // Notify Flutter about input finish
        methodChannel?.invokeMethod("onFinishInputView", mapOf(
            "finishingInput" to finishingInput
        ))
    }
}
