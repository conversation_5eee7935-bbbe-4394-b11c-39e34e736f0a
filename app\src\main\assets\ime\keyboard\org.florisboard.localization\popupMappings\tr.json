{"all": {"a": {"main": {"$": "auto_text_key", "code": 226, "label": "â"}}, "c": {"main": {"$": "auto_text_key", "code": 231, "label": "ç"}}, "g": {"main": {"$": "auto_text_key", "code": 287, "label": "ğ"}}, "i": {"main": {"$": "case_selector", "lower": {"code": 305, "label": "ı"}, "upper": {"code": 73, "label": "I"}}, "relevant": [{"$": "auto_text_key", "code": 238, "label": "î"}]}, "ı": {"main": {"$": "case_selector", "lower": {"code": 105, "label": "i"}, "upper": {"code": 304, "label": "İ"}}, "relevant": [{"$": "auto_text_key", "code": 238, "label": "î"}]}, "o": {"main": {"$": "auto_text_key", "code": 246, "label": "ö"}}, "s": {"main": {"$": "auto_text_key", "code": 351, "label": "ş"}}, "u": {"main": {"$": "auto_text_key", "code": 252, "label": "ü"}, "relevant": [{"$": "auto_text_key", "code": 251, "label": "û"}]}, "~right": {"main": {"code": 44, "label": ","}, "relevant": [{"code": 38, "label": "&"}, {"code": 37, "label": "%"}, {"code": 43, "label": "+"}, {"code": 34, "label": "\""}, {"code": 45, "label": "-"}, {"code": 58, "label": ":"}, {"code": 39, "label": "'"}, {"code": 64, "label": "@"}, {"code": 59, "label": ";"}, {"code": 47, "label": "/"}, {"$": "layout_direction_selector", "ltr": {"code": 40, "label": "("}, "rtl": {"code": 41, "label": "("}}, {"$": "layout_direction_selector", "ltr": {"code": 41, "label": ")"}, "rtl": {"code": 40, "label": ")"}}, {"code": 35, "label": "#"}, {"code": 33, "label": "!"}, {"code": 63, "label": "?"}]}}, "uri": {"~right": {"main": {"code": -255, "label": ".com"}, "relevant": [{"code": -255, "label": ".gov.tr"}, {"code": -255, "label": ".org"}, {"code": -255, "label": ".edu.tr"}, {"code": -255, "label": ".com.tr"}, {"code": -255, "label": ".net"}]}}}