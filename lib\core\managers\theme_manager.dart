import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

import '../../data/models/theme_data.dart';
import '../constants/app_constants.dart';
import '../preferences/preferences_manager.dart';

class ThemeManager extends Cubit<ThemeState> {
  ThemeManager() : super(const ThemeState.initial());

  final Map<String, KiratTheme> _loadedThemes = {};

  Future<void> initialize() async {
    await _loadBuiltInThemes();
    await _loadUserThemes();
    _setInitialTheme();
  }

  Future<void> _loadBuiltInThemes() async {
    // Load built-in themes
    final dayTheme = PredefinedThemes.kiratDay;
    final nightTheme = PredefinedThemes.kiratNight;
    
    _loadedThemes[dayTheme.id] = dayTheme;
    _loadedThemes[nightTheme.id] = nightTheme;
  }

  Future<void> _loadUserThemes() async {
    // Load user-installed themes from extensions
    // This would be implemented when extension system is ready
  }

  void _setInitialTheme() {
    final prefs = PreferencesManager.instance.theme;
    final themeMode = prefs.mode;
    
    KiratTheme currentTheme;
    
    switch (themeMode) {
      case ThemeModeEnum.ALWAYS_DAY:
        currentTheme = _loadedThemes[prefs.dayThemeId] ?? PredefinedThemes.kiratDay;
        break;
      case ThemeModeEnum.ALWAYS_NIGHT:
        currentTheme = _loadedThemes[prefs.nightThemeId] ?? PredefinedThemes.kiratNight;
        break;
      case ThemeModeEnum.FOLLOW_SYSTEM:
        // This would check system theme
        currentTheme = _loadedThemes[prefs.dayThemeId] ?? PredefinedThemes.kiratDay;
        break;
      case ThemeModeEnum.FOLLOW_TIME:
        // This would check current time
        final hour = DateTime.now().hour;
        final isNight = hour < 6 || hour > 18;
        currentTheme = isNight 
            ? _loadedThemes[prefs.nightThemeId] ?? PredefinedThemes.kiratNight
            : _loadedThemes[prefs.dayThemeId] ?? PredefinedThemes.kiratDay;
        break;
    }
    
    emit(state.copyWith(
      currentTheme: currentTheme,
      availableThemes: _loadedThemes.values.toList(),
    ));
  }

  void switchTheme(String themeId) {
    final theme = _loadedThemes[themeId];
    if (theme != null) {
      emit(state.copyWith(currentTheme: theme));
      
      // Update preferences
      final prefs = PreferencesManager.instance.theme;
      if (theme.isNightTheme) {
        prefs.nightThemeId = themeId;
      } else {
        prefs.dayThemeId = themeId;
      }
    }
  }

  void setThemeMode(ThemeModeEnum mode) {
    PreferencesManager.instance.theme.mode = mode;
    _setInitialTheme(); // Reapply theme based on new mode
  }

  KiratStyle queryStyle(String elementName, Map<String, String> attributes) {
    return state.currentTheme.query(elementName, attributes);
  }

  void addTheme(KiratTheme theme) {
    _loadedThemes[theme.id] = theme;
    emit(state.copyWith(
      availableThemes: _loadedThemes.values.toList(),
    ));
  }

  void removeTheme(String themeId) {
    // Don't allow removing built-in themes
    if (themeId == 'kirat_day' || themeId == 'kirat_night') {
      return;
    }
    
    _loadedThemes.remove(themeId);
    emit(state.copyWith(
      availableThemes: _loadedThemes.values.toList(),
    ));
    
    // If current theme was removed, switch to default
    if (state.currentTheme.id == themeId) {
      switchTheme('kirat_day');
    }
  }

  List<KiratTheme> getThemesByType(bool isNightTheme) {
    return _loadedThemes.values
        .where((theme) => theme.isNightTheme == isNightTheme)
        .toList();
  }

  KiratTheme? getThemeById(String id) {
    return _loadedThemes[id];
  }

  void updateSystemTheme(Brightness brightness) {
    final prefs = PreferencesManager.instance.theme;
    
    if (prefs.mode == ThemeModeEnum.FOLLOW_SYSTEM) {
      final themeId = brightness == Brightness.dark 
          ? prefs.nightThemeId 
          : prefs.dayThemeId;
      
      final theme = _loadedThemes[themeId];
      if (theme != null && theme.id != state.currentTheme.id) {
        emit(state.copyWith(currentTheme: theme));
      }
    }
  }

  void refreshThemes() {
    _setInitialTheme();
  }
}

class ThemeState extends Equatable {
  const ThemeState({
    required this.currentTheme,
    required this.availableThemes,
    required this.isLoading,
    this.error,
  });

  const ThemeState.initial()
      : currentTheme = const KiratTheme(
          id: 'kirat_day',
          name: 'Kirat Day',
          isNightTheme: false,
          styles: {},
          colorScheme: KiratColorScheme(
            primary: Color(0xFF4CAF50),
            onPrimary: Color(0xFFFFFFFF),
            secondary: Color(0xFF2196F3),
            onSecondary: Color(0xFFFFFFFF),
            background: Color(0xFFF5F5F5),
            onBackground: Color(0xFF212121),
            surface: Color(0xFFFFFFFF),
            onSurface: Color(0xFF212121),
            error: Color(0xFFF44336),
            onError: Color(0xFFFFFFFF),
          ),
        ),
        availableThemes = const [],
        isLoading = false,
        error = null;

  final KiratTheme currentTheme;
  final List<KiratTheme> availableThemes;
  final bool isLoading;
  final String? error;

  ThemeState copyWith({
    KiratTheme? currentTheme,
    List<KiratTheme>? availableThemes,
    bool? isLoading,
    String? error,
  }) {
    return ThemeState(
      currentTheme: currentTheme ?? this.currentTheme,
      availableThemes: availableThemes ?? this.availableThemes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [currentTheme, availableThemes, isLoading, error];
}
