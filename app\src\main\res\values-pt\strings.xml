<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">FlorisBoard</string>
    <string name="key__phone_pause" comment="Label for the Pause key in the telephone keyboard layout">Pausa</string>
    <string name="key__phone_wait" comment="Label for the Wait key in the telephone keyboard layout">Aguarde</string>
    <string name="key_popup__threedots_alt" comment="Content description for the three-dots icon in a key popup">Ícone de 3 pontos. Se mostrado, indica que pode utilizar mais letras com um toque longo.</string>
    <!-- One-handed strings -->
    <string name="one_handed__close_btn_content_description" comment="Content description for the one-handed close button">Fechar modo de uma mão.</string>
    <string name="one_handed__move_start_btn_content_description" comment="Content description for the one-handed move to left button">Mover teclado para a esquerda.</string>
    <string name="one_handed__move_end_btn_content_description" comment="Content description for the one-handed move to right button">Mover teclado para a direita.</string>
    <!-- Media strings -->
    <string name="settings__media__title">Emojis</string>
    <string name="media__tab__emojis" comment="Tab description for emojis in the media UI">Emojis</string>
    <string name="media__tab__emoticons" comment="Tab description for emoticons in the media UI">Emoticons</string>
    <string name="media__tab__kaomoji" comment="Tab description for kaomoji in the media UI">Kaomoji</string>
    <string name="prefs__media__emoji_preferred_skin_tone">Tom de pele preferido para emoji</string>
    <string name="prefs__media__emoji_preferred_hair_style">Estilo de cabelo preferido para emoji</string>
    <!-- Emoji strings -->
    <string name="emoji__category__smileys_emotion" comment="Emoji category name">Sorrisos &amp; Emoticons</string>
    <string name="emoji__category__people_body" comment="Emoji category name">Pessoas &amp; Corpo</string>
    <string name="emoji__category__animals_nature" comment="Emoji category name">Animais &amp; Natureza</string>
    <string name="emoji__category__food_drink" comment="Emoji category name">Comida &amp; Bebida</string>
    <string name="emoji__category__travel_places" comment="Emoji category name">Viagens &amp; Locais</string>
    <string name="emoji__category__activities" comment="Emoji category name">Atividades</string>
    <string name="emoji__category__objects" comment="Emoji category name">Objetos</string>
    <string name="emoji__category__symbols" comment="Emoji category name">Símbolos</string>
    <string name="emoji__category__flags" comment="Emoji category name">Bandeiras</string>
    <!-- Quick action strings -->
    <string name="quick_action__arrow_up" maxLength="12">Seta p/ cima</string>
    <string name="quick_action__arrow_up__tooltip">Aciona a seta para cima</string>
    <!-- Incognito mode strings -->
    <!-- Settings UI strings -->
    <string name="settings__title" comment="Title of Settings">Definições</string>
    <string name="settings__preview_keyboard" comment="Hint for try your setup box">Testar configuração</string>
    <string name="settings__help" comment="General label for help buttons in Settings">Ajuda</string>
    <string name="settings__default" comment="General string which is used when a preference has the default value set">Padrão</string>
    <string name="settings__system_default" comment="General string which is used when a preference has the system default value set">Definição do sistema</string>
    <string name="settings__home__title" comment="Title of the Home screen">Bem-vindo a {app_name}</string>
    <string name="settings__home__ime_not_enabled" comment="Error message shown in Home fragment when FlorisBoard is not enabled in the system">FlorisBoard não está ativo no sistema, logo não pode ser utilizado como método de introdução no seletor. Toque aqui para resolver esta situação.</string>
    <string name="settings__home__ime_not_selected" comment="Warning message shown in Home fragment when FlorisBoard is not selected as the default keyboard">FlorisBoard não está selecionado como método de introdução padrão. Toque aqui para resolver esta situação.</string>
    <string name="settings__localization__title" comment="Title of languages and Layout screen">Idiomas e esquemas</string>
    <string name="settings__localization__display_language_names_in__label" comment="Label of Display language names in preference">Mostrar nome dos idiomas em</string>
    <string name="settings__localization__group_subtypes__label" comment="Label of subtypes group">Subtipos</string>
    <string name="settings__localization__subtype_add_title" comment="Title of subtype dialog when adding a new subtype">Adicionar subtipo</string>
    <string name="settings__localization__subtype_edit_title" comment="Title of subtype dialog when editing an existing subtype">Editar subtipo</string>
    <string name="settings__localization__subtype_locale" comment="Label for locale dropdown in subtype dialog">Idioma principal</string>
    <string name="settings__localization__subtype_popup_mapping" comment="Label for popup mapping dropdown in subtype screen">Mapeamento de popups</string>
    <string name="settings__localization__subtype_characters_layout" comment="Label for layout dropdown in subtype dialog">Disposição de caracteres</string>
    <string name="settings__localization__subtype_symbols_layout" comment="Label for layout dropdown in subtype dialog">Disposição principal de símbolos</string>
    <string name="settings__localization__subtype_symbols2_layout" comment="Label for layout dropdown in subtype dialog">Disposição secundária de símbolos</string>
    <string name="settings__localization__subtype_composer" comment="Label for composer dropdown in subtype dialog.">Compositor</string>
    <string name="settings__localization__subtype_currency_set" comment="Label for currency set dropdown in subtype dialog. 'set' is used as a noun here and can be compared to a group of elements (in this case currency symbols).">Conjunto de moedas</string>
    <string name="settings__localization__subtype_numeric_layout" comment="Label for layout dropdown in subtype dialog">Disposição dos números</string>
    <string name="settings__localization__subtype_numeric_advanced_layout" comment="Label for layout dropdown in subtype dialog">Disposição dos números (avançado)</string>
    <string name="settings__localization__subtype_numeric_row_layout" comment="Label for layout dropdown in subtype dialog">Disposição da linha de números</string>
    <string name="settings__localization__subtype_phone_layout" comment="Label for layout dropdown in subtype dialog">Disposição principal do telefone</string>
    <string name="settings__localization__subtype_phone2_layout" comment="Label for layout dropdown in subtype dialog">Disposição secundária do telefone</string>
    <string name="settings__localization__subtype_select_locale" comment="Subtype select language title">Selecione o idioma</string>
    <string name="settings__localization__subtype_search_locale_placeholder" comment="Subtype search language placeholder">Pesquisar por um idioma</string>
    <string name="settings__localization__subtype_search_locale_not_found" comment="Subtype search language not found">Não foi possível encontrar o idioma \"{search_term}\".</string>
    <string name="settings__localization__subtype_select_placeholder" comment="Subtype dialog select value placeholder (&amp;#8210; is a hyphen character)">&#8210; selecionar &#8210;</string>
    <string name="settings__localization__subtype_summary" comment="Subtype summary">{characters_name} / {symbols_name} / {currency_set_name}</string>
    <string name="settings__localization__suggested_subtype_presets" comment="Suggested presets title">Subtipos sugeridos</string>
    <string name="settings__localization__suggested_subtype_presets_none_found" comment="Suggested presets none found">Não existem subtipos sugeridos. Utilize o botão abaixo para ver as predefinições de subtipos.</string>
    <string name="settings__localization__subtype_presets" comment="Subtype presets dialog title">Predefinições de subtipos.</string>
    <string name="settings__localization__subtype_presets_view_all" comment="View all presets button">Mostrar tudo</string>
    <string name="settings__localization__subtype_no_subtypes_configured_warning" comment="Warning message that no subtype has been defined">Parece que ainda não configurou quaisquer subtipos. Como recurso será utilizado o subtipo Inglês/QWERTY!</string>
    <string name="settings__localization__subtype_error_already_exists" comment="Error message shown in subtype dialog when a subtype to add already exists">Este subtipo já existe!</string>
    <string name="settings__localization__subtype_error_fields_no_value" comment="Error message shown in subtype editor if at least one field is set to '- select -' (means no value specified)">Existe, pelo menos, um campo que não tem valor. Deve escolher um valor para o(s) campo(s).</string>
    <string name="settings__localization__subtype_error_layout_not_installed" comment="Error message shown in subtype list when a layout is not installed, where %s will be replaced by the layout ID">{layout_id} (não instalado)</string>
    <string name="settings__localization__group_layouts__label" comment="Label of layouts group">Esquemas</string>
    <string name="settings__theme__title" comment="Title of the Theme screen"> Tema</string>
    <string name="pref__theme__mode__label" comment="Label of the theme mode preference">Modo do tema</string>
    <string name="pref__theme__sunrise_time__label" comment="Label of the sunrise time preference">Hora do nascer do sol</string>
    <string name="pref__theme__sunset_time__label" comment="Label of the sunset time preference">Hora do pôr do sol</string>
    <string name="pref__theme__day" comment="Label of the day group (day means light theme)">Tema diurno</string>
    <string name="pref__theme__night" comment="Label of the night group (night means dark theme)">Tema noturno</string>
    <string name="settings__theme_manager__title_manage" comment="Title of the theme manager screen for managing installed and custom themes">Gerir temas instalados</string>
    <string name="pref__theme__source_assets" comment="Label for the theme source field">Dados da aplicação FlorisBoard</string>
    <string name="pref__theme__source_internal" comment="Label for the theme source field">Armazenamento interno</string>
    <string name="pref__theme__source_external" comment="Label for the theme source field">Fornecedor externo</string>
    <string name="settings__theme_manager__title_day" comment="Title of the theme manager screen for day theme selection">Selecionar tema diurno</string>
    <string name="settings__theme_manager__title_night" comment="Title of the theme manager screen for night theme selection">Selecionar tema noturno</string>
    <string name="settings__theme_editor__fine_tune__title">Editor preciso</string>
    <string name="settings__theme_editor__fine_tune__level">Nível de edição</string>
    <string name="settings__theme_editor__fine_tune__display_kbd_after_dialogs">Mostrar teclado após os diálogos</string>
    <string name="settings__theme_editor__add_rule">Adicionar regra</string>
    <string name="settings__theme_editor__edit_rule">Editar regra</string>
    <string name="settings__theme_editor__no_rules_defined">Esta folha de estilo não tem regras definidas. Adicione uma regra para começar a editar.</string>
    <string name="settings__theme_editor__rule_already_exists">Esta regra de folha de estilos já está definida.</string>
    <string name="settings__theme_editor__rule_codes">Códigos-chave alvo</string>
    <string name="settings__theme_editor__rule_groups">Grupos</string>
    <string name="settings__theme_editor__rule_selectors">Seletores</string>
    <string name="settings__theme_editor__add_code">Adicionar código-chave</string>
    <string name="settings__theme_editor__edit_code">Editar código-chave</string>
    <string name="settings__theme_editor__no_codes_defined">Aplicar regra a todos os elementos alvo.</string>
    <string name="settings__theme_editor__code_already_exists">Este código já está definido.</string>
    <string name="settings__theme_editor__code_invalid">Este código-chave não é válido. Assegure-se que o código-chave está entre o alcance de {c_min} a {c_max} para carateres ou {i_min} a {i_max} para teclas internas especiais.</string>
    <string name="settings__theme_editor__code_placeholder">Código</string>
    <string name="settings__theme_editor__code_recording_placeholder">A gravar…</string>
    <string name="settings__theme_editor__add_property">Adicionar propriedade</string>
    <string name="settings__theme_editor__edit_property">Editar propriedade</string>
    <string name="settings__theme_editor__property_already_exists">Já existe uma propriedade com este nome dentro da regra atual.</string>
    <string name="settings__theme_editor__property_name">Nome da propriedade</string>
    <string name="settings__theme_editor__property_value">Valor da propriedade</string>
    <string name="settings__theme_editor__property_value_shape_apply_for_all_corners">Aplicar a todos os cantos</string>
    <string name="settings__theme_editor__property_value_color_dialog_title">Editar cadeia de cor</string>
    <string name="settings__theme_editor__component_meta_is_night_theme">É tema noturno</string>
    <string name="settings__theme_editor__component_meta_is_borderless">Não tem contornos</string>
    <string name="settings__theme_editor__component_meta_stylesheet_path">Caminho da folha de estilo</string>
    <string name="snygg__rule_element__key">Tecla</string>
    <string name="snygg__rule_element__key_hint">Dica de tecla</string>
    <string name="snygg__rule_element__clipboard_item">Item da área de transferência</string>
    <string name="snygg__rule_element__extracted_landscape_input_layout">Esquema de introdução horizontal</string>
    <string name="snygg__rule_element__extracted_landscape_input_field">Campo de introdução horizontal</string>
    <string name="snygg__rule_element__extracted_landscape_input_action">Ação de introdução horizontal</string>
    <string name="snygg__rule_element__glide_trail">Rasto de gestos</string>
    <string name="snygg__rule_element__one_handed_panel">Painel de uma mão</string>
    <string name="snygg__rule_selector__pressed">Premido</string>
    <string name="snygg__rule_selector__focus">Focado</string>
    <string name="snygg__rule_selector__disabled">Desativada</string>
    <string name="snygg__property_name__background">Fundo</string>
    <string name="snygg__property_name__foreground">Principal</string>
    <string name="snygg__property_name__border_color">Cor do contorno</string>
    <string name="snygg__property_name__border_style">Estilo do contorno</string>
    <string name="snygg__property_name__border_width">Largura do contorno</string>
    <string name="snygg__property_name__font_family">Família do tipo de letra</string>
    <string name="snygg__property_name__font_size">Tamanho do tipo de letra</string>
    <string name="snygg__property_name__font_style">Estilo do tipo de letra</string>
    <string name="snygg__property_name__shape">Forma</string>
    <string name="snygg__property_name__var_primary">Cor primária</string>
    <string name="snygg__property_name__var_primary_variant">Cor primária (variante)</string>
    <string name="snygg__property_name__var_secondary">Cor secundária</string>
    <string name="snygg__property_name__var_secondary_variant">Cor secundária (variante)</string>
    <string name="snygg__property_name__var_background">Plano de fundo comum</string>
    <string name="snygg__property_name__var_surface">Superfície comum</string>
    <string name="snygg__property_name__var_surface_variant">Superfície comum (variante)</string>
    <string name="snygg__property_name__var_shape">Froma comum</string>
    <string name="snygg__property_name__var_shape_variant">Forma comun (variante)</string>
    <string name="snygg__property_value__explicit_inherit">Herdar</string>
    <string name="snygg__property_value__solid_color">Cor sólida</string>
    <string name="snygg__property_value__rectangle_shape">Forma de retângulo</string>
    <string name="snygg__property_value__circle_shape">Forma de círculo</string>
    <string name="snygg__property_value__cut_corner_shape_dp">Forma de canto cortado (dp)</string>
    <string name="snygg__property_value__cut_corner_shape_percent">Forma de canto cortado (%)</string>
    <string name="snygg__property_value__rounded_corner_shape_dp">Forma de canto arredondado (dp)</string>
    <string name="snygg__property_value__rounded_corner_shape_percent">Forma de canto arredondado (%)</string>
    <string name="snygg__property_value__dp_size">Tamanho (dp)</string>
    <string name="snygg__property_value__sp_size">Tamanho (sp)</string>
    <string name="snygg__property_value__percentage_size">Tamanho (%)</string>
    <string name="settings__input_feedback__title" comment="Title of Input Feedback screen">Sons e vibração</string>
    <string name="pref__input_feedback__group_audio__label" comment="Preference group title">Sons e resposta táctil</string>
    <string name="pref__input_feedback__audio_enabled__label" comment="Preference title">Ativar respostas de áudio</string>
    <string name="pref__input_feedback__audio_volume__label" comment="Preference title">Volume para os eventos de introdução</string>
    <string name="pref__input_feedback__audio_feat_key_press__label" comment="Preference title">Som ao premir as teclas</string>
    <string name="pref__input_feedback__audio_feat_key_long_press__label" comment="Preference title">Som na pressão longa das teclas</string>
    <string name="pref__input_feedback__audio_feat_key_repeated_action__label" comment="Preference title">Som nas repetições de teclas</string>
    <string name="pref__input_feedback__audio_feat_gesture_swipe__label" comment="Preference title">Sons de deslize</string>
    <string name="pref__input_feedback__audio_feat_gesture_moving_swipe__label" comment="Preference title">Som para gestos de deslize</string>
    <string name="pref__input_feedback__group_haptic__label" comment="Preference group title">Resposta táctil/Vibração</string>
    <string name="pref__input_feedback__haptic_enabled__label" comment="Preference title">Ativar resposta táctil</string>
    <string name="pref__input_feedback__haptic_vibration_duration__label" comment="Preference title">Duração da vibração</string>
    <string name="pref__input_feedback__haptic_vibration_strength__label" comment="Preference title">Intensidade da vibração</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_amplitude_ctrl" comment="Preference summary">Esta funcionalidade necessita do suporte a controlo de amplitude do hardware, que não está disponível no seu dispositivo</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_unsupported_android_version" comment="Preference summary">Esta funcionalidade necessita do suporte a controlo de amplitude do hardware, apenas disponível em Android 8.0 ou mais recente.</string>
    <string name="pref__input_feedback__haptic_feat_key_press__label" comment="Preference title">Vibrar ao premir as teclas</string>
    <string name="pref__input_feedback__haptic_feat_key_long_press__label" comment="Preference title">Vibrar ao premir longamente as teclas</string>
    <string name="pref__input_feedback__haptic_feat_key_repeated_action__label" comment="Preference title">Vibrar na repetição das teclas</string>
    <string name="pref__input_feedback__haptic_feat_gesture_swipe__label" comment="Preference title">Vibrar ao deslizar</string>
    <string name="pref__input_feedback__haptic_feat_gesture_moving_swipe__label" comment="Preference title">Vibrar nos gestos de deslize</string>
    <string name="pref__input_feedback__any_feat_key_press__summary" comment="Preference summary">por exemplo: teclas, botões e emoji</string>
    <string name="pref__input_feedback__any_feat_key_long_press__summary" comment="Preference summary">por exemplo: menus popup</string>
    <string name="pref__input_feedback__any_feat_key_repeated_action__summary" comment="Preference summary">por exemplo: tecla delete</string>
    <string name="pref__input_feedback__any_feat_gesture_swipe__summary" comment="Preference summary">não implementado</string>
    <string name="pref__input_feedback__any_feat_gesture_moving_swipe__summary" comment="Preference summary">por exemplo: deslize do cursor</string>
    <string name="settings__keyboard__title" comment="Title of Keyboard preferences screen">Teclado</string>
    <string name="pref__keyboard__number_row__label" comment="Preference title">Linha de números</string>
    <string name="pref__keyboard__number_row__summary" comment="Preference summary">Mostrar linha de números na parte superior do teclado</string>
    <string name="pref__keyboard__hinted_number_row_mode__label" comment="Preference title">Indicador de números</string>
    <string name="pref__keyboard__hinted_symbols_mode__label" comment="Preference title">Indicador de símbolos</string>
    <string name="pref__keyboard__utility_key_enabled__label" comment="Preference title">Mostrar tecla utilitária</string>
    <string name="pref__keyboard__utility_key_enabled__summary" comment="Preference summary">Mostra uma tecla utilitária ao lado da barra de espaço</string>
    <string name="pref__keyboard__utility_key_action__label" comment="Preference title">Ação da tecla utilitária</string>
    <string name="pref__keyboard__font_size_multiplier__label" comment="Preference title">Multiplicador do tamanho das letras</string>
    <string name="pref__keyboard__group_layout__label" comment="Preference group title">Disposição</string>
    <string name="pref__keyboard__one_handed_mode__label" comment="Preference title">Modo de uma mão</string>
    <string name="pref__keyboard__one_handed_mode_scale_factor__label" comment="Preference title">Largura do teclado no modo de uma mão</string>
    <string name="pref__keyboard__landscape_input_ui_mode__label" comment="Preference value">Introdução em ecrã completo (horizontal)</string>
    <string name="pref__keyboard__height_factor__label" comment="Preference title">Altura do teclado</string>
    <string name="pref__keyboard__key_spacing__label" comment="Preference title">Espaço entre teclas</string>
    <string name="pref__keyboard__bottom_offset__label" comment="Preference title">Deslocação inferior</string>
    <string name="pref__keyboard__group_keypress__label" comment="Preference group title">Pressão de teclas</string>
    <string name="pref__keyboard__popup_enabled__label" comment="Preference title">Visibilidade do popup</string>
    <string name="pref__keyboard__popup_enabled__summary" comment="Preference summary">Mostrar popup ao tocar nas teclas</string>
    <string name="pref__keyboard__long_press_delay__label" comment="Preference title">Atraso para a pressão longa de teclas</string>
    <!-- Smartbar strings -->
    <string name="settings__smartbar__title" comment="Title of Smartbar screen">Barra inteligente</string>
    <string name="pref__smartbar__enabled__label" comment="Preference title">Ativar barra inteligente</string>
    <string name="pref__smartbar__enabled__summary" comment="Preference summary">Mostrar na parte superior do teclado</string>
    <!-- Typing strings -->
    <string name="pref__suggestion__title" comment="Preference group title">Sugestões</string>
    <string name="pref__suggestion__display_mode__label" comment="Preference title">Modo de exibição das sugestões</string>
    <string name="pref__correction__title" comment="Preference group title">Correções</string>
    <string name="pref__correction__auto_capitalization__label" comment="Preference title">Maiúsculas automáticas</string>
    <string name="pref__correction__auto_capitalization__summary" comment="Preference summary">Colocar letras maiúsculas tendo em conta o contexto das palavras</string>
    <string name="pref__correction__remember_caps_lock_state__label" comment="Preference title">Memorizar estado do CapsLock</string>
    <string name="pref__correction__remember_caps_lock_state__summary" comment="Preference summary">Manter CapsLock quando se muda para outro campo de texto</string>
    <string name="pref__correction__double_space_period__label" comment="Preference title">Duplo espaço insere ponto final</string>
    <string name="pref__correction__double_space_period__summary" comment="Preference summary">Ao tocar 2 vezes na barra de espaços, inserir um ponto final e um espaço</string>
    <string name="pref__spelling__active_spellchecker__summary_disabled">Desativado globalmente. Não serão mostradas riscas vermelhas se existirem palavras erradas. Toque para alterar.</string>
    <string name="pref__spelling__active_spellchecker__summary_none">Não definiu o serviço de verificação ortográfica. Toque para alterar.</string>
    <string name="pref__spelling__language_mode__label" comment="Label of Language mode pref">Idiomas</string>
    <string name="pref__spelling__use_udm_entries__label" comment="Label of Use user dictionary entries pref">Utilizar entradas do dicionário de utilizador</string>
    <string name="settings__dictionary__title" comment="Title of the User dictionaries screen">Dicionários do utilizador</string>
    <string name="pref__dictionary__enable_system_user_dictionary__label" comment="Preference title">Ativar dicionário do sistema</string>
    <string name="pref__dictionary__enable_system_user_dictionary__summary" comment="Preference summary">Sugerir palavras guardadas no dicionário do sistema</string>
    <string name="pref__dictionary__manage_system_user_dictionary__label" comment="Preference title">Gerir dicionários do sistema</string>
    <string name="pref__dictionary__manage_system_user_dictionary__summary" comment="Preference summary">Adicionar, ver e remover entradas ao dicionário do sistema</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__label" comment="Preference title">Ativar dicionário do utilizador</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__summary" comment="Preference summary">Sugerir palavras guardadas no dicionário do utilizador</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__label" comment="Preference title">Gerir dicionários do utilizador</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__summary" comment="Preference summary">Adicionar, ver e remover entradas ao dicionário do utilizador</string>
    <string name="settings__udm__title_floris" comment="Title of the User Dictionary Manager activity for internal">Dicionário do utilizador</string>
    <string name="settings__udm__title_system" comment="Title of the User Dictionary Manager activity for system">Dicionário do sistema</string>
    <string name="settings__udm__word_summary_freq" comment="Summary label for a word entry. The decimal placeholder inserts the frequency for the word it summarizes.">Frequência: {freq}</string>
    <string name="settings__udm__word_summary_freq_shortcut" comment="Summary label for a word entry. The first placeholder inserts the frequency for the word it summarizes, the second placeholder the shortcut defined.">Frequência: {freq} | Atalho: {shortcut}</string>
    <string name="settings__udm__all_languages" comment="Label of the For all languages entry in the language list">Para todos os idiomas</string>
    <string name="settings__udm__open_system_manager_ui" comment="Label of the Open system manager UI menu option">Abrir gestor do sistema</string>
    <string name="settings__udm__dictionary_import_success" comment="Message for dictionary import success">Dicionário do utilizador importado com sucesso!</string>
    <string name="settings__udm__dictionary_export_success" comment="Message for dictionary export success">Dicionário do utilizador exportado com sucesso!</string>
    <string name="settings__udm__dialog__title_add" comment="Label for the title (when in adding mode) in the user dictionary add/edit dialog">Adicionar palavra</string>
    <string name="settings__udm__dialog__title_edit" comment="Label for the title (when in editing mode) in the user dictionary add/edit dialog">Editar palavra</string>
    <string name="settings__udm__dialog__word_label" comment="Label for the word in the user dictionary add/edit dialog">Palavra</string>
    <string name="settings__udm__dialog__word_error_empty" comment="Error label for the word in the user dictionary add/edit dialog">Por favor, introduza uma palavra</string>
    <string name="settings__udm__dialog__word_error_invalid" comment="Error label for the word in the user dictionary add/edit dialog">Por favor, introduza uma palavra correspondente a {regex}</string>
    <string name="settings__udm__dialog__freq_label" comment="Label for the frequency in the user dictionary add/edit dialog. The two decimal placeholders are the minimum and maximum frequency, both inclusive.">Frequência (entre {f_min} e {f_max})</string>
    <string name="settings__udm__dialog__freq_error_empty" comment="Error label for the frequency in the user dictionary add/edit dialog">Por favor, introduza um valor de frequência</string>
    <string name="settings__udm__dialog__freq_error_invalid" comment="Error label for the frequency in the user dictionary add/edit dialog">Por favor, introduza um número válido entre os limites especificados</string>
    <string name="settings__udm__dialog__shortcut_label" comment="Label for the shortcut in the user dictionary add/edit dialog">Atalho (opcional)</string>
    <string name="settings__udm__dialog__shortcut_error_invalid" comment="Error label for the shortcut in the user dictionary add/edit dialog">Por favor, introduza um atalho correspondente a {regex}</string>
    <string name="settings__udm__dialog__locale_label" comment="Label for the language code in the user dictionary add/edit dialog">Código do idioma (opcional)</string>
    <string name="settings__udm__dialog__locale_error_invalid" comment="Error label for the language code in the user dictionary add/edit dialog">O código de idioma indicado não é coincidente com a sintaxe esperada. O código deve um idioma (pt), um idioma e um país (pt_PT) ou um idioma, um país e um script (pt_PT-script).</string>
    <string name="settings__gestures__title" comment="Title of Gestures screen">Gestos e deslize</string>
    <string name="pref__glide__title" comment="Preference group title">Digitar ao deslizar</string>
    <string name="pref__glide__enabled__label" comment="Preference title">Ativar escrita por egstos</string>
    <string name="pref__glide__enabled__summary" comment="Preference summary">Digitar uma palavra deslizando o dedo através das teclas</string>
    <string name="pref__glide__show_trail__label" comment="Preference title">Mostrar rasto dos gestos</string>
    <string name="pref__glide__show_trail__summary" comment="Preference summary">O rasto desaparece no final de cada palavra</string>
    <string name="pref__glide_trail_fade_duration">Tempo para mostrar o rasto</string>
    <string name="pref__glide_preview_refresh_delay">Atraso de pré-visualização</string>
    <string name="pref__glide__show_preview">Mostrar pré-visualização ao escrever com gestos</string>
    <string name="pref__glide__immediate_backspace_deletes_word__label">Eliminar palavra sempre</string>
    <string name="pref__glide__immediate_backspace_deletes_word__summary">Pressionar \"eliminar\" de seguida ao deslizar, apaga a palavra inteira</string>
    <string name="pref__gestures__general_title" comment="Preference group title">Gestos genéricos</string>
    <string name="pref__gestures__space_bar_title" comment="Preference group title">Gestos na barra de espaço</string>
    <string name="pref__gestures__other_title" comment="Preference group title">Outros gestos / Limitação de gestos</string>
    <string name="pref__gestures__swipe_up__label" comment="Preference title">Deslizar para cima</string>
    <string name="pref__gestures__swipe_down__label" comment="Preference title">Deslizar para baixo</string>
    <string name="pref__gestures__swipe_left__label" comment="Preference title">Deslizar para a esquerda</string>
    <string name="pref__gestures__swipe_right__label" comment="Preference title">Deslizar para a direita</string>
    <string name="pref__gestures__space_bar_swipe_up__label" comment="Preference title">Deslize para cima na barra de espaços</string>
    <string name="pref__gestures__space_bar_swipe_left__label" comment="Preference title">Deslizar para a esquerda na barra de espaços</string>
    <string name="pref__gestures__space_bar_swipe_right__label" comment="Preference title">Deslizar para a direita na barra de espaços</string>
    <string name="pref__gestures__space_bar_long_press__label" comment="Preference title">Toque longo na barra de espaço</string>
    <string name="pref__gestures__delete_key_swipe_left__label" comment="Preference title">Deslizar para a esquerda na tecla de retrocesso</string>
    <string name="pref__gestures__swipe_velocity_threshold__label" comment="Preference title">Velocidade de deslize</string>
    <string name="pref__gestures__swipe_distance_threshold__label" comment="Preference title">Distância de deslize</string>
    <!-- About UI strings -->
    <string name="about__title" comment="Title of About activity">Sobre o FlorisBoard</string>
    <string name="about__app_icon_content_description" comment="Content description of app icon in About">Ícone do FlorisBoard</string>
    <string name="about__view_licenses" comment="Label of View licenses button in About">Licenças de código aberto</string>
    <string name="about__view_privacy_policy" comment="Label of View privacy policy button in About">Política de privacidade</string>
    <string name="about__view_source_code" comment="Label of View source code button in About">Código</string>
    <string name="about__license__title" comment="Title of Open-source licenses dialog">Licenças de código aberto</string>
    <string name="about__version__title" comment="Preference title">Versão</string>
    <string name="about__version_copied__title" comment="Title of the toast for copying the version string">Versão copiada para a área de transferência</string>
    <string name="about__version_copied__error" comment="Title of the error toast for copying the version string">Ocorreu  um erro: {error_message}</string>
    <string name="about__changelog__title" comment="Preference title">Alterações</string>
    <string name="about__changelog__summary" comment="Preference summary">Novidades</string>
    <string name="about__repository__title" comment="Preference title">Repositório (GitHub)</string>
    <string name="about__repository__summary" comment="Preference summary">Código-fonte, discussões, erros e informações</string>
    <string name="about__privacy_policy__title" comment="Preference title">Política de privacidade</string>
    <string name="about__privacy_policy__summary" comment="Preference summary">A política de privacidade deste projeto</string>
    <string name="about__project_license__title" comment="Preference title">Licença</string>
    <string name="about__project_license__summary" comment="Preference summary">FlorisBoard é licenciado nos termos da {license_name}</string>
    <string name="about__project_license__error_license_text_failed" comment="Error text for license text loading failure">Erro: não foi possível carregar a licença.\n-&gt; Motivo: {error_message}</string>
    <string name="about__project_license__error_reason_asset_manager_null" comment="Error text if asset manager is null">Referência do gestor de ativos é \'null\'</string>
    <string name="about__third_party_licenses__title" comment="Preference title">Licenças de terceiros</string>
    <string name="about__third_party_licenses__summary" comment="Preference summary">Licenças das bibliotecas de terceiros incluídas nesta aplicação</string>
    <!-- Setup UI strings -->
    <string name="setup__title" comment="Title of Setup">Boas vindas!</string>
    <string name="setup__intro_message" comment="Short intro message welcoming new users">Obrigado por utilizar {app_name}! Esta configuração permite-lhe dar os primeiros passos para começar a utilizar {app_name}.</string>
    <string name="setup__footer__privacy_policy" comment="Privacy policy label for URL">Política de privacidade</string>
    <string name="setup__footer__repository" comment="Repository label for URL">Repositório</string>
    <string name="setup__enable_ime__title">Ativar {app_name}</string>
    <string name="setup__enable_ime__description">O sistema Android obriga a que ative o teclado separadamente antes de o poder utilizar. Aceda às definições do sistema e em <i>Idiomas e introdução</i>, ative \"{app_name}\".</string>
    <string name="setup__enable_ime__open_settings_btn">Abrir definições do sistema</string>
    <string name="setup__select_ime__title">Selecione {app_name}</string>
    <string name="setup__select_ime__description">{app_name} está agora ativado no sistema. Para o poder utilizar, escolha {app_name} no  na caixa de diálogo de seleção!</string>
    <string name="setup__select_ime__switch_keyboard_btn">Mudar de teclado</string>
    <string name="setup__finish_up__title">A terminar</string>
    <string name="setup__finish_up__description_p1">{app_name} está agora ativado no sistema e já pode ser personalizado ao seu gosto.</string>
    <string name="setup__finish_up__description_p2">Se encontrar erros, problemas ou se quiser sugerir melhorias, aceda ao repositório do projeto através do ecrã Acerca!</string>
    <string name="setup__finish_up__finish_btn">Iniciar personalização</string>
    <!-- Back up & Restore -->
    <string name="backup_and_restore__title">Backup e restauro</string>
    <string name="backup_and_restore__back_up__title">Copiar dados</string>
    <string name="backup_and_restore__back_up__summary">Gerar um ficheiro de backup das preferências e das personalizações</string>
    <string name="backup_and_restore__back_up__destination">Selecione o destino</string>
    <string name="backup_and_restore__back_up__destination_file_sys">Sistema de ficheiros local</string>
    <string name="backup_and_restore__back_up__destination_share_intent">Aplicação de terceiros através de partilha</string>
    <string name="backup_and_restore__back_up__files">Selecione os dados a copiar</string>
    <string name="backup_and_restore__back_up__files_jetpref_datastore">Preferências</string>
    <string name="backup_and_restore__back_up__files_ime_keyboard">Extensões de teclado</string>
    <string name="backup_and_restore__back_up__files_ime_spelling">Extensões/dicionários de ortografia</string>
    <string name="backup_and_restore__back_up__files_ime_theme">Extensões de temas</string>
    <string name="backup_and_restore__back_up__success">Backup criado com sucesso!</string>
    <string name="backup_and_restore__back_up__failure">Falha ao criar o backup: {error_message}</string>
    <string name="backup_and_restore__restore__title">Restaurar dados</string>
    <string name="backup_and_restore__restore__summary">Restaurar preferências e personalizações de um backup</string>
    <string name="backup_and_restore__restore__files">Selecione os dados a restaurar</string>
    <string name="backup_and_restore__restore__metadata">Arquivo selecionado</string>
    <string name="backup_and_restore__restore__metadata_warn_different_version">Este arquivo foi gerado com uma versão mais antiga da aplicação que, normalmente, é funcional. Contudo, tenha em atenção de que podem ocorrer alguns erros e algumas preferências podem não ser restauradas corretamente.</string>
    <string name="backup_and_restore__restore__metadata_warn_different_vendor">Este arquivo foi criado com uma aplicação de terceiros que, normalmente, não é funcional. Pode perder alguns dados e deve utilizar esta opção por sua conta e risco!</string>
    <string name="backup_and_restore__restore__metadata_error_invalid_metadata">Este arquivo possui dados inválidos. É provavél que esteja corrompido e modificado incorretamente. Não é possível restaurar os dados.</string>
    <string name="backup_and_restore__restore__metadata_error_nothing_to_restore">Este arquivo não possui quaisquer dados para restaurar. Por favor escolha outro.</string>
    <string name="backup_and_restore__restore__mode">Modo de restauro</string>
    <string name="backup_and_restore__restore__mode_merge">Juntar com os dados atuais</string>
    <string name="backup_and_restore__restore__mode_erase_and_overwrite">Apagar e substituir os dados atuais</string>
    <string name="backup_and_restore__restore__success">Dados restaurados com sucesso!</string>
    <string name="backup_and_restore__restore__failure">Falha ao restaurar dados: {error_message}</string>
    <!-- Crash Dialog strings -->
    <string name="crash_dialog__title" comment="Title of crash dialog">Relatório de erros do FlorisBoard</string>
    <string name="crash_dialog__description" comment="Description of crash dialog">Desculpe o inconveniente mas a aplicação encerrou devido a um problema desconhecido.</string>
    <string name="crash_dialog__report_instructions" comment="Issue tracker report instructions for the crash dialog. The %s placeholder is the name of the crash report template and always in English/LTR.">Se quiser reportar este erro, analise o registo de erros reportados no GitHub e verifique se este problema já foi reportado.\nSe não o encontrar, copie o relatório de erro e abra uma ocorrência. Utilize o modelo \"%s\", preencha a descrição, os passos necessários para reproduzir o erro e cole o relatório no final. Desta forma, pode ajudar a melhorar o Floriboard. Obrigado!</string>
    <string name="crash_dialog__copy_to_clipboard" comment="Label of Copy to clipboard button in crash dialog">Copiar para a área de transferência</string>
    <string name="crash_dialog__copy_to_clipboard_success" comment="Label of Copy to clipboard success message in crash dialog">Copiado para a área de transferência</string>
    <string name="crash_dialog__copy_to_clipboard_failure" comment="Label of Copy to clipboard failure message in crash dialog">Não foi possível copiar. Não existe um gestor para a área de transferência.</string>
    <string name="crash_dialog__open_issue_tracker" comment="Label of Open issue tracker button in crash dialog">Abrir registo de erros (github.com)</string>
    <string name="crash_dialog__close" comment="Label of Close button in crash dialog">Fechar</string>
    <string name="crash_notification_channel__title" comment="Title of crash notification channel">Relatórios de erros do FlorisBoard</string>
    <string name="crash_once_notification__title" comment="Title of the notification for a single crash">O FlorisBoard parou de funcionar…</string>
    <string name="crash_once_notification__body" comment="Body of the notification for a single crash">Toque para ver os detalhes do erro</string>
    <string name="crash_multiple_notification__title" comment="Title of the notification for consecutive crashes">Parece que FlorisBoard não está a funcionar muito bem…</string>
    <string name="crash_multiple_notification__body" comment="Body of the notification for consecutive crashes">A ativar o teclado usado anteriormente para parar este ciclo. Toque para ver os detalhes do erro.</string>
    <!-- Clipboard strings -->
    <string name="clipboard__header_title">Área de transferência</string>
    <string name="clipboard__disabled__title">O histórico da área de transferência está desativado</string>
    <string name="clipboard__disabled__message">O histórico da área de transferência de {app_name} permite-lhe armazenar e aceder ao texto e às imagens que tenha copiado, com a possibilidade de fixar itens, configurar a limpeza automática e definir um tempo limite para o acesso.</string>
    <string name="clipboard__disabled__enable_button">Ativar histórico da área de transferência</string>
    <string name="clipboard__empty__title">A área de transferência está vazia</string>
    <string name="clipboard__empty__message">Assim que copiar texto ou imagens, estes serão mostrados aqui.</string>
    <string name="clipboard__locked__title">A área de transferência está bloqueada</string>
    <string name="clipboard__locked__message">Para aceder à sua área de transferência, tem que desbloquear o dispositivo.</string>
    <string name="clipboard__group_pinned">Fixado</string>
    <string name="clipboard__group_recent">Recente</string>
    <string name="clipboard__group_other">Outro</string>
    <string name="clipboard__item_description_email">Email</string>
    <string name="clipboard__item_description_url">URL</string>
    <string name="clip__clear_history">Limpar histórico</string>
    <string name="clip__unpin_item">Desafixar item</string>
    <string name="clip__pin_item">Fixar item</string>
    <string name="clip__delete_item">Remover</string>
    <string name="clip__paste_item">Colar</string>
    <string name="clip__back_to_text_input">Voltar para a introdução de texto</string>
    <string name="clip__cant_paste">A aplicação não permite a colagem deste conteúdo.</string>
    <string name="clipboard__cleared_primary_clip">Item principal limpo</string>
    <string name="clipboard__cleared_history">Histórico limpo</string>
    <string name="clipboard__cleared_full_history">Todo o histórico foi limpo</string>
    <string name="clipboard__confirm_clear_history__message">Tem a certeza que quer limpar o seu histórico de pesquisas?</string>
    <string name="settings__clipboard__title">Área de transferência</string>
    <string name="pref__clipboard__use_internal_clipboard__label">Usar área de transferência interna</string>
    <string name="pref__clipboard__use_internal_clipboard__summary">Utilizar área de transferência interna e não a do sistema</string>
    <string name="pref__clipboard__sync_from_system_clipboard__label">Sincronizar da área de transferência do sistema</string>
    <string name="pref__clipboard__sync_from_system_clipboard__summary">Atualizações à área de transferência do sistema atualizam a do Florisboard</string>
    <string name="pref__clipboard__sync_to_system_clipboard__label">Sincronizar para área de transferência do sistema</string>
    <string name="pref__clipboard__sync_to_system_clipboard__summary">As atualizações à área de transferência do Florisboard atualizam a do sistema</string>
    <string name="pref__clipboard__group_clipboard_history__label">Histórico da área de transferência</string>
    <string name="pref__clipboard__enable_clipboard_history__label">Ativar histórico da área de transferência</string>
    <string name="pref__clipboard__enable_clipboard_history__summary">Manter itens da área de transferência para acesso rápido</string>
    <string name="pref__clipboard__clean_up_old__label">Remover itens antigos</string>
    <string name="pref__clipboard__clean_up_after__label">Remover itens antigos após</string>
    <string name="pref__clipboard__limit_history_size__label">Limitar histórico</string>
    <string name="pref__clipboard__max_history_size__label">Tamanho máximo do histórico</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__label">Limpeza do item principal afeta o histórico</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__summary">Ao limpar o item principal também remove a entrada do histórico</string>
    <!-- Devtools strings -->
    <string name="devtools__title" comment="Title of Devtools screen. Translators: treat this string as 'Developer tools' for translation, except a similar short term is available for your language.">Ferramentas de programação</string>
    <string name="devtools__enabled__label" comment="Label of Enable developer tools in Devtools">Ativar ferramentas de programação</string>
    <string name="devtools__enabled__summary" comment="Summary of Enable developer tools in Devtools">Ferramentas desenhadas para depuração e resolução de erros</string>
    <string name="devtools__show_primary_clip__label" comment="Label of Show primary clip in Devtools">Mostrar item principal</string>
    <string name="devtools__show_primary_clip__summary" comment="Summary of Show primary clip in Devtools">Substitui o item principal existente na área de transferência</string>
    <string name="devtools__show_input_state_overlay__label" comment="Label of Show input cache overlay in Devtools">Mostrar a interface do estado de introdução</string>
    <string name="devtools__show_key_touch_boundaries__label" comment="Label of Show key touch boundaries in Devtools">Mostrar limite das teclas</string>
    <string name="devtools__show_key_touch_boundaries__summary" comment="Summary of Show key touch boundaries in Devtools">Destacar a vermelho os limites das teclas</string>
    <string name="devtools__clear_udm_internal_database__label" comment="Label of Clear internal user dictionary database in Devtools">Limpar dicionário do utilizador</string>
    <string name="devtools__clear_udm_internal_database__summary" comment="Summary of Clear internal user dictionary database in Devtools">Remove todas as palavras do dicionário do utilizador</string>
    <string name="devtools__reset_flag__label" comment="Label of Reset flag preferences in Devtools">Repor \'flag\' \"{flag_name}\"</string>
    <string name="devtools__reset_flag_is_ime_set_up__summary" comment="Summary of Reset is IME set up flag in Devtools">Ação de depuração para voltar a mostrar a configuração</string>
    <string name="devtools__test_crash_report__label" comment="Label of Test Crash Report in Devtools">Ecrã de testes</string>
    <string name="devtools__test_crash_report__summary" comment="Summary of Test Crash Report in Devtools">Ação de depuração que, propositadamente, provoca um erro</string>
    <string name="devtools__group_android__title" comment="Title of Android group in Devtools">Ferramentas Android</string>
    <string name="devtools__android_settings_global__title" comment="Title of Android settings (global) screen">Definições globais Android</string>
    <string name="devtools__android_settings_secure__title" comment="Title of Android settings (secure) screen">Definições de segurança Android</string>
    <string name="devtools__android_settings_system__title" comment="Title of Android settings (system) screen">Definições de sistema Android</string>
    <string name="devtools__android_locales__title" comment="Title of Android locales screen">Idiomas do sistema</string>
    <!-- Extension strings -->
    <string name="ext__meta__authors">Autores</string>
    <string name="ext__meta__components">Componentes nativos</string>
    <string name="ext__meta__components_theme">Temas nativos</string>
    <string name="ext__meta__components_none_found">O arquivo não contém quaisquer componentes incorporados.</string>
    <string name="ext__meta__description">Descrição</string>
    <string name="ext__meta__homepage">Página web</string>
    <string name="ext__meta__id">ID</string>
    <string name="ext__meta__issue_tracker">Erros e problemas</string>
    <string name="ext__meta__keywords">Palavras-chaves</string>
    <string name="ext__meta__label">Etiqueta</string>
    <string name="ext__meta__license">Licença</string>
    <string name="ext__meta__maintainers">Mantido por</string>
    <string name="ext__meta__maintainers_by">De: {maintainers}</string>
    <string name="ext__meta__title">Título</string>
    <string name="ext__meta__version">Versão</string>
    <string name="ext__error__not_found_title">Extensão não encontrada</string>
    <string name="ext__error__not_found_description">Não foi possível encontrar a extensão coma ID \"{id}\".</string>
    <string name="ext__editor__title_create_any">Criar extensão</string>
    <string name="ext__editor__title_create_keyboard">Criar extensão de teclado</string>
    <string name="ext__editor__title_create_theme">Criar extensão de tema</string>
    <string name="ext__editor__title_edit_any">Editar extensão</string>
    <string name="ext__editor__title_edit_keyboard">Editar extensão de teclado</string>
    <string name="ext__editor__title_edit_theme">Editar extensão de tema</string>
    <string name="ext__editor__metadata__title">Gerir meta-dados</string>
    <string name="ext__editor__metadata__title_invalid">Meta-dados inválidos</string>
    <string name="ext__editor__dependencies__title">Gerir dependências</string>
    <string name="ext__editor__files__title">Gerir ficheiros de arquivo</string>
    <string name="ext__editor__create_component__title">Criar componente</string>
    <string name="ext__editor__create_component__title_theme">Criar tema</string>
    <string name="ext__editor__create_component__from_empty">Vazio</string>
    <string name="ext__editor__create_component__from_existing">De um existente</string>
    <string name="ext__editor__edit_component__title">Editar componente</string>
    <string name="ext__editor__edit_component__title_theme">Editar componente do tema</string>
    <string name="ext__export__success">Extensão exportada com sucesso!</string>
    <string name="ext__export__failure">Falha ao exportar a extensão: {error_message}</string>
    <string name="ext__import__success">Extensão importada com sucesso!</string>
    <string name="ext__import__failure">Falha ao importar a extensão: {error_message}</string>
    <string name="ext__import__ext_any" comment="Title of Importer screen for import of any supported FlorisBoard extension">Importar extensão</string>
    <string name="ext__import__ext_keyboard" comment="Title of Importer screen for keyboard extension import">Importar extensão do teclado</string>
    <string name="ext__import__ext_theme" comment="Title of Importer screen for theme extension import">Importar extensão</string>
    <string name="ext__import__file_skip" comment="Label when a file cannot be imported in the current context. The actual reason string is in a separate text view below this string.">Não foi possível a importação. Motivo:</string>
    <string name="ext__import__file_skip_unsupported" comment="Reason string when file is unsupported">Tipo de ficheiros não suportado ou desconhecido.</string>
    <string name="ext__import__error_unexpected_exception" comment="Label when an error occurred during import. The error message will be appended below this text view">Ocorreu um erro inesperado durante a importação. Detalhes:</string>
    <!-- Action strings -->
    <string name="action__add">Adicionar</string>
    <string name="action__apply">Aplicar</string>
    <string name="action__back_up">Criar backup</string>
    <string name="action__cancel">Cancelar</string>
    <string name="action__create">Criar</string>
    <string name="action__default">Padrão</string>
    <string name="action__delete">Eliminar</string>
    <string name="action__delete_confirm_title">Confirmar</string>
    <string name="action__delete_confirm_message">Tem a certeza de que deseja eliminar \"{name}\"? Esta ação não pode ser revertida.</string>
    <string name="action__discard">Descartar</string>
    <string name="action__discard_confirm_title">Alterações não guardadas</string>
    <string name="action__edit">Editar</string>
    <string name="action__export">Exportar</string>
    <string name="action__import">Importar</string>
    <string name="action__no">Não</string>
    <string name="action__ok">OK</string>
    <string name="action__restore">Restaurar</string>
    <string name="action__save">Guardar</string>
    <string name="action__select">Selecionar</string>
    <string name="action__select_dir">Selecionar diretório</string>
    <string name="action__select_dirs">Selecionar diretórios</string>
    <string name="action__select_file">Selecionar ficheiro</string>
    <string name="action__select_files">Selecionar ficheiros</string>
    <string name="action__yes">Sim</string>
    <!-- Error strings (generic) -->
    <string name="error__title">Erro</string>
    <string name="error__details">Detalhes</string>
    <string name="error__invalid">Inválido</string>
    <string name="error__snackbar_message">Ocorreu um erro</string>
    <string name="error__snackbar_message_template">Ocorreu  um erro: {error_message}</string>
    <!-- General strings -->
    <string name="general__example_given" comment="This string is used where an example will be inserted in the {example} block. When translating, use the abbreviation used in your language for 'example given'.">e.g. {example}</string>
    <string name="general__no_browser_app_found_for_url">Não existe uma aplicação para gerir o URL {url}</string>
    <string name="general__select_dropdown_value_placeholder">&#45; selecionar &#45;</string>
    <string name="general__unlimited">Ilimitado</string>
    <!-- Screen orientation strings -->
    <string name="screen_orientation__portrait">Retrato</string>
    <string name="screen_orientation__landscape">Paisagem</string>
    <string name="screen_orientation__vertical">Vertical</string>
    <string name="screen_orientation__horizontal">Horizontal</string>
    <!-- State strings -->
    <string name="state__disabled">Desativada</string>
    <string name="state__enabled">Ativada</string>
    <string name="state__no_dir_selected">Nenhum diretório selecionado</string>
    <string name="state__no_dirs_selected">Nenhum diretório selecionado</string>
    <string name="state__no_file_selected">Nenhum ficheiro selecionado</string>
    <string name="state__no_files_selected">Nenhum ficheiro selecionado</string>
    <!-- Enum label and description strings -->
    <string name="enum__candidates_display_mode__classic" comment="Enum value label">Clássico (3 colunas)</string>
    <string name="enum__candidates_display_mode__dynamic" comment="Enum value label">Largura dinâmica</string>
    <string name="enum__candidates_display_mode__dynamic_scrollable" comment="Enum value label">Largura dinâmica e deslocável</string>
    <string name="enum__display_kbd_after_dialogs__always" comment="Enum value label">Mostrar sempre</string>
    <string name="enum__display_kbd_after_dialogs__never" comment="Enum value label">Nunca mostrar</string>
    <string name="enum__display_kbd_after_dialogs__remember" comment="Enum value label">Lembrar último estado</string>
    <string name="enum__display_language_names_in__system_locale" comment="Enum value label">Idiomas do sistema</string>
    <string name="enum__display_language_names_in__native_locale" comment="Enum value label">Idioma nativo</string>
    <string name="enum__emoji_skin_tone__default" comment="Enum value label">{emoji} Tom de pele padrão</string>
    <string name="enum__emoji_skin_tone__light_skin_tone" comment="Enum value label">{emoji} Tom de pele claro</string>
    <string name="enum__emoji_skin_tone__medium_light_skin_tone" comment="Enum value label">{emoji} Tom de pele médio claro</string>
    <string name="enum__emoji_skin_tone__medium_skin_tone" comment="Enum value label">{emoji} Tom de pele médio</string>
    <string name="enum__emoji_skin_tone__medium_dark_skin_tone" comment="Enum value label">{emoji} Tom de pele médio escuro</string>
    <string name="enum__emoji_skin_tone__dark_skin_tone" comment="Enum value label">{emoji} Tom de pele escuro</string>
    <string name="enum__emoji_hair_style__default" comment="Enum value label">{emoji} Cabelo padrão</string>
    <string name="enum__emoji_hair_style__red_hair" comment="Enum value label">{emoji} Cabelo vermelho</string>
    <string name="enum__emoji_hair_style__curly_hair" comment="Enum value label">{emoji} Cabelo encaracolado</string>
    <string name="enum__emoji_hair_style__white_hair" comment="Enum value label">{emoji} Cabelo branco</string>
    <string name="enum__emoji_hair_style__bald" comment="Enum value label">{emoji} Careca</string>
    <string name="enum__key_hint_mode__accent_priority" comment="Enum value label">Prioridade aos acentos</string>
    <string name="enum__key_hint_mode__accent_priority__description" comment="Enum value description">O carácter inicialmente selecionado após um toque longo será sempre o acento principal ou o símbolo caso não exista um acento</string>
    <string name="enum__key_hint_mode__hint_priority" comment="Enum value label">Prioridade aos símbolos</string>
    <string name="enum__key_hint_mode__hint_priority__description" comment="Enum value description">O carácter inicialmente selecionado após um toque longo será sempre o símbolo ou o acento principal caso não exista um símbolo</string>
    <string name="enum__key_hint_mode__smart_priority" comment="Enum value label">Prioridade inteligente</string>
    <string name="enum__key_hint_mode__smart_priority__description" comment="Enum value description">O carácter inicialmente selecionado após um toque longo é decidido por ser o acento principal ou o símbolo, tendo por base o idioma e a disposição atual</string>
    <string name="enum__input_shift_state__unshifted" comment="Enum value label">Não tabulado</string>
    <string name="enum__input_shift_state__shifted_manual" comment="Enum value label">Tabulado (manual)</string>
    <string name="enum__input_shift_state__shifted_automatic" comment="Enum value label">Tabulado (automático)</string>
    <string name="enum__input_shift_state__caps_lock" comment="Enum value label">Caps lock</string>
    <string name="enum__landscape_input_ui_mode__never_show" comment="Enum value label">Não mostrar</string>
    <string name="enum__landscape_input_ui_mode__always_show" comment="Enum value label">Mostrar sempre</string>
    <string name="enum__landscape_input_ui_mode__dynamically_show" comment="Enum value label">Mostrar dinamicamente</string>
    <string name="enum__one_handed_mode__start" comment="Enum value label">Modo esquerdino</string>
    <string name="enum__one_handed_mode__end" comment="Enum value label">Modo destro</string>
    <string name="enum__shape_corner__top_start" comment="Enum value label">Início do topo</string>
    <string name="enum__shape_corner__top_end" comment="Enum value label">Fim do topo</string>
    <string name="enum__shape_corner__bottom_end" comment="Enum value label">Fim do fundo</string>
    <string name="enum__shape_corner__bottom_start" comment="Enum value label">Inicio do fundo</string>
    <string name="enum__snygg_level__basic" comment="Enum value label">Básico</string>
    <string name="enum__snygg_level__advanced" comment="Enum value label">Avançado</string>
    <string name="enum__snygg_level__developer" comment="Enum value label">Programador</string>
    <string name="enum__spelling_language_mode__use_system_languages" comment="Enum value label">Utilizar idiomas do sistema</string>
    <string name="enum__spelling_language_mode__use_keyboard_subtypes" comment="Enum value label">Utilizar subtipos de teclado</string>
    <string name="enum__swipe_action__no_action" comment="Enum value label">Sem ação</string>
    <string name="enum__swipe_action__cycle_to_previous_keyboard_mode" comment="Enum value label">Mudar para o anterior modo de teclado</string>
    <string name="enum__swipe_action__cycle_to_next_keyboard_mode" comment="Enum value label">Mudar para o próximo modo de teclado</string>
    <string name="enum__swipe_action__delete_character" comment="Enum value label">Eliminar carater antes do cursor</string>
    <string name="enum__swipe_action__delete_characters_precisely" comment="Enum value label">Eliminar caracteres com precisão</string>
    <string name="enum__swipe_action__delete_word" comment="Enum value label">Eliminar palavra antes do cursor</string>
    <string name="enum__swipe_action__delete_words_precisely" comment="Enum value label">Eliminar palavras com precisão</string>
    <string name="enum__swipe_action__hide_keyboard" comment="Enum value label">Ocultar teclado</string>
    <string name="enum__swipe_action__insert_space" comment="Enum value label">Inserir espaço</string>
    <string name="enum__swipe_action__move_cursor_up" comment="Enum value label">Mover cursor para cima</string>
    <string name="enum__swipe_action__move_cursor_down" comment="Enum value label">Mover cursor para baixo</string>
    <string name="enum__swipe_action__move_cursor_left" comment="Enum value label">Mover cursor para a esquerda</string>
    <string name="enum__swipe_action__move_cursor_right" comment="Enum value label">Mover cursor para a direita</string>
    <string name="enum__swipe_action__move_cursor_start_of_line" comment="Enum value label">Mover cursor para o início da linha</string>
    <string name="enum__swipe_action__move_cursor_end_of_line" comment="Enum value label">Mover cursor para o fim da linha</string>
    <string name="enum__swipe_action__move_cursor_start_of_page" comment="Enum value label">Mover cursor para o início da página</string>
    <string name="enum__swipe_action__move_cursor_end_of_page" comment="Enum value label">Mover cursor para o fim da página</string>
    <string name="enum__swipe_action__switch_to_clipboard_context" comment="Enum value label">Abrir gestor da área de transferência</string>
    <string name="enum__swipe_action__shift" comment="Enum value label">Deslocar</string>
    <string name="enum__swipe_action__redo" comment="Enum value label">Refazer</string>
    <string name="enum__swipe_action__undo" comment="Enum value label">Desfazer</string>
    <string name="enum__swipe_action__select_characters_precisely" comment="Enum value label">Selecionar carateres com precisão</string>
    <string name="enum__swipe_action__select_words_precisely" comment="Enum value label">Selecionar palavras com precisão</string>
    <string name="enum__swipe_action__show_input_method_picker" comment="Enum value label">Mostrar seletor do método de introdução</string>
    <string name="enum__swipe_action__switch_to_prev_keyboard" comment="Enum value label">Mudar para o teclado anterior</string>
    <string name="enum__swipe_action__switch_to_prev_subtype" comment="Enum value label">Mudar para o subtipo anterior</string>
    <string name="enum__swipe_action__switch_to_next_subtype" comment="Enum value label">Mudar para o subtipo seguinte</string>
    <string name="enum__swipe_action__toggle_smartbar_visibility" comment="Enum value label">Comutar visibilidade da barra inteligente</string>
    <string name="enum__theme_mode__always_day" comment="Enum value label">Sempre dia</string>
    <string name="enum__theme_mode__always_night" comment="Enum value label">Sempre noite</string>
    <string name="enum__theme_mode__follow_system" comment="Enum value label">Seguir sistema</string>
    <string name="enum__theme_mode__follow_time" comment="Enum value label">Seguir hora do dia</string>
    <string name="enum__utility_key_action__switch_to_emojis" comment="Enum value label">Mudar para emojis</string>
    <string name="enum__utility_key_action__switch_language" comment="Enum value label">Mudar de idioma</string>
    <string name="enum__utility_key_action__switch_keyboard_app" comment="Enum value label">Mudar aplicação de teclado</string>
    <string name="enum__utility_key_action__dynamic_switch_language_emojis" comment="Enum value label">Dinâmico: Mudar para emojis/Mudar de idioma</string>
    <!-- Unit strings (symbols) -->
    <!-- Unit strings (written words) -->
    <plurals name="unit__hours__written">
        <item quantity="one">{v} hora</item>
        <item quantity="other">{v} horas</item>
    </plurals>
    <plurals name="unit__minutes__written">
        <item quantity="one">{v} minuto</item>
        <item quantity="other">{v} minutos</item>
    </plurals>
    <plurals name="unit__seconds__written">
        <item quantity="one">{v} segundo</item>
        <item quantity="other">{v} segundos</item>
    </plurals>
    <plurals name="unit__items__written">
        <item quantity="one">{v} item</item>
        <item quantity="other">{v} itens</item>
    </plurals>
</resources>
