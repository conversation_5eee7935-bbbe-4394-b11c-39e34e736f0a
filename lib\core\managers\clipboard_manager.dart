import 'dart:async';
import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:flutter/services.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../constants/app_constants.dart';
import '../preferences/preferences_manager.dart';
import '../services/ime_service.dart';

class ClipboardManager extends Cubit<ClipboardState> {
  ClipboardManager() : super(const ClipboardState.initial());

  final List<ClipboardItem> _history = [];
  final int _maxHistorySize = 50;
  Timer? _syncTimer;

  Future<void> initialize() async {
    await _loadHistory();
    _startSyncTimer();
    _listenToSystemClipboard();
  }

  Future<void> _loadHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = prefs.getString('clipboard_history');
      if (historyJson != null) {
        final List<dynamic> historyList = json.decode(historyJson);
        _history.clear();
        _history.addAll(
          historyList.map((item) => ClipboardItem.fromJson(item)).toList(),
        );
        emit(state.copyWith(history: List.from(_history)));
      }
    } catch (e) {
      print('Failed to load clipboard history: $e');
    }
  }

  Future<void> _saveHistory() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final historyJson = json.encode(_history.map((item) => item.toJson()).toList());
      await prefs.setString('clipboard_history', historyJson);
    } catch (e) {
      print('Failed to save clipboard history: $e');
    }
  }

  void _startSyncTimer() {
    _syncTimer = Timer.periodic(const Duration(seconds: 2), (timer) {
      _checkSystemClipboard();
    });
  }

  void _listenToSystemClipboard() {
    // Listen to system clipboard changes
    // Note: This requires platform-specific implementation
  }

  Future<void> _checkSystemClipboard() async {
    if (!PreferencesManager.instance.clipboard.syncToFloris) return;

    try {
      final clipboardData = await Clipboard.getData(Clipboard.kTextPlain);
      if (clipboardData?.text != null && clipboardData!.text!.isNotEmpty) {
        final text = clipboardData.text!;
        
        // Check if this text is already the most recent item
        if (_history.isNotEmpty && _history.first.text == text) return;
        
        await addToHistory(ClipboardItem(
          id: _generateId(),
          text: text,
          timestamp: DateTime.now(),
          type: ClipboardItemType.TEXT,
        ));
      }
    } catch (e) {
      print('Failed to check system clipboard: $e');
    }
  }

  Future<void> addToHistory(ClipboardItem item) async {
    // Remove existing item with same text
    _history.removeWhere((existing) => existing.text == item.text);
    
    // Add to beginning
    _history.insert(0, item);
    
    // Trim to max size
    if (_history.length > _maxHistorySize) {
      _history.removeRange(_maxHistorySize, _history.length);
    }
    
    await _saveHistory();
    emit(state.copyWith(history: List.from(_history)));
  }

  Future<void> pasteItem(ClipboardItem item) async {
    // Copy to system clipboard if enabled
    if (PreferencesManager.instance.clipboard.syncToSystem) {
      await Clipboard.setData(ClipboardData(text: item.text));
    }
    
    // Insert text through IME
    await IMEService.commitText(item.text);
    
    // Move item to top of history
    await addToHistory(item.copyWith(timestamp: DateTime.now()));
  }

  Future<void> removeItem(String itemId) async {
    _history.removeWhere((item) => item.id == itemId);
    await _saveHistory();
    emit(state.copyWith(history: List.from(_history)));
  }

  Future<void> clearHistory() async {
    _history.clear();
    await _saveHistory();
    emit(state.copyWith(history: []));
  }

  Future<void> pinItem(String itemId) async {
    final itemIndex = _history.indexWhere((item) => item.id == itemId);
    if (itemIndex != -1) {
      final item = _history[itemIndex];
      _history[itemIndex] = item.copyWith(isPinned: true);
      await _saveHistory();
      emit(state.copyWith(history: List.from(_history)));
    }
  }

  Future<void> unpinItem(String itemId) async {
    final itemIndex = _history.indexWhere((item) => item.id == itemId);
    if (itemIndex != -1) {
      final item = _history[itemIndex];
      _history[itemIndex] = item.copyWith(isPinned: false);
      await _saveHistory();
      emit(state.copyWith(history: List.from(_history)));
    }
  }

  String _generateId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  @override
  Future<void> close() {
    _syncTimer?.cancel();
    return super.close();
  }
}

class ClipboardItem extends Equatable {
  const ClipboardItem({
    required this.id,
    required this.text,
    required this.timestamp,
    required this.type,
    this.isPinned = false,
    this.isFavorite = false,
    this.source,
  });

  final String id;
  final String text;
  final DateTime timestamp;
  final ClipboardItemType type;
  final bool isPinned;
  final bool isFavorite;
  final String? source;

  ClipboardItem copyWith({
    String? id,
    String? text,
    DateTime? timestamp,
    ClipboardItemType? type,
    bool? isPinned,
    bool? isFavorite,
    String? source,
  }) {
    return ClipboardItem(
      id: id ?? this.id,
      text: text ?? this.text,
      timestamp: timestamp ?? this.timestamp,
      type: type ?? this.type,
      isPinned: isPinned ?? this.isPinned,
      isFavorite: isFavorite ?? this.isFavorite,
      source: source ?? this.source,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'text': text,
      'timestamp': timestamp.millisecondsSinceEpoch,
      'type': type.index,
      'isPinned': isPinned,
      'isFavorite': isFavorite,
      'source': source,
    };
  }

  factory ClipboardItem.fromJson(Map<String, dynamic> json) {
    return ClipboardItem(
      id: json['id'] as String,
      text: json['text'] as String,
      timestamp: DateTime.fromMillisecondsSinceEpoch(json['timestamp'] as int),
      type: ClipboardItemType.values[json['type'] as int],
      isPinned: json['isPinned'] as bool? ?? false,
      isFavorite: json['isFavorite'] as bool? ?? false,
      source: json['source'] as String?,
    );
  }

  @override
  List<Object?> get props => [id, text, timestamp, type, isPinned, isFavorite, source];
}

class ClipboardState extends Equatable {
  const ClipboardState({
    required this.history,
    required this.historyEnabled,
    required this.isLoading,
    this.error,
  });

  const ClipboardState.initial()
      : history = const [],
        historyEnabled = true,
        isLoading = false,
        error = null;

  final List<ClipboardItem> history;
  final bool historyEnabled;
  final bool isLoading;
  final String? error;

  ClipboardState copyWith({
    List<ClipboardItem>? history,
    bool? historyEnabled,
    bool? isLoading,
    String? error,
  }) {
    return ClipboardState(
      history: history ?? this.history,
      historyEnabled: historyEnabled ?? this.historyEnabled,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  @override
  List<Object?> get props => [history, historyEnabled, isLoading, error];
}
