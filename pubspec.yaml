name: kirat_keyboard
description: A customizable keyboard for Android built with Flutter

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Local Storage
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  shared_preferences: ^2.2.2
  
  # Database
  sqflite: ^2.3.0
  
  # JSON & Serialization
  json_annotation: ^4.8.1
  freezed_annotation: ^2.4.1
  
  # File Management
  path_provider: ^2.1.1
  path: ^1.8.3
  
  # UI & Animations
  flutter_staggered_animations: ^1.1.1
  lottie: ^2.7.0
  
  # Utilities
  collection: ^1.17.2
  rxdart: ^0.27.7
  
  # Platform Integration
  # android_intent_plus: ^4.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  
  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  freezed: ^2.4.6
  hive_generator: ^2.0.1
  
  # Testing
  bloc_test: ^9.1.4
  mockito: ^5.4.2
  
  # Linting
  flutter_lints: ^3.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/themes/
    - assets/layouts/
    - assets/emoji/
    - assets/dictionary/
    - assets/extensions/
    - assets/fonts/
  
  # fonts:
  #   - family: KiratKeyboard
  #     fonts:
  #       - asset: assets/fonts/KiratKeyboard-Regular.ttf
  #       - asset: assets/fonts/KiratKeyboard-Bold.ttf
  #         weight: 700
