{"all": {"a": {"relevant": [{"$": "auto_text_key", "code": 261, "label": "ą"}, {"$": "auto_text_key", "code": 224, "label": "à"}]}, "c": {"relevant": [{"$": "auto_text_key", "code": 263, "label": "ć"}]}, "e": {"relevant": [{"$": "auto_text_key", "code": 281, "label": "ę"}]}, "l": {"relevant": [{"$": "auto_text_key", "code": 322, "label": "ł"}]}, "n": {"relevant": [{"$": "auto_text_key", "code": 324, "label": "ń"}]}, "o": {"relevant": [{"$": "auto_text_key", "code": 243, "label": "ó"}]}, "s": {"relevant": [{"$": "auto_text_key", "code": 347, "label": "ś"}]}, "x": {"relevant": [{"$": "auto_text_key", "code": 378, "label": "ź"}]}, "z": {"relevant": [{"$": "auto_text_key", "code": 380, "label": "ż"}, {"$": "auto_text_key", "code": 378, "label": "ź"}]}, "~right": {"main": {"code": 44, "label": ","}, "relevant": [{"code": 38, "label": "&"}, {"code": 37, "label": "%"}, {"code": 43, "label": "+"}, {"code": 34, "label": "\""}, {"code": 45, "label": "-"}, {"code": 58, "label": ":"}, {"code": 39, "label": "'"}, {"code": 64, "label": "@"}, {"code": 59, "label": ";"}, {"code": 47, "label": "/"}, {"$": "layout_direction_selector", "ltr": {"code": 40, "label": "("}, "rtl": {"code": 41, "label": "("}}, {"$": "layout_direction_selector", "ltr": {"code": 41, "label": ")"}, "rtl": {"code": 40, "label": ")"}}, {"code": 35, "label": "#"}, {"code": 33, "label": "!"}, {"code": 63, "label": "?"}]}}, "uri": {"~right": {"main": {"code": -255, "label": ".com"}, "relevant": [{"code": -255, "label": ".pl"}, {"code": -255, "label": ".gov"}, {"code": -255, "label": ".edu"}, {"code": -255, "label": ".org"}, {"code": -255, "label": ".net"}]}}}