<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <style name="FlorisAppTheme.Splash" parent="Theme.SplashScreen.IconBackground">
        <item name="postSplashScreenTheme">@style/FlorisAppTheme</item>
        <item name="windowSplashScreenBackground">@color/florisApp_windowBackground</item>
        <item name="windowSplashScreenIconBackgroundColor">@color/ic_app_icon_background</item>
        <item name="windowSplashScreenAnimatedIcon">@drawable/floris_app_icon_foreground</item>

        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
    </style>

    <style name="FlorisAppTheme" parent="android:style/Theme.Material.NoActionBar">
        <item name="android:colorPrimary">@color/colorPrimary</item>
        <item name="android:colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:colorAccent">@color/colorAccent</item>

        <!-- IMPORTANT: the following 2 colors MUST be references and not inline colors, or older
             versions of Android will not set the window background and thus causing weird
             rendering glitches!! -->
        <item name="android:windowBackground">@color/florisApp_windowBackground</item>
        <item name="android:colorBackground">@color/florisApp_windowBackground</item>

        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
    </style>

    <style name="CrashDialogTheme" parent="android:style/Theme.Material.NoActionBar"/>

    <style name="FlorisImeTheme" parent="android:Theme.Material.InputMethod">
        <!-- Need this to prevent Xiaomi devices from messing with our beautiful UI -->
        <item name="android:forceDarkAllowed" tools:targetApi="q">false</item>
        <!-- FFS -->
        <item name="android:navigationBarColor">@android:color/transparent</item>
    </style>

    <style name="FlorisAppTheme.Transparent" parent="android:style/Theme.Material.NoActionBar">
        <item name="android:colorPrimary">@android:color/transparent</item>
        <item name="android:colorPrimaryDark">@android:color/transparent</item>
        <item name="android:colorAccent">@android:color/transparent</item>

        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:colorBackground">@android:color/transparent</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

</resources>
