<p><i>FlorisBoard</i> is an open-source keyboard aimed at providing you with an easy way to type while respecting your privacy.</p>
<p><b>Note:</b> This project is currently in early-beta stage. If you want to see a feature being implemented or want to report a bug, please visit this project's repository (linked in the end of the description) on GitHub and file an issue. This helps making FlorisBoard even better! Thank you!</p>
<p><b>Currently implemented and fully working features:</b></p>
<ul>
    <li>Huge variety of Latin keyboard layouts</li>
    <li>Limited support for non-Latin keyboard layouts (Arabic, Persian and Hebrew currently, more are planned)</li>
    <li>Easy switching between languages/layouts by defining subtypes in the settings</li>
    <li>Full theme customization + theme presets for day/night themes</li>
    <li>Automatic day/night theme switching</li>
    <li>Keyboard layouts for typing in a (phone) number</li>
    <li>Special characters input</li>
    <li>Emoji/Emoticon keyboard</li>
    <li>One-handed/compact mode for easier typing on large devices</li>
    <li>Customization of key press sound/vibration</li>
    <li>Customizable actions for gestures: swipe up/down/left/right, space bar left/right, delete key swipe)</li>
    <li>Integrated special symbols into character layouts</li>
    <li>Clipboard/Cursor toolbar</li>
    <li>Clipboard manager/history</li>
</ul>
