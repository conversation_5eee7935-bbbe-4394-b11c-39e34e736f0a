project_id_env: "FSEC_CROWDIN_PROJECT_ID"
api_token_env: "FSEC_CROWDIN_PERSONAL_TOKEN"
base_path: "."
base_url: "https://api.crowdin.com"
preserve_hierarchy: true

files:
  - source: "/app/src/main/res/values/strings.xml"
    translation: "/app/src/main/res/values-%android_code%/%original_file_name%"
    translate_attributes: 0
    languages_mapping:
      android_code:
        ar: "ar"
        ast: "ast-rES"
        bg: "bg"
        bs: "bs"
        ca: "ca"
        ckb: "ckb"
        cs: "cs"
        da: "da"
        de: "de"
        el: "el"
        eo: "eo"
        es-ES: "es"
        fa: "fa"
        fi: "fi"
        fr: "fr"
        he: "iw"
        hr: "hr"
        hu: "hu"
        id: "in"
        it: "it"
        ja: "ja"
        kmr: "ku"
        mk: "mk"
        nl: "nl"
        "no": "no"
        pl: "pl"
        pt-PT: "pt"
        ru: "ru"
        sk: "sk"
        sl: "sl"
        sr: "sr"
        sv-SE: "sv"
        tr: "tr"
        uk: "uk"
        zgh: "zgh"
