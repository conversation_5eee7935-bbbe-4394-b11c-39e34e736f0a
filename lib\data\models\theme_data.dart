import 'package:flutter/material.dart';
import 'package:equatable/equatable.dart';

import '../../core/constants/app_constants.dart';

// Simplified theme data without complex serialization for now
class KiratTheme extends Equatable {
  const KiratTheme({
    required this.id,
    required this.name,
    required this.isNightTheme,
    required this.styles,
    required this.colorScheme,
    this.variables = const {},
    this.authors = const [],
    this.description,
    this.version,
  });

  final String id;
  final String name;
  final bool isNightTheme;
  final Map<String, KiratStyleSet> styles;
  final KiratColorScheme colorScheme;
  final Map<String, String> variables;
  final List<String> authors;
  final String? description;
  final String? version;

  @override
  List<Object?> get props => [
        id,
        name,
        isNightTheme,
        styles,
        colorScheme,
        variables,
        authors,
        description,
        version,
      ];
}

class KiratColorScheme extends Equatable {
  const KiratColorScheme({
    required this.primary,
    required this.onPrimary,
    required this.secondary,
    required this.onSecondary,
    required this.background,
    required this.onBackground,
    required this.surface,
    required this.onSurface,
    required this.error,
    required this.onError,
    this.outline = const Color(0xFF000000),
    this.surfaceVariant = const Color(0xFF808080),
    this.onSurfaceVariant = const Color(0xFF404040),
  });

  final Color primary;
  final Color onPrimary;
  final Color secondary;
  final Color onSecondary;
  final Color background;
  final Color onBackground;
  final Color surface;
  final Color onSurface;
  final Color error;
  final Color onError;
  final Color outline;
  final Color surfaceVariant;
  final Color onSurfaceVariant;

  @override
  List<Object?> get props => [
        primary,
        onPrimary,
        secondary,
        onSecondary,
        background,
        onBackground,
        surface,
        onSurface,
        error,
        onError,
        outline,
        surfaceVariant,
        onSurfaceVariant,
      ];
}

class KiratStyleSet extends Equatable {
  const KiratStyleSet({
    required this.rules,
  });

  final Map<String, KiratStyle> rules;

  @override
  List<Object?> get props => [rules];
}

class KiratStyle extends Equatable {
  const KiratStyle({
    this.backgroundColor,
    this.foregroundColor,
    this.borderRadius,
    this.padding,
    this.margin,
    this.textStyle,
    this.shadows,
    this.border,
    this.elevation,
    this.opacity = 1.0,
  });

  final Color? backgroundColor;
  final Color? foregroundColor;
  final BorderRadius? borderRadius;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final TextStyle? textStyle;
  final List<BoxShadow>? shadows;
  final Border? border;
  final double? elevation;
  final double opacity;

  @override
  List<Object?> get props => [
        backgroundColor,
        foregroundColor,
        borderRadius,
        padding,
        margin,
        textStyle,
        shadows,
        border,
        elevation,
        opacity,
      ];
}

// Theme query system
class ThemeQuery {
  final String elementName;
  final Map<String, String> attributes;
  final ThemeSelector selector;

  const ThemeQuery({
    required this.elementName,
    this.attributes = const {},
    this.selector = ThemeSelector.NONE,
  });
}

// Extension methods for theme querying
extension KiratThemeExtension on KiratTheme {
  KiratStyle query(String elementName, Map<String, String> attributes) {
    final styleSet = styles[elementName];
    if (styleSet == null) return const KiratStyle();

    // Apply style rules based on attributes and selectors
    return _mergeStyles(styleSet, attributes);
  }

  KiratStyle _mergeStyles(
      KiratStyleSet styleSet, Map<String, String> attributes) {
    KiratStyle result = const KiratStyle();

    // Apply base style
    final baseStyle = styleSet.rules['base'];
    if (baseStyle != null) {
      result = _combineStyles(result, baseStyle);
    }

    // Apply attribute-specific styles
    for (final entry in attributes.entries) {
      final attributeStyle = styleSet.rules['${entry.key}:${entry.value}'];
      if (attributeStyle != null) {
        result = _combineStyles(result, attributeStyle);
      }
    }

    return result;
  }

  KiratStyle _combineStyles(KiratStyle base, KiratStyle overlay) {
    return KiratStyle(
      backgroundColor: overlay.backgroundColor ?? base.backgroundColor,
      foregroundColor: overlay.foregroundColor ?? base.foregroundColor,
      borderRadius: overlay.borderRadius ?? base.borderRadius,
      padding: overlay.padding ?? base.padding,
      margin: overlay.margin ?? base.margin,
      textStyle: _combineTextStyles(base.textStyle, overlay.textStyle),
      shadows: overlay.shadows ?? base.shadows,
      border: overlay.border ?? base.border,
      elevation: overlay.elevation ?? base.elevation,
      opacity: overlay.opacity,
    );
  }

  TextStyle? _combineTextStyles(TextStyle? base, TextStyle? overlay) {
    if (overlay == null) return base;
    if (base == null) return overlay;
    return base.merge(overlay);
  }
}

// Predefined themes
class PredefinedThemes {
  static KiratTheme get kiratDay => KiratTheme(
        id: 'kirat_day',
        name: 'Kirat Day',
        isNightTheme: false,
        colorScheme: const KiratColorScheme(
          primary: Color(0xFF4CAF50),
          onPrimary: Color(0xFFFFFFFF),
          secondary: Color(0xFF2196F3),
          onSecondary: Color(0xFFFFFFFF),
          background: Color(0xFFF5F5F5),
          onBackground: Color(0xFF212121),
          surface: Color(0xFFFFFFFF),
          onSurface: Color(0xFF212121),
          error: Color(0xFFF44336),
          onError: Color(0xFFFFFFFF),
          outline: Color(0xFFE0E0E0),
          surfaceVariant: Color(0xFFF0F0F0),
          onSurfaceVariant: Color(0xFF424242),
        ),
        styles: {
          'keyboard': KiratStyleSet(rules: {
            'base': KiratStyle(
              backgroundColor: Color(0xFFF5F5F5),
              padding: EdgeInsets.all(4),
            ),
          }),
          'key': KiratStyleSet(rules: {
            'base': KiratStyle(
              backgroundColor: Color(0xFFFFFFFF),
              foregroundColor: Color(0xFF212121),
              borderRadius: BorderRadius.circular(8),
              padding: EdgeInsets.all(8),
              shadows: [
                BoxShadow(
                  color: Color(0x1A000000),
                  blurRadius: 2,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            'pressed': KiratStyle(
              backgroundColor: Color(0xFFE0E0E0),
              elevation: 4,
            ),
            'disabled': KiratStyle(
              backgroundColor: Color(0xFFF5F5F5),
              foregroundColor: Color(0xFF9E9E9E),
            ),
          }),
          'key_label': KiratStyleSet(rules: {
            'base': KiratStyle(
              textStyle: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFF212121),
              ),
            ),
          }),
          'smartbar': KiratStyleSet(rules: {
            'base': KiratStyle(
              backgroundColor: Color(0xFFFFFFFF),
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              border: Border(
                bottom: BorderSide(color: Color(0xFFE0E0E0), width: 1),
              ),
            ),
          }),
        },
      );

  static KiratTheme get kiratNight => KiratTheme(
        id: 'kirat_night',
        name: 'Kirat Night',
        isNightTheme: true,
        colorScheme: const KiratColorScheme(
          primary: Color(0xFF4CAF50),
          onPrimary: Color(0xFF000000),
          secondary: Color(0xFF2196F3),
          onSecondary: Color(0xFF000000),
          background: Color(0xFF121212),
          onBackground: Color(0xFFFFFFFF),
          surface: Color(0xFF1E1E1E),
          onSurface: Color(0xFFFFFFFF),
          error: Color(0xFFCF6679),
          onError: Color(0xFF000000),
          outline: Color(0xFF404040),
          surfaceVariant: Color(0xFF2C2C2C),
          onSurfaceVariant: Color(0xFFE0E0E0),
        ),
        styles: {
          'keyboard': KiratStyleSet(rules: {
            'base': KiratStyle(
              backgroundColor: Color(0xFF121212),
              padding: EdgeInsets.all(4),
            ),
          }),
          'key': KiratStyleSet(rules: {
            'base': KiratStyle(
              backgroundColor: Color(0xFF2C2C2C),
              foregroundColor: Color(0xFFFFFFFF),
              borderRadius: BorderRadius.circular(8),
              padding: EdgeInsets.all(8),
              shadows: [
                BoxShadow(
                  color: Color(0x1AFFFFFF),
                  blurRadius: 2,
                  offset: Offset(0, 1),
                ),
              ],
            ),
            'pressed': KiratStyle(
              backgroundColor: Color(0xFF404040),
              elevation: 4,
            ),
            'disabled': KiratStyle(
              backgroundColor: Color(0xFF1A1A1A),
              foregroundColor: Color(0xFF666666),
            ),
          }),
          'key_label': KiratStyleSet(rules: {
            'base': KiratStyle(
              textStyle: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w500,
                color: Color(0xFFFFFFFF),
              ),
            ),
          }),
          'smartbar': KiratStyleSet(rules: {
            'base': KiratStyle(
              backgroundColor: Color(0xFF1E1E1E),
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              border: Border(
                bottom: BorderSide(color: Color(0xFF404040), width: 1),
              ),
            ),
          }),
        },
      );

  static List<KiratTheme> get allThemes => [
        kiratDay,
        kiratNight,
      ];
}
