{"formatVersion": 1, "database": {"version": 3, "identityHash": "145ca5bf4bff8e98f71ebc70ab3b495b", "entities": [{"tableName": "clipboard_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `text` TEXT, `uri` TEXT, `creationTimestampMs` INTEGER NOT NULL, `isPinned` INTEGER NOT NULL, `mimeTypes` TEXT NOT NULL, `isSensitive` INTEGER NOT NULL DEFAULT 0, `isRemoteDevice` INTEGER NOT NULL DEFAULT 0)", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "text", "columnName": "text", "affinity": "TEXT", "notNull": false}, {"fieldPath": "uri", "columnName": "uri", "affinity": "TEXT", "notNull": false}, {"fieldPath": "creationTimestampMs", "columnName": "creationTimestampMs", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeTypes", "columnName": "mimeTypes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isSensitive", "columnName": "isSensitive", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isRemoteDevice", "columnName": "isRemoteDevice", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": true, "columnNames": ["_id"]}, "indices": [{"name": "index_clipboard_history__id", "unique": false, "columnNames": ["_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_clipboard_history__id` ON `${TABLE_NAME}` (`_id`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '145ca5bf4bff8e98f71ebc70ab3b495b')"]}}