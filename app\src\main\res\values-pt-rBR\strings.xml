<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="MissingTranslation">
    <string name="app_name">FlorisBoard</string>
    <string name="key__phone_pause" comment="Label for the Pause key in the telephone keyboard layout">Pausar</string>
    <string name="key__phone_wait" comment="Label for the Wait key in the telephone keyboard layout">Esperar</string>
    <string name="key_popup__threedots_alt" comment="Content description for the three-dots icon in a key popup">Ícone de três pontos. Se mostrado, indica que mais letras podem ser usadas ao pressionar e segurar a tecla.</string>
    <!-- One-handed strings -->
    <string name="one_handed__close_btn_content_description" comment="Content description for the one-handed close button">Fechar o modo uma mão.</string>
    <string name="one_handed__move_start_btn_content_description" comment="Content description for the one-handed move to left button">Mover teclado para a esquerda.</string>
    <string name="one_handed__move_end_btn_content_description" comment="Content description for the one-handed move to right button">Mover teclado para a direita.</string>
    <!-- Media strings -->
    <string name="settings__media__title">Emojis</string>
    <string name="media__tab__emojis" comment="Tab description for emojis in the media UI">Emojis</string>
    <string name="media__tab__emoticons" comment="Tab description for emoticons in the media UI">Emoticons</string>
    <string name="media__tab__kaomoji" comment="Tab description for kaomoji in the media UI">Kaomoji</string>
    <string name="prefs__media__emoji_preferred_skin_tone">Tom de pele do emoji preferido</string>
    <string name="prefs__media__emoji_preferred_hair_style">Estilo de cabelo do emoji preferido</string>
    <string name="prefs__media__emoji_history__title" comment="Preference group title">Histórico de emojis</string>
    <string name="prefs__media__emoji_history_enabled" comment="Preference title">Ativar histórico de emojis</string>
    <string name="prefs__media__emoji_history_enabled__summary" comment="Preference summary">Lembrar emojis usados para acesso rápido</string>
    <string name="prefs__media__emoji_history_pinned_update_strategy" comment="Preference title">Estratégia de atualização (Fixado)</string>
    <string name="prefs__media__emoji_history_recent_update_strategy" comment="Preference title">Estratégia de atualização (Recente)</string>
    <string name="prefs__media__emoji_history_max_size">Quantos itens manter no histórico</string>
    <string name="prefs__media__emoji_history_pinned_reset">Limpar os emojis fixados</string>
    <string name="prefs__media__emoji_history_reset">Limpar os emojis recentes</string>
    <string name="prefs__media__emoji_suggestion__title" comment="Preference group title">Sugestões de emojis</string>
    <string name="prefs__media__emoji_suggestion_enabled" comment="Preference title">Ativar sugestões de emoji</string>
    <string name="prefs__media__emoji_suggestion_enabled__summary" comment="Preference summary">Mostrar sugestões de emoji enquanto digita</string>
    <string name="prefs__media__emoji_suggestion_type" comment="Preference title">Como ativar</string>
    <string name="prefs__media__emoji_suggestion_update_history" comment="Preference title">Atualizar histórico de emojis</string>
    <string name="prefs__media__emoji_suggestion_update_history__summary" comment="Preference summary">Salva no histórico os emojis sugeridos que usar</string>
    <string name="prefs__media__emoji_suggestion_candidate_show_name" comment="Preference title">Mostrar o nome dos emojis</string>
    <string name="prefs__media__emoji_suggestion_candidate_show_name__summary" comment="Preference summary">Todas as sugestões de emojis virão com seus nomes, para você encontrar fácil</string>
    <string name="prefs__media__emoji_suggestion_candidate_max_count" comment="Preference title">Máximo de sugestões</string>
    <!-- Emoji strings -->
    <string name="emoji__category__smileys_emotion" comment="Emoji category name">Sorrisos e Emoções</string>
    <string name="emoji__category__people_body" comment="Emoji category name">Pessoas e Corpo</string>
    <string name="emoji__category__animals_nature" comment="Emoji category name">Animais e Natureza</string>
    <string name="emoji__category__food_drink" comment="Emoji category name">Comida e Bebida</string>
    <string name="emoji__category__travel_places" comment="Emoji category name">Viagem e Lugares</string>
    <string name="emoji__category__activities" comment="Emoji category name">Atividades</string>
    <string name="emoji__category__objects" comment="Emoji category name">Objetos</string>
    <string name="emoji__category__symbols" comment="Emoji category name">Símbolos</string>
    <string name="emoji__category__flags" comment="Emoji category name">Bandeiras</string>
    <string name="emoji__history__empty_message" comment="Message if the emoji history is empty">Nenhum emoji usado ainda... Que tal começar agora? Eles aparecerão aqui.</string>
    <string name="emoji__history__phone_locked_message" comment="Message to show if phone is locked">Quer ver seus emojis favoritos? É só desbloquear a telinha!</string>
    <string name="emoji__history__usage_tip" comment="Feature discoverability for actions of emoji history">Quer personalizar? Segure um emoji para fixá-lo nos favoritos ou removê-lo do histórico!</string>
    <string name="emoji__history__removal_success_message" comment="Toast message if user has used the delete action on an emoji in the emoji history">O emoji {emoji} foi removido do histórico</string>
    <string name="emoji__history__pinned">Fixados</string>
    <string name="emoji__history__recent">Recentes</string>
    <!-- Quick action strings -->
    <string name="quick_action__arrow_up" maxLength="12">Cima</string>
    <string name="quick_action__arrow_up__tooltip">Execute a seta para cima</string>
    <string name="quick_action__arrow_down" maxLength="12">Baixo</string>
    <string name="quick_action__arrow_down__tooltip">Execute a seta para baixo</string>
    <string name="quick_action__arrow_left" maxLength="12">Esquerda</string>
    <string name="quick_action__arrow_left__tooltip">Aplicar seta para a esquerda</string>
    <string name="quick_action__arrow_right" maxLength="12">Direita</string>
    <string name="quick_action__arrow_right__tooltip">Aplicar seta para a direita</string>
    <string name="quick_action__clipboard_clear_primary_clip" maxLength="12">Limpar</string>
    <string name="quick_action__clipboard_clear_primary_clip__tooltip">Aplica limpeza de área de transferência primária</string>
    <string name="quick_action__clipboard_copy" maxLength="12">Copiar</string>
    <string name="quick_action__clipboard_copy__tooltip">Aplica cópia para área de transferência</string>
    <string name="quick_action__clipboard_cut" maxLength="12">Cortar</string>
    <string name="quick_action__clipboard_cut__tooltip">Execute o corte da área de transferência</string>
    <string name="quick_action__clipboard_paste" maxLength="12">Colar</string>
    <string name="quick_action__clipboard_paste__tooltip">Executar colagem da área de transferência</string>
    <string name="quick_action__clipboard_select_all" maxLength="12">Selec. tudo</string>
    <string name="quick_action__clipboard_select_all__tooltip">Executar seleção de tudo da área de transferência</string>
    <string name="quick_action__ime_ui_mode_clipboard" maxLength="12">Área transf.</string>
    <string name="quick_action__ime_ui_mode_clipboard__tooltip">Abrir histórico da área de transferência</string>
    <string name="quick_action__ime_ui_mode_media" maxLength="12">Emoji</string>
    <string name="quick_action__ime_ui_mode_media__tooltip">Painel de emojis</string>
    <string name="quick_action__settings" maxLength="12">Ajustes</string>
    <string name="quick_action__settings__tooltip">Abrir configurações</string>
    <string name="quick_action__undo" maxLength="12">Desfazer</string>
    <string name="quick_action__undo__tooltip">Abrir a última entrada</string>
    <string name="quick_action__redo" maxLength="12">Refazer</string>
    <string name="quick_action__redo__tooltip">Refazer a última entrada</string>
    <string name="quick_action__toggle_actions_overflow" maxLength="12">Mais ações</string>
    <string name="quick_action__toggle_actions_overflow__tooltip">Mostrar ou ocultar ações adicionais</string>
    <string name="quick_action__toggle_incognito_mode" maxLength="12">Anônimo</string>
    <string name="quick_action__toggle_incognito_mode__tooltip">Ativar/desativar modo anônimo</string>
    <string name="quick_action__toggle_autocorrect" maxLength="12">Autocorreção</string>
    <string name="quick_action__toggle_autocorrect__tooltip">Alternar correção automática</string>
    <string name="quick_action__voice_input" maxLength="12">Etd. por voz</string>
    <string name="quick_action__voice_input__tooltip" comment="IME stands for Input Method Editor and is indirectly equivalent to 'keyboard'.">Abrir provedor de entrada de voz</string>
    <string name="quick_action__one_handed_mode" maxLength="12">Modo uma mão</string>
    <string name="quick_action__one_handed_mode__tooltip">Ativar/desativar modo uma mão</string>
    <string name="quick_action__drag_marker" maxLength="12" comment="This action is only used as a placeholder in the actions editor drag and drop screen and only visible in debug mode">Arrastar</string>
    <string name="quick_action__drag_marker__tooltip" comment="This action is only used as a placeholder in the actions editor drag and drop screen and only visible in debug mode">Posição atual do marcador de arrastar</string>
    <string name="quick_action__noop" maxLength="12" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">Nenhum</string>
    <string name="quick_action__noop__tooltip" comment="Noop=no operation; this action is only used as a placeholder in the actions editor drag and drop screen">Nenhuma Operação</string>
    <string name="quick_actions_overflow__customize_actions_button">Reordenar ações</string>
    <string name="quick_actions_editor__header">Personalizar ordem das ações</string>
    <string name="quick_actions_editor__subheader_sticky_action">Ação fixa ({n})</string>
    <string name="quick_actions_editor__subheader_dynamic_actions">Ações dinâmicas ({n})</string>
    <string name="quick_actions_editor__subheader_hidden_actions">Ações ocultas ({n})</string>
    <string name="select_subtype_panel__header">Selecione o subtipo</string>
    <!-- Incognito mode strings -->
    <string name="incognito_mode__toast_after_enabled">O modo anônimo agora está habilitado\n\n{app_name} não aprenderá novas palavras por meio de sua digitação enquanto esse mode estiver ativo</string>
    <string name="incognito_mode__toast_after_disabled">O modo anônimo agora está desabilitado por padrão</string>
    <!-- Settings UI strings -->
    <string name="settings__title" comment="Title of Settings">Configurações</string>
    <string name="settings__preview_keyboard" comment="Hint for try your setup box">Experimente sua configuração</string>
    <string name="settings__help" comment="General label for help buttons in Settings">Ajuda</string>
    <string name="settings__default" comment="General string which is used when a preference has the default value set">Padrão</string>
    <string name="settings__system_default" comment="General string which is used when a preference has the system default value set">Padrão do sistema</string>
    <string name="settings__home__title" comment="Title of the Home screen">Bem-vindo ao {app_name}</string>
    <string name="settings__home__ime_not_enabled" comment="Error message shown in Home fragment when FlorisBoard is not enabled in the system">O FlorisBoard não está ativado no sistema e, portanto, não estará disponível para ser selecionado no alternador de teclado. Clique aqui para resolver este problema.</string>
    <string name="settings__home__ime_not_selected" comment="Warning message shown in Home fragment when FlorisBoard is not selected as the default keyboard">O FlorisBoard não foi selecionado como o teclado padrão. Clique aqui para resolver este problema.</string>
    <string name="settings__localization__title" comment="Title of languages and Layout screen">Idiomas e Layouts</string>
    <string name="settings__localization__display_language_names_in__label" comment="Label of Display language names in preference">Exibir nomes de idiomas em</string>
    <string name="settings__localization__group_subtypes__label" comment="Label of subtypes group">Subtipos</string>
    <string name="settings__localization__subtype_add_title" comment="Title of subtype dialog when adding a new subtype">Adicionar formato de digitação</string>
    <string name="settings__localization__language_pack_title" comment="Title of the language pack manager screen for managing installed and custom language packs">Gerenciar pacotes de linguagem instalados</string>
    <string name="settings__localization__language_pack_summary" comment="Summary of preference item for adding a new language pack">Experimental: gerenciar extenções que adicionam suporte para linguagens específicas (por enquanto, entradas em chinês baseadas em formas)</string>
    <string name="settings__localization__subtype_edit_title" comment="Title of subtype dialog when editing an existing subtype">Editar formato de digitação</string>
    <string name="settings__localization__subtype_locale" comment="Label for locale dropdown in subtype dialog">Idioma principal</string>
    <string name="settings__localization__subtype_popup_mapping" comment="Label for popup mapping dropdown in subtype screen">Mapeamento de popup</string>
    <string name="settings__localization__subtype_characters_layout" comment="Label for layout dropdown in subtype dialog">Layout de caracteres</string>
    <string name="settings__localization__subtype_suggestion_provider" comment="Label for suggestion provider dropdown in subtype dialog">Ferramenta de sugestões</string>
    <string name="settings__localization__subtype_symbols_layout" comment="Label for layout dropdown in subtype dialog">Layout primário de símbolos</string>
    <string name="settings__localization__subtype_symbols2_layout" comment="Label for layout dropdown in subtype dialog">Layout secundário de símbolos</string>
    <string name="settings__localization__subtype_composer" comment="Label for composer dropdown in subtype dialog.">Compositor</string>
    <string name="settings__localization__subtype_currency_set" comment="Label for currency set dropdown in subtype dialog. 'set' is used as a noun here and can be compared to a group of elements (in this case currency symbols).">Conjunto de moedas</string>
    <string name="settings__localization__subtype_numeric_layout" comment="Label for layout dropdown in subtype dialog">Layout numérico</string>
    <string name="settings__localization__subtype_numeric_advanced_layout" comment="Label for layout dropdown in subtype dialog">Layout numérico (avançado)</string>
    <string name="settings__localization__subtype_numeric_row_layout" comment="Label for layout dropdown in subtype dialog">Layout da linha de números</string>
    <string name="settings__localization__subtype_phone_layout" comment="Label for layout dropdown in subtype dialog">Layout primário do telefone</string>
    <string name="settings__localization__subtype_phone2_layout" comment="Label for layout dropdown in subtype dialog">Layout secundário do telefone</string>
    <string name="settings__localization__subtype_select_locale" comment="Subtype select language title">Selecionar idioma</string>
    <string name="settings__localization__subtype_search_locale_placeholder" comment="Subtype search language placeholder">Pesquisar um idioma</string>
    <string name="settings__localization__subtype_search_locale_not_found" comment="Subtype search language not found">Não foi possível encontrar um idioma correspondente a \"{search_term}\".</string>
    <string name="settings__localization__subtype_select_placeholder" comment="Subtype dialog select value placeholder (&amp;#8210; is a hyphen character)">&#8210; selecionar &#8210;</string>
    <string name="settings__localization__subtype_summary" comment="Subtype summary">{characters_name} / {symbols_name} / {currency_set_name}</string>
    <string name="settings__localization__suggested_subtype_presets" comment="Suggested presets title">Predefinições de subtipo sugeridas</string>
    <string name="settings__localization__suggested_subtype_presets_none_found" comment="Suggested presets none found">Nenhuma predefinição sugerida disponível. Use o botão abaixo para ver todas as predefinições de subtipo.</string>
    <string name="settings__localization__subtype_presets" comment="Subtype presets dialog title">Predefinições de subtipo</string>
    <string name="settings__localization__subtype_presets_view_all" comment="View all presets button">Mostrar todos</string>
    <string name="settings__localization__subtype_no_subtypes_configured_warning" comment="Warning message that no subtype has been defined">Parece que você não configurou nenhum formato de digitação. Como alternativa, será utilizado o formato Inglês/QWERTY!</string>
    <string name="settings__localization__subtype_error_already_exists" comment="Error message shown in subtype dialog when a subtype to add already exists">Este formato de digitação já existe!</string>
    <string name="settings__localization__subtype_error_fields_no_value" comment="Error message shown in subtype editor if at least one field is set to '- select -' (means no value specified)">Pelo menos um campo não possui um valor selecionado. Escolha um valor para o(s) campo(s).</string>
    <string name="settings__localization__subtype_error_layout_not_installed" comment="Error message shown in subtype list when a layout is not installed, where %s will be replaced by the layout ID">{layout_id} (não instalado)</string>
    <string name="settings__localization__group_layouts__label" comment="Label of layouts group">Layouts</string>
    <string name="settings__localization__subtype_delete_confirmation_title" comment="Title of the subtype delete confirmation dialog">Confirmação de Exclusão</string>
    <string name="settings__localization__subtype_delete_confirmation_warning" comment="Warning message in the confirmation dialog to confirm the user's intent to delete">Tem certeza que deseja deletar esse subtipo?</string>
    <string name="settings__theme__title" comment="Title of the Theme screen">Tema</string>
    <string name="pref__theme__mode__label" comment="Label of the theme mode preference">Modo do tema</string>
    <string name="pref__theme__sunrise_time__label" comment="Label of the sunrise time preference">Hora do nascer do sol</string>
    <string name="pref__theme__sunset_time__label" comment="Label of the sunset time preference">Hora do pôr do sol</string>
    <string name="pref__theme__day" comment="Label of the day group (day means light theme)">Tema diurno</string>
    <string name="pref__theme__night" comment="Label of the night group (night means dark theme)">Tema noturno</string>
    <string name="settings__theme_manager__title_manage" comment="Title of the theme manager screen for managing installed and custom themes">Gerenciar temas instalados</string>
    <string name="pref__theme__source_assets" comment="Label for the theme source field">Assets do Aplicativo FlorisBoard</string>
    <string name="pref__theme__source_internal" comment="Label for the theme source field">Armazenamento Interno</string>
    <string name="pref__theme__source_external" comment="Label for the theme source field">Provedor Externo</string>
    <string name="settings__theme_manager__title_day" comment="Title of the theme manager screen for day theme selection">Selecionar tema diurno</string>
    <string name="settings__theme_manager__title_night" comment="Title of the theme manager screen for night theme selection">Selecionar tema noturno</string>
    <string name="settings__theme_editor__fine_tune__title">Editor de ajuste fino</string>
    <string name="settings__theme_editor__fine_tune__level">Nível de edição</string>
    <string name="settings__theme_editor__fine_tune__display_kbd_after_dialogs">Exibir teclado após diálogos</string>
    <string name="settings__theme_editor__add_rule">Adicionar regra</string>
    <string name="settings__theme_editor__edit_rule">Editar regra</string>
    <string name="settings__theme_editor__no_rules_defined">Esta folha de estilo não tem regras definidas. Adicione uma regra para começar a personalizar esta folha de estilo.</string>
    <string name="settings__theme_editor__rule_already_exists">Esta regra de folha de estilo já está definida.</string>
    <string name="settings__theme_editor__rule_codes">Códigos de teclas de destino</string>
    <string name="settings__theme_editor__rule_groups">Grupos</string>
    <string name="settings__theme_editor__rule_selectors">Seletores</string>
    <string name="settings__theme_editor__add_code">Adicionar código de tecla</string>
    <string name="settings__theme_editor__edit_code">Editar código de tecla</string>
    <string name="settings__theme_editor__no_codes_defined">Aplicar regra a todos os elementos de destino.</string>
    <string name="settings__theme_editor__code_already_exists">Este código de tecla já está definido.</string>
    <string name="settings__theme_editor__code_invalid">Este código de tecla não é válido. Certifique-se de que o código da tecla esteja dentro do intervalo de {c_min} até {c_max} para caracteres ou {i_min} até {i_max} para teclas especiais internas.</string>
    <string name="settings__theme_editor__code_help_text">Como alternativa, os links a seguir ajudarão você a encontrar o código de tecla correspondente:</string>
    <string name="settings__theme_editor__code_placeholder">Código</string>
    <string name="settings__theme_editor__code_recording_help_text">Para encontrar o código de uma tecla, use o botão ao lado do campo de entrada de código. Uma vez ativado, ele gravará a próxima tecla pressionada e inserirá o código no campo de entrada.</string>
    <string name="settings__theme_editor__code_recording_started">Gravação de código de tecla iniciada</string>
    <string name="settings__theme_editor__code_recording_stopped">Gravação de código de tecla parada</string>
    <string name="settings__theme_editor__code_recording_requires_default_ime_floris">{app_name} precisa ser o teclado padrão para gravar um código de tecla</string>
    <string name="settings__theme_editor__code_recording_placeholder">Gravando…</string>
    <string name="settings__theme_editor__add_property">Adicionar propriedade</string>
    <string name="settings__theme_editor__edit_property">Editar propriedade</string>
    <string name="settings__theme_editor__property_already_exists">Já existe uma propriedade com este nome na regra atual.</string>
    <string name="settings__theme_editor__property_name">Nome da propriedade</string>
    <string name="settings__theme_editor__property_value">Valor da propriedade</string>
    <string name="settings__theme_editor__property_value_shape_apply_for_all_corners">Aplicar para todos os cantos</string>
    <string name="settings__theme_editor__property_value_color_dialog_title">Editar sequência de cores</string>
    <string name="settings__theme_editor__component_meta_is_night_theme">É tema noturno</string>
    <string name="settings__theme_editor__component_meta_is_borderless">É sem bordas</string>
    <string name="settings__theme_editor__component_meta_stylesheet_path">Caminho da folha de estilo</string>
    <string name="snygg__rule_element__key">Tecla</string>
    <string name="snygg__rule_element__key_hint">Dica da tecla</string>
    <string name="snygg__rule_element__clipboard_header">Cabeçalho da área de transferência</string>
    <string name="snygg__rule_element__clipboard_item">Item da área de transferência</string>
    <string name="snygg__rule_element__clipboard_item_popup">Pop-up do item da área de transferência</string>
    <string name="snygg__rule_element__extracted_landscape_input_layout">Layout de entrada do modo paisagem</string>
    <string name="snygg__rule_element__extracted_landscape_input_field">Campo de entrada do modo paisagem</string>
    <string name="snygg__rule_element__extracted_landscape_input_action">Ação de entrada do modo paisagem</string>
    <string name="snygg__rule_element__glide_trail">Trilha de deslizamento</string>
    <string name="snygg__rule_element__incognito_mode_indicator">Indicador de modo anônimo</string>
    <string name="snygg__rule_element__one_handed_panel">Painel de uma mão</string>
    <string name="snygg__rule_element__smartbar">Barra inteligente</string>
    <string name="snygg__rule_element__smartbar_shared_actions_row">Linha de ações da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_shared_actions_toggle">Ativar/desativar linha de ações compartilhada da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_extended_actions_row">Linha de ações estendida da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_extended_actions_toggle">Ativar/desativar linha de ações estendida da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_action_key">Botão de ação da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_action_tile">Título de ação da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_actions_overflow">Overflow das ações da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_actions_overflow_customize_button">Botão para personalizar o overflow das ações da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_actions_editor">Editor de ações da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_actions_editor_header">Cabeçalho do editor de ações da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_actions_editor_subheader">Sub-cabeçalho do editor de ações da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_candidates_row">Linha candidata a barra inteligente</string>
    <string name="snygg__rule_element__smartbar_candidate_word">Palavra candidata da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_candidate_clip">Fixado candidato da barra inteligente</string>
    <string name="snygg__rule_element__smartbar_candidate_spacer">Espaçador candidato de barra inteligente</string>
    <string name="snygg__rule_selector__pressed">Pressionado</string>
    <string name="snygg__rule_selector__focus">Focado</string>
    <string name="snygg__rule_selector__disabled">Desativado</string>
    <string name="snygg__property_name__background">Fundo</string>
    <string name="snygg__property_name__foreground">Primeiro plano</string>
    <string name="snygg__property_name__border_color">Cor da borda</string>
    <string name="snygg__property_name__border_style">Estilo da borda</string>
    <string name="snygg__property_name__border_width">Largura da borda</string>
    <string name="snygg__property_name__font_family">Família da fonte</string>
    <string name="snygg__property_name__font_size">Tamanho da fonte</string>
    <string name="snygg__property_name__font_style">Estilo da fonte</string>
    <string name="snygg__property_name__font_weight">Espessura da fonte</string>
    <string name="snygg__property_name__shadow_elevation">Elevação da sombra</string>
    <string name="snygg__property_name__shape">Forma</string>
    <string name="snygg__property_name__var_primary">Cor primária</string>
    <string name="snygg__property_name__var_primary_variant">Cor primária (variante)</string>
    <string name="snygg__property_name__var_secondary">Cor secundária</string>
    <string name="snygg__property_name__var_secondary_variant">Cor secundária (variante)</string>
    <string name="snygg__property_name__var_background">Fundo comum</string>
    <string name="snygg__property_name__var_surface">Superfície comum</string>
    <string name="snygg__property_name__var_surface_variant">Superfície comum (variante)</string>
    <string name="snygg__property_name__var_on_primary">Primeiro plano do primário</string>
    <string name="snygg__property_name__var_on_secondary">Primeiro plano do secundário</string>
    <string name="snygg__property_name__var_on_background">Primeiro plano do fundo</string>
    <string name="snygg__property_name__var_on_surface">Primeiro plano da superfície</string>
    <string name="snygg__property_name__var_on_surface_variant">Primeiro plano da superfície (variante)</string>
    <string name="snygg__property_name__var_shape">Forma comum</string>
    <string name="snygg__property_name__var_shape_variant">Forma comum (variante)</string>
    <string name="snygg__property_value__explicit_inherit">Herdar</string>
    <string name="snygg__property_value__defined_var">Referência da Var</string>
    <string name="snygg__property_value__solid_color">Cor sólida</string>
    <string name="snygg__property_value__material_you_light_color">Cor do Material You (Clara)</string>
    <string name="snygg__property_value__material_you_dark_color">Cor do Material You (Escura)</string>
    <string name="snygg__property_value__rectangle_shape">Forma retangular</string>
    <string name="snygg__property_value__circle_shape">Forma de círculo</string>
    <string name="snygg__property_value__cut_corner_shape_dp">Corte em formato de canto (dp)</string>
    <string name="snygg__property_value__cut_corner_shape_percent">Corte em formato de canto (%)</string>
    <string name="snygg__property_value__rounded_corner_shape_dp">Forma de canto arredondado (dp)</string>
    <string name="snygg__property_value__rounded_corner_shape_percent">Forma de canto arredondado (%)</string>
    <string name="snygg__property_value__dp_size">Tamanho (dp)</string>
    <string name="snygg__property_value__sp_size">Tamanho (sp)</string>
    <string name="snygg__property_value__percentage_size">Tamanho (%)</string>
    <string name="settings__input_feedback__title" comment="Title of Input Feedback screen">Sons e Vibração</string>
    <string name="pref__input_feedback__group_audio__label" comment="Preference group title">Resposta de Áudio / Sons</string>
    <string name="pref__input_feedback__audio_enabled__label" comment="Preference title">Ativar resposta de áudio</string>
    <string name="pref__input_feedback__audio_enabled__summary_disabled" comment="Preference summary">Nunca tocar sons para eventos de entrada, independente das configurações do sistema</string>
    <string name="pref__input_feedback__audio_volume__label" comment="Preference title">Volume de som para eventos de entrada</string>
    <string name="pref__input_feedback__audio_feat_key_press__label" comment="Preference title">Sons ao pressionar a tecla</string>
    <string name="pref__input_feedback__audio_feat_key_long_press__label" comment="Preference title">Sons ao pressionar e segurar a tecla</string>
    <string name="pref__input_feedback__audio_feat_key_repeated_action__label" comment="Preference title">Sons de ação repetida</string>
    <string name="pref__input_feedback__audio_feat_gesture_swipe__label" comment="Preference title">Sons do gesto de deslizar</string>
    <string name="pref__input_feedback__audio_feat_gesture_moving_swipe__label" comment="Preference title">Sons de movimento de deslizar com gestos</string>
    <string name="pref__input_feedback__group_haptic__label" comment="Preference group title">Resposta Tátil / Vibração</string>
    <string name="pref__input_feedback__haptic_enabled__label" comment="Preference title">Ativar resposta tátil</string>
    <string name="pref__input_feedback__haptic_enabled__summary_disabled" comment="Preference summary">Nunca vibrar para eventos de entrada, independente das configurações do sistema</string>
    <string name="pref__input_feedback__haptic_vibration_mode__label" comment="Preference title">Modo de vibração</string>
    <string name="pref__input_feedback__haptic_vibration_duration__label" comment="Preference title">Duração da vibração</string>
    <string name="pref__input_feedback__haptic_vibration_strength__label" comment="Preference title">Intensidade da vibração</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_vibrator" comment="Preference summary">Essa funcionalidade exige um vibrador no hardware, que parece não estar presente nesse dispositivo</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_no_amplitude_ctrl" comment="Preference summary">Este recurso requer suporte de controle de amplitude de hardware, que está faltando em seu dispositivo</string>
    <string name="pref__input_feedback__haptic_vibration_strength__summary_unsupported_android_version" comment="Preference summary">Este recurso requer suporte de controle de amplitude, que só está disponível no Android 8.0 ou mais recente</string>
    <string name="pref__input_feedback__haptic_feat_key_press__label" comment="Preference title">Vibração ao pressionar tecla</string>
    <string name="pref__input_feedback__haptic_feat_key_long_press__label" comment="Preference title">Vibração ao pressionar e segurar tecla</string>
    <string name="pref__input_feedback__haptic_feat_key_repeated_action__label" comment="Preference title">Vibração de ação repetida da tecla</string>
    <string name="pref__input_feedback__haptic_feat_gesture_swipe__label" comment="Preference title">Vibração do gesto de deslizar</string>
    <string name="pref__input_feedback__haptic_feat_gesture_moving_swipe__label" comment="Preference title">Vibração do movimento de deslizar com gestos</string>
    <string name="pref__input_feedback__any_feat_key_press__summary" comment="Preference summary">p. ex. teclas, botões, abas de emoji</string>
    <string name="pref__input_feedback__any_feat_key_long_press__summary" comment="Preference summary">p. ex. menu popup</string>
    <string name="pref__input_feedback__any_feat_key_repeated_action__summary" comment="Preference summary">p. ex. tecla excluir</string>
    <string name="pref__input_feedback__any_feat_gesture_swipe__summary" comment="Preference summary">não implementado</string>
    <string name="pref__input_feedback__any_feat_gesture_moving_swipe__summary" comment="Preference summary">p. ex. deslizamento de controle do cursor</string>
    <string name="settings__keyboard__title" comment="Title of Keyboard preferences screen">Teclado</string>
    <string name="pref__keyboard__number_row__label" comment="Preference title">Linha de números</string>
    <string name="pref__keyboard__number_row__summary" comment="Preference summary">Mostra uma linha numérica acima do layout de caracteres</string>
    <string name="pref__keyboard__hinted_number_row_mode__label" comment="Preference title">Sugerir linha de números</string>
    <string name="pref__keyboard__hinted_symbols_mode__label" comment="Preference title">Sugerir símbolos</string>
    <string name="pref__keyboard__utility_key_enabled__label" comment="Preference title">Mostrar tecla de utilidade</string>
    <string name="pref__keyboard__utility_key_enabled__summary" comment="Preference summary">Mostra uma tecla de utilidade configurável ao lado da barra de espaço</string>
    <string name="pref__keyboard__utility_key_action__label" comment="Preference title">Ação da tecla de utilidade</string>
    <string name="pref__keyboard__space_bar_mode__label" comment="Preference title">Rótulo da barra de espaço</string>
    <string name="pref__keyboard__capitalization_behavior__label" comment="Preference title">Comportamento de capitalização</string>
    <string name="pref__keyboard__font_size_multiplier__label" comment="Preference title">Multiplicador do tamanho de fonte</string>
    <string name="pref__keyboard__group_layout__label" comment="Preference group title">Layout</string>
    <string name="pref__keyboard__one_handed_mode__label" comment="Preference title">Modo uma mão</string>
    <string name="pref__keyboard__one_handed_mode_scale_factor__label" comment="Preference title">Largura do teclado no modo uma mão</string>
    <string name="pref__keyboard__landscape_input_ui_mode__label" comment="Preference value">Entrada do modo paisagem em tela cheia</string>
    <string name="pref__keyboard__height_factor__label" comment="Preference title">Altura do teclado</string>
    <string name="pref__keyboard__key_spacing__label" comment="Preference title">Espaçamento de teclas</string>
    <string name="pref__keyboard__bottom_offset__label" comment="Preference title">Deslocamento inferior</string>
    <string name="pref__keyboard__group_keypress__label" comment="Preference group title">Pressionar tecla</string>
    <string name="pref__keyboard__popup_enabled__label" comment="Preference title">Visibilidade do popup</string>
    <string name="pref__keyboard__popup_enabled__summary" comment="Preference summary">Mostrar popup quando você pressionar uma tecla</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__label" comment="Preference title">Os acentos incluem popups de símbolos</string>
    <string name="pref__keyboard__merge_hint_popups_enabled__summary" comment="Preference summary">Adiciona popups de símbolos aos acentos do layout padrão</string>
    <string name="pref__keyboard__long_press_delay__label" comment="Preference title">Atraso ao pressionar e segurar uma tecla</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__label" comment="Preference title">Barra de espaço muda para caracteres</string>
    <string name="pref__keyboard__space_bar_switches_to_characters__summary" comment="Preference summary">Volta automaticamente para caracteres quando em símbolos ou numéricos</string>
    <string name="pref__keyboard__incognito_indicator__label" comment="Preference title">Indicador anônimo</string>
    <!-- Smartbar strings -->
    <string name="settings__smartbar__title" comment="Title of Smartbar screen">Barra inteligente</string>
    <string name="pref__smartbar__enabled__label" comment="Preference title">Ativar barra inteligente</string>
    <string name="pref__smartbar__enabled__summary" comment="Preference summary">Mostrar na parte superior do teclado</string>
    <string name="pref__smartbar__layout__label" comment="Preference title">Layout</string>
    <string name="pref__smartbar__group_layout_specific__label" comment="Preference group title">Opções específicas de layout</string>
    <string name="pref__smartbar__flip_toggles__label" comment="Preference title">Botões de alternância</string>
    <string name="pref__smartbar__flip_toggles__summary" comment="Preference summary">Inverte a linha de ação de alternância</string>
    <string name="pref__smartbar__shared_actions_auto_expand_collapse__label" comment="Preference title">Auto-expandir/recolher</string>
    <string name="pref__smartbar__shared_actions_auto_expand_collapse__summary" comment="Preference summary">Expande/recolhe automaticamente a linha de ação compartilhada com base no estado atual</string>
    <string name="pref__smartbar__extended_actions_placement__label" comment="Preference title">Posicionamento da linha de ação</string>
    <!-- Typing strings -->
    <string name="settings__typing__title" comment="Title of Typing screen">Digitação</string>
    <string name="pref__suggestion__title" comment="Preference group title">Sugestões</string>
    <string name="pref__suggestion__enabled__label" comment="Preference title">Exibir sugestões</string>
    <string name="pref__suggestion__enabled__summary" comment="Preference summary">Fornece sugestões enquanto você digita</string>
    <string name="pref__suggestion__display_mode__label" comment="Preference title">Modo de exibição de sugestões</string>
    <string name="pref__suggestion__block_possibly_offensive__label" comment="Preference title">Bloqueie palavras possivelmente ofensivas</string>
    <string name="pref__suggestion__api30_inline_suggestions_enabled__summary" comment="Preference summary">Mostrar sugestões em linha fornecidas pelos serviços de preenchimento automático</string>
    <string name="pref__suggestion__incognito_mode__label" comment="Label of Incognito mode preference in Typing">Modo anônimo</string>
    <string name="pref__correction__title" comment="Preference group title">Correções</string>
    <string name="pref__correction__auto_capitalization__label" comment="Preference title">Capitalização automática</string>
    <string name="pref__correction__auto_capitalization__summary" comment="Preference summary">Deixar palavras em maiúsculo com base no contexto de entrada atual</string>
    <string name="pref__correction__auto_space_punctuation__label" comment="Preference title">Espaço automático após pontuação</string>
    <string name="pref__correction__auto_space_punctuation__summary" comment="Preference summary">Inserir um espaço automaticamente após uma pontuação</string>
    <string name="pref__correction__remember_caps_lock_state__label" comment="Preference title">Lembrar do estado do caps lock</string>
    <string name="pref__correction__remember_caps_lock_state__summary" comment="Preference summary">O caps lock permanecerá ligado ao mudar para outro campo de texto</string>
    <string name="pref__correction__double_space_period__label" comment="Preference title">Dois espaços para ponto final</string>
    <string name="pref__correction__double_space_period__summary" comment="Preference summary">Tocar duas vezes na barra de espaço insere um ponto final seguido por um espaço</string>
    <string name="pref__spelling__title" comment="Preference group title">Ortografia</string>
    <string name="pref__spelling__active_spellchecker__summary_disabled">Desativado em todo o sistema. Nenhuma linha vermelha aparecerá nos campos de texto para palavras incorretas. Toque para mudar.</string>
    <string name="pref__spelling__active_spellchecker__summary_none">Nenhum serviço de corretor ortográfico de texto embutido definido. Toque para mudar.</string>
    <string name="pref__spelling__language_mode__label" comment="Label of Language mode pref">Idiomas</string>
    <string name="pref__spelling__use_contacts__label" comment="Label of Use contact list pref">Usar nomes de contatos</string>
    <string name="pref__spelling__use_contacts__summary" comment="Summary of Use contact list pref">Procure nomes da sua lista de contatos</string>
    <string name="pref__spelling__use_udm_entries__label" comment="Label of Use user dictionary entries pref">Usar entradas de dicionário do usuário</string>
    <string name="pref__spelling__use_udm_entries__summary" comment="Summary of Use user dictionary entries pref">Procurar entradas nos dicionários do usuário</string>
    <string name="settings__dictionary__title" comment="Title of the User dictionaries screen">Dicionários do usuário</string>
    <string name="pref__dictionary__enable_system_user_dictionary__label" comment="Preference title">Ativar dicionário de usuário do sistema</string>
    <string name="pref__dictionary__enable_system_user_dictionary__summary" comment="Preference summary">Sugerir palavras armazenadas no dicionário de usuário do sistema</string>
    <string name="pref__dictionary__manage_system_user_dictionary__label" comment="Preference title">Gerenciar dicionário de usuário do sistema</string>
    <string name="pref__dictionary__manage_system_user_dictionary__summary" comment="Preference summary">Adicionar, visualizar e remover entradas para o dicionário de usuário do sistema</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__label" comment="Preference title">Ativar dicionário interno do usuário</string>
    <string name="pref__dictionary__enable_internal_user_dictionary__summary" comment="Preference summary">Sugerir palavras armazenadas no dicionário interno do usuário</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__label" comment="Preference title">Gerenciar dicionário interno do usuário</string>
    <string name="pref__dictionary__manage_floris_user_dictionary__summary" comment="Preference summary">Adicionar, visualizar e remover entradas para o dicionário interno do usuário</string>
    <string name="settings__udm__title_floris" comment="Title of the User Dictionary Manager activity for internal">Dicionário Interno do Usuário</string>
    <string name="settings__udm__title_system" comment="Title of the User Dictionary Manager activity for system">Dicionário de Usuário do Sistema</string>
    <string name="settings__udm__no_words_in_dictionary" comment="String to show if no words are present in the dictionary">Este dicionário do usuário não contém palavras.</string>
    <string name="settings__udm__word_summary_freq" comment="Summary label for a word entry. The decimal placeholder inserts the frequency for the word it summarizes.">Frequência: {freq}</string>
    <string name="settings__udm__word_summary_freq_shortcut" comment="Summary label for a word entry. The first placeholder inserts the frequency for the word it summarizes, the second placeholder the shortcut defined.">Frequência: {freq} | Atalho: {shortcut}</string>
    <string name="settings__udm__all_languages" comment="Label of the For all languages entry in the language list">Para todas as línguas</string>
    <string name="settings__udm__open_system_manager_ui" comment="Label of the Open system manager UI menu option">Abrir interface do gerenciador do sistema</string>
    <string name="settings__udm__dictionary_import_success" comment="Message for dictionary import success">Dicionário de usuário importado com sucesso!</string>
    <string name="settings__udm__dictionary_export_success" comment="Message for dictionary export success">Dicionário de usuário exportado com sucesso!</string>
    <string name="settings__udm__dialog__title_add" comment="Label for the title (when in adding mode) in the user dictionary add/edit dialog">Adicionar palavra</string>
    <string name="settings__udm__dialog__title_edit" comment="Label for the title (when in editing mode) in the user dictionary add/edit dialog">Editar palavra</string>
    <string name="settings__udm__dialog__word_label" comment="Label for the word in the user dictionary add/edit dialog">Palavra</string>
    <string name="settings__udm__dialog__word_error_empty" comment="Error label for the word in the user dictionary add/edit dialog">Por favor, insira uma palavra</string>
    <string name="settings__udm__dialog__word_error_invalid" comment="Error label for the word in the user dictionary add/edit dialog">Por favor, insira uma palavra correspondente a {regex}</string>
    <string name="settings__udm__dialog__freq_label" comment="Label for the frequency in the user dictionary add/edit dialog. The two decimal placeholders are the minimum and maximum frequency, both inclusive.">Frequência (entre {f_min} e {f_max})</string>
    <string name="settings__udm__dialog__freq_error_empty" comment="Error label for the frequency in the user dictionary add/edit dialog">Por favor, insira um valor de frequência</string>
    <string name="settings__udm__dialog__freq_error_invalid" comment="Error label for the frequency in the user dictionary add/edit dialog">Por favor, insira um número válido dentro dos limites especificados</string>
    <string name="settings__udm__dialog__shortcut_label" comment="Label for the shortcut in the user dictionary add/edit dialog">Atalho (opcional)</string>
    <string name="settings__udm__dialog__shortcut_error_invalid" comment="Error label for the shortcut in the user dictionary add/edit dialog">Por favor, insira um atalho correspondente a {regex}</string>
    <string name="settings__udm__dialog__locale_label" comment="Label for the language code in the user dictionary add/edit dialog">Código de idioma (opcional)</string>
    <string name="settings__udm__dialog__locale_error_invalid" comment="Error label for the language code in the user dictionary add/edit dialog">Este código de idioma não está de acordo com a sintaxe esperada. O código deve ser apenas uma língua (como en), uma língua e um país (como en_US) ou uma língua, país e script (como en_US-script).</string>
    <string name="settings__gestures__title" comment="Title of Gestures screen">Digitação por Gestos</string>
    <string name="pref__glide__title" comment="Preference group title">Digitação deslizante</string>
    <string name="pref__glide__enabled__label" comment="Preference title">Ativar digitação deslizante</string>
    <string name="pref__glide__enabled__summary" comment="Preference summary">Digitar uma palavra deslizando o dedo através de suas letras</string>
    <string name="pref__glide__show_trail__label" comment="Preference title">Mostrar trilha de deslizamento</string>
    <string name="pref__glide__show_trail__summary" comment="Preference summary">Desaparecerá após cada palavra</string>
    <string name="pref__glide_trail_fade_duration">Tempo de desaparecimento da trilha de deslizamento</string>
    <string name="pref__glide_preview_refresh_delay">Atraso de atualização da pré-visualização</string>
    <string name="pref__glide__show_preview">Mostrar pré-visualização durante a digitação deslizante</string>
    <string name="pref__glide__immediate_backspace_deletes_word__label">Sempre excluir palavra</string>
    <string name="pref__glide__immediate_backspace_deletes_word__summary">Pressionar excluir logo após um deslize exclui a palavra inteira</string>
    <string name="pref__gestures__general_title" comment="Preference group title">Gestos gerais</string>
    <string name="pref__gestures__space_bar_title" comment="Preference group title">Gestos da barra de espaço</string>
    <string name="pref__gestures__other_title" comment="Preference group title">Outros gestos / Limites dos gestos</string>
    <string name="pref__gestures__swipe_up__label" comment="Preference title">Deslizar para cima</string>
    <string name="pref__gestures__swipe_down__label" comment="Preference title">Deslizar para baixo</string>
    <string name="pref__gestures__swipe_left__label" comment="Preference title">Deslizar para esquerda</string>
    <string name="pref__gestures__swipe_right__label" comment="Preference title">Deslizar para direita</string>
    <string name="pref__gestures__space_bar_swipe_up__label" comment="Preference title">Deslizar barra de espaço para cima</string>
    <string name="pref__gestures__space_bar_swipe_left__label" comment="Preference title">Deslizar barra de espaço para esquerda</string>
    <string name="pref__gestures__space_bar_swipe_right__label" comment="Preference title">Deslizar barra de espaço para direita</string>
    <string name="pref__gestures__space_bar_long_press__label" comment="Preference title">Pressionar e segurar barra de espaço</string>
    <string name="pref__gestures__delete_key_swipe_left__label" comment="Preference title">Deslizar tecla excluir para esquerda</string>
    <string name="pref__gestures__delete_key_long_press__label" comment="Preference title">Apertar e segurar tecla de excluir</string>
    <string name="pref__gestures__swipe_velocity_threshold__label" comment="Preference title">Limite de velocidade do deslizamento</string>
    <string name="pref__gestures__swipe_distance_threshold__label" comment="Preference title">Limite de distância do deslizamento</string>
    <string name="settings__other__title" comment="Title of Other settings">Outro</string>
    <string name="pref__other__settings_theme__label" comment="Label of Settings theme preference in Other">Configurações de tema</string>
    <string name="pref__other__settings_theme__auto_amoled" comment="Possible value of Settings theme preference in Other">Padrão do sistema (AMOLED)</string>
    <string name="pref__other__settings_theme__light" comment="Possible value of Settings theme preference in Other">Claro</string>
    <string name="pref__other__settings_theme__dark" comment="Possible value of Settings theme preference in Other">Escuro</string>
    <string name="pref__other__settings_theme__amoled_dark" comment="Possible value of Settings theme preference in Other">AMOLED Escuro</string>
    <string name="pref__other__settings_language__label" comment="Label of Settings language preference in Other">Idioma das configurações</string>
    <string name="pref__other__show_app_icon__label" comment="Label of Show app icon preference in Other">Mostrar o ícone do app no menu principal</string>
    <string name="pref__other__show_app_icon__summary_atleast_q" comment="Summary of Show app icon preference in Other for Android 10+">Sempre ativado no Android 10+ devido a restrições do sistema</string>
    <!-- About UI strings -->
    <string name="about__title" comment="Title of About activity">Sobre</string>
    <string name="about__app_icon_content_description" comment="Content description of app icon in About">Ícone do aplicativo FlorisBoard</string>
    <string name="about__view_licenses" comment="Label of View licenses button in About">Licenças de código aberto</string>
    <string name="about__view_privacy_policy" comment="Label of View privacy policy button in About">Política de privacidade</string>
    <string name="about__view_source_code" comment="Label of View source code button in About">Código-fonte</string>
    <string name="about__license__title" comment="Title of Open-source licenses dialog">Licenças de código aberto</string>
    <string name="about__version__title" comment="Preference title">Versão</string>
    <string name="about__version_copied__title" comment="Title of the toast for copying the version string">Versão copiada para a área de transferência</string>
    <string name="about__version_copied__error" comment="Title of the error toast for copying the version string">Algo deu errado: {error_message}</string>
    <string name="about__changelog__title" comment="Preference title">Registro de alterações</string>
    <string name="about__changelog__summary" comment="Preference summary">O que há de novo</string>
    <string name="about__repository__title" comment="Preference title">Repositório (GitHub)</string>
    <string name="about__repository__summary" comment="Preference summary">Código-fonte, discussões, problemas e informações</string>
    <string name="about__privacy_policy__title" comment="Preference title">Política de privacidade</string>
    <string name="about__privacy_policy__summary" comment="Preference summary">A política de privacidade para este projeto</string>
    <string name="about__project_license__title" comment="Preference title">Licença do projeto</string>
    <string name="about__project_license__summary" comment="Preference summary">FlorisBoard é licenciado sob {license_name}</string>
    <string name="about__project_license__error_license_text_failed" comment="Error text for license text loading failure">Erro: falha ao carregar o texto da licença.\n-&gt; Razão: {error_message}</string>
    <string name="about__project_license__error_reason_asset_manager_null" comment="Error text if asset manager is null">A referência do gerenciador de asset é nula</string>
    <string name="about__third_party_licenses__title" comment="Preference title">Licenças de terceiros</string>
    <string name="about__third_party_licenses__summary" comment="Preference summary">Licenças das bibliotecas de terceiros incluídas neste aplicativo</string>
    <!-- Setup UI strings -->
    <string name="setup__title" comment="Title of Setup">Bem-vindo!</string>
    <string name="setup__intro_message" comment="Short intro message welcoming new users">Obrigado por usar o {app_name}! Esta configuração rápida o orienta através das etapas necessárias para usar o {app_name} no seu dispositivo.</string>
    <string name="setup__footer__privacy_policy" comment="Privacy policy label for URL">Política de privacidade</string>
    <string name="setup__footer__repository" comment="Repository label for URL">Repositório</string>
    <string name="setup__enable_ime__title">Ativar {app_name}</string>
    <string name="setup__enable_ime__description">O Android requer que cada teclado personalizado seja ativado separadamente antes de você poder usá-lo. Abra as Configurações de <i>Idiomas e Entrada</i> do Sistema, lá ative o \"{app_name}\".</string>
    <string name="setup__enable_ime__open_settings_btn">Abrir Configurações do Sistema</string>
    <string name="setup__select_ime__title">Selecionar {app_name}</string>
    <string name="setup__select_ime__description">{app_name} agora está ativado em seu sistema. Para usá-lo ativamente, mude para o {app_name} selecionando-o na caixa de diálogo do seletor de entrada!</string>
    <string name="setup__select_ime__switch_keyboard_btn">Mudar Teclado</string>
    <string name="setup__grant_notification_permission__title">Permitir notificação com relatório de crash</string>
    <string name="setup__grant_notification_permission__description">A partir do Android 13, aplicativos precisam pedir permissão para
        enviar notificações. No Florisboard, isso só é usado para abrir a tela de relatório de crash caso ocorra um crash.
        Essa permissão pode ser alterada a qualquer momento nas configurações do sistema.
    </string>
    <string name="setup__grant_notification_permission__btn">Conceder permissão</string>
    <string name="setup__finish_up__title">Finalizar</string>
    <string name="setup__finish_up__description_p1">{app_name} agora está ativado no sistema e pronto para ser personalizado por você.</string>
    <string name="setup__finish_up__description_p2">Se você encontrar quaisquer problemas, bugs, travamentos ou apenas quiser fazer uma sugestão, verifique o repositório do projeto na tela sobre!</string>
    <string name="setup__finish_up__finish_btn">Começar a Personalizar</string>
    <!-- Back up & Restore -->
    <string name="backup_and_restore__title">Backup e Restauração</string>
    <string name="backup_and_restore__back_up__title">Fazer backup dos dados</string>
    <string name="backup_and_restore__back_up__summary">Gere um arquivo de backup de preferências e personalizações</string>
    <string name="backup_and_restore__back_up__destination">Selecionar destino do backup</string>
    <string name="backup_and_restore__back_up__destination_file_sys">Sistema de arquivos locais</string>
    <string name="backup_and_restore__back_up__destination_share_intent">Aplicativo de terceiros via menu de compartilhamento</string>
    <string name="backup_and_restore__back_up__files">Selecionar o que fazer backup</string>
    <string name="backup_and_restore__back_up__files_jetpref_datastore">Preferências</string>
    <string name="backup_and_restore__back_up__files_ime_keyboard">Extensões do teclado</string>
    <string name="backup_and_restore__back_up__files_ime_spelling">Extensões de ortografia / dicionários</string>
    <string name="backup_and_restore__back_up__files_ime_theme">Extensões de tema</string>
    <string name="backup_and_restore__back_up__files_clipboard_history">Histórico da área de transferência</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_text_items">Itens de texto</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_image_items">Imagens</string>
    <string name="backup_and_restore__back_up__files_clipboard_history__clipboard_video_items">Vídeos</string>
    <string name="backup_and_restore__back_up__success">Arquivo de backup exportado com sucesso!</string>
    <string name="backup_and_restore__back_up__failure">Falha ao exportar o arquivo de backup: {error_message}</string>
    <string name="backup_and_restore__restore__title">Restaurar dados</string>
    <string name="backup_and_restore__restore__summary">Restaurar preferências e personalizações de um arquivo de backup</string>
    <string name="backup_and_restore__restore__files">Selecionar que restaurar</string>
    <string name="backup_and_restore__restore__metadata">Arquivo de backup selecionado</string>
    <string name="backup_and_restore__restore__metadata_warn_different_version">Este arquivo de backup foi gerado em outra versão que não a atual, que geralmente é suportada. Esteja ciente de que pequenos problemas podem ocorrer ou algumas preferências podem não ser transferidas corretamente devido a diferenças de recursos.</string>
    <string name="backup_and_restore__restore__metadata_warn_different_vendor">Esse arquivo de backup foi gerado em um aplicativo de terceiros, que geralmente não é compatível. Podem ocorrer perdas de dados, restaure por sua conta e risco!</string>
    <string name="backup_and_restore__restore__metadata_error_invalid_metadata">Este arquivo de backup contém metadados inválidos. Ou ele foi corrompido ou mal modificado. Não é possível restaurar a partir deste arquivo, selecione outro.</string>
    <string name="backup_and_restore__restore__metadata_error_nothing_to_restore">Este arquivo de backup não contém nenhum arquivo para restaurar, selecione outro.</string>
    <string name="backup_and_restore__restore__mode">Modo de restauração</string>
    <string name="backup_and_restore__restore__mode_merge">Mesclar com os dados atuais</string>
    <string name="backup_and_restore__restore__mode_erase_and_overwrite">Apagar e substituir os dados atuais</string>
    <string name="backup_and_restore__restore__success">Dados restaurados com sucesso!</string>
    <string name="backup_and_restore__restore__failure">Falha ao restaurar dados: {error_message}</string>
    <!-- Crash Dialog strings -->
    <string name="crash_dialog__title" comment="Title of crash dialog">Relatório de erro do FlorisBoard</string>
    <string name="crash_dialog__description" comment="Description of crash dialog">Desculpe pelo inconveniente, mas o FlorisBoard travou devido a um erro inesperado.</string>
    <string name="crash_dialog__report_instructions" comment="Issue tracker report instructions for the crash dialog. The %s placeholder is the name of the crash report template and always in English/LTR.">Se você deseja relatar esse erro, primeiro verifique o rastreador de problemas no GitHub para conferir se o seu travamento ainda não foi relatado.\nSe ainda não, copie o registro de travamento gerado e abra um novo Issue. Use o modelo \"%s\" e preencha a descrição, as etapas para reproduzir e cole o registro de travamento gerado no final. Isso ajuda a tornar o FlorisBoard melhor e mais estável para todos. Obrigado!</string>
    <string name="crash_dialog__copy_to_clipboard" comment="Label of Copy to clipboard button in crash dialog">Copiar para a área de transferência do sistema</string>
    <string name="crash_dialog__copy_to_clipboard_success" comment="Label of Copy to clipboard success message in crash dialog">Copiado para a área de transferência do sistema</string>
    <string name="crash_dialog__copy_to_clipboard_failure" comment="Label of Copy to clipboard failure message in crash dialog">Não é possível copiar para a área de transferência do sistema: Instância do gerenciador de área de transferência não encontrada</string>
    <string name="crash_dialog__open_issue_tracker" comment="Label of Open issue tracker button in crash dialog">Abrir issue tracker (github.com)</string>
    <string name="crash_dialog__close" comment="Label of Close button in crash dialog">Fechar</string>
    <string name="crash_notification_channel__title" comment="Title of crash notification channel">Relatórios de erros do FlorisBoard</string>
    <string name="crash_once_notification__title" comment="Title of the notification for a single crash">FlorisBoard parou de funcionar…</string>
    <string name="crash_once_notification__body" comment="Body of the notification for a single crash">Toque para ver os detalhes do erro</string>
    <string name="crash_multiple_notification__title" comment="Title of the notification for consecutive crashes">FlorisBoard parece parar de funcionar repetidamente…</string>
    <string name="crash_multiple_notification__body" comment="Body of the notification for consecutive crashes">Recuando para o teclado anterior para parar o loop infinito de travamento. Toque para ver os detalhes do erro</string>
    <!-- Clipboard strings -->
    <string name="clipboard__header_title">Área de transferência</string>
    <string name="clipboard__disabled__title">O histórico da área de transferência está atualmente desativado</string>
    <string name="clipboard__disabled__message">O histórico da área de transferência do {app_name} permite que você armazene e acesse rapidamente textos e imagens copiados, com a capacidade de fixar itens, configurar a limpeza automática e definir um limite máximo de itens.</string>
    <string name="clipboard__disabled__enable_button">Ativar histórico da área de transferência</string>
    <string name="clipboard__empty__title">Sua área de transferência está vazia</string>
    <string name="clipboard__empty__message">Depois de copiar clipes de texto ou imagens, eles aparecerão aqui.</string>
    <string name="clipboard__locked__title">Sua área de transferência está bloqueada</string>
    <string name="clipboard__locked__message">Para acessar o histórico da área de transferência, primeiro desbloqueie o dispositivo.</string>
    <string name="clipboard__group_pinned">Fixado</string>
    <string name="clipboard__group_recent">Recente</string>
    <string name="clipboard__group_other">Outro</string>
    <string name="clipboard__item_description_email">Email</string>
    <string name="clipboard__item_description_url">URL</string>
    <string name="clipboard__item_description_phone">Telefone</string>
    <string name="clip__clear_history">Limpar histórico</string>
    <string name="clip__unpin_item">Desafixar item</string>
    <string name="clip__pin_item">Fixar item</string>
    <string name="clip__delete_item">Excluir</string>
    <string name="clip__paste_item">Colar</string>
    <string name="clip__back_to_text_input">Voltar para a entrada de texto</string>
    <string name="clip__cant_paste">Este aplicativo não permite colar esse conteúdo.</string>
    <string name="clipboard__cleared_primary_clip">Clipe primário excluído</string>
    <string name="clipboard__cleared_history">Histórico excluído</string>
    <string name="clipboard__cleared_full_history">Todo o histórico excluído</string>
    <string name="clipboard__confirm_clear_history__message">Tem certeza de que deseja limpar o histórico da área de transferência?</string>
    <string name="settings__clipboard__title">Área de transferência</string>
    <string name="pref__clipboard__use_internal_clipboard__label">Usar área de transferência interna</string>
    <string name="pref__clipboard__use_internal_clipboard__summary">Use uma área de transferência interna em vez da área de transferência do sistema</string>
    <string name="pref__clipboard__sync_from_system_clipboard__label">Sincronizar a partir da área de transferência do sistema</string>
    <string name="pref__clipboard__sync_from_system_clipboard__summary">Atualizações da área de transferência do sistema também atualizam a área de transferência do Floris</string>
    <string name="pref__clipboard__sync_to_system_clipboard__label">Sincronizar com a área de transferência do sistema</string>
    <string name="pref__clipboard__sync_to_system_clipboard__summary">Atualizações da área de transferência do Floris também atualizam a área de transferência do sistema</string>
    <string name="pref__clipboard__group_clipboard_suggestion__label">Sugestões da área de transf.</string>
    <string name="pref__clipboard__suggestion_enabled__label" comment="Preference title">Sugestões da área de transf.</string>
    <string name="pref__clipboard__suggestion_enabled__summary" comment="Preference summary">Sugerir conteúdos da área de transferência</string>
    <string name="pref__clipboard__suggestion_timeout__label" comment="Preference title; Translators: This should form a sentence together with `pref__clipboard__suggestion_timeout__summary` and is the first part">Limitar as sugestões da área de transf. em</string>
    <string name="pref__clipboard__suggestion_timeout__summary" comment="Preference summary; Translators: This should form a sentence together with `pref__clipboard__suggestion_timeout__label` and is the second part">Itens copiados nos últimos {v} s</string>
    <string name="pref__clipboard__group_clipboard_history__label">Histórico da área de transferência</string>
    <string name="pref__clipboard__enable_clipboard_history__label">Ativar histórico da área de transferência</string>
    <string name="pref__clipboard__enable_clipboard_history__summary">Retenha itens da área de transferência para acesso rápido</string>
    <string name="pref__clipboard__clean_up_old__label">Limpar itens antigos</string>
    <string name="pref__clipboard__clean_up_after__label">Limpar itens antigos depois</string>
    <string name="pref__clipboard__auto_clean_sensitive__label">Auto excluir itens sensíveis</string>
    <string name="pref__clipboard__auto_clean_sensitive_after__label">Limpar a área de transferência após o uso automaticamente</string>
    <string name="pref__clipboard__limit_history_size__label">Limitar o tamanho do histórico</string>
    <string name="pref__clipboard__max_history_size__label">Tamanho máximo do histórico</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__label">Limpar o clipe primário afeta o histórico</string>
    <string name="pref__clipboard__clear_primary_clip_deletes_last_item__summary">Limpar o clipe primário também exclui a última entrada do histórico</string>
    <string name="send_to_clipboard__unknown_error">Ocorreu um erro desconhecido. Por favor, tente novamente!</string>
    <string name="send_to_clipboard__type_not_supported_error">Esse tipo de mídia não é suportado.</string>
    <string name="send_to_clipboard__android_version_to_old_error">Sua versão do Android é muito antiga para essa funcionalidade.    </string>
    <string name="send_to_clipboard__description__copied_image_to_clipboard">Imagem abaixo copiada para área de transferência.</string>
    <!-- Devtools strings -->
    <string name="devtools__title" comment="Title of Devtools screen. Translators: treat this string as 'Developer tools' for translation, except a similar short term is available for your language.">Ferramentas de desenvolvedor</string>
    <string name="devtools__enabled__label" comment="Label of Enable developer tools in Devtools">Ativar ferramentas de desenvolvedor</string>
    <string name="devtools__enabled__summary" comment="Summary of Enable developer tools in Devtools">Ferramentas projetadas especificamente para depuração e solução de problemas</string>
    <string name="devtools__show_primary_clip__label" comment="Label of Show primary clip in Devtools">Mostrar clipe primário</string>
    <string name="devtools__show_primary_clip__summary" comment="Summary of Show primary clip in Devtools">Sobrepõe o clipe primário atual da área de transferência</string>
    <string name="devtools__show_input_state_overlay__label" comment="Label of Show input cache overlay in Devtools">Mostrar sobreposição de estado de entrada</string>
    <string name="devtools__show_input_state_overlay__summary" comment="Summary of Show input cache overlay in Devtools">Sobrepõe o estado de entrada atual para depuração</string>
    <string name="devtools__show_spelling_overlay__label" comment="Label of Show spelling overlay in Devtools">Mostrar sobreposição de ortografia</string>
    <string name="devtools__show_spelling_overlay__summary" comment="Summary of Show spelling overlay in Devtools">Sobrepõe os resultados ortográficos atuais para depuração</string>
    <string name="devtools__show_key_touch_boundaries__label" comment="Label of Show key touch boundaries in Devtools">Mostrar limites de toque da tecla</string>
    <string name="devtools__show_key_touch_boundaries__summary" comment="Summary of Show key touch boundaries in Devtools">Contornar os limites de toque da tecla em vermelho</string>
    <string name="devtools__show_drag_and_drop_helpers__label" comment="Label of Show drag and drop helpers in Devtools">Exibir facilitadores de arrastar&amp;</string>
    <string name="devtools__show_drag_and_drop_helpers__summary" comment="Summary of Show drag and drop helpers in Devtools">Renderizar guias que estariam invisíveis em telas de arrastar&amp;soltar para depuração</string>
    <string name="devtools__clear_udm_internal_database__label" comment="Label of Clear internal user dictionary database in Devtools">Limpar banco de dados de dicionário interno do usuário</string>
    <string name="devtools__clear_udm_internal_database__summary" comment="Summary of Clear internal user dictionary database in Devtools">Limpa todas as palavras da tabela do banco de dados do dicionário</string>
    <string name="devtools__reset_flag__label" comment="Label of Reset flag preferences in Devtools">Redefinir flag \"{flag_name}\"</string>
    <string name="devtools__reset_flag_is_ime_set_up__summary" comment="Summary of Reset is IME set up flag in Devtools">Ação de depuração para mostrar novamente a tela de configuração</string>
    <string name="devtools__test_crash_report__label" comment="Label of Test Crash Report in Devtools">Tela de relatório de falha de teste</string>
    <string name="devtools__test_crash_report__summary" comment="Summary of Test Crash Report in Devtools">Ação de depuração para produzir uma falha propositalmente</string>
    <string name="devtools__group_android__title" comment="Title of Android group in Devtools">Ferramentas do sistema Android</string>
    <string name="devtools__android_settings_global__title" comment="Title of Android settings (global) screen">Configurações globais do Android</string>
    <string name="devtools__android_settings_secure__title" comment="Title of Android settings (secure) screen">Configurações seguras do Android</string>
    <string name="devtools__android_settings_system__title" comment="Title of Android settings (system) screen">Configurações do sistema Android</string>
    <string name="devtools__android_locales__title" comment="Title of Android locales screen">Localização do sistema</string>
    <string name="devtools__debuglog__title">Log de depuração</string>
    <string name="devtools__debuglog__copied_to_clipboard">Logs de depuração copiados à área de transf.</string>
    <string name="devtools__debuglog__copy_log">Copiar log</string>
    <string name="devtools__debuglog__copy_for_github">Copiar log (formatação do GitHub)</string>
    <string name="devtools__debuglog__loading">Carregando…</string>
    <!-- Extension strings -->
    <string name="ext__home__title">Complementos &amp; Extensões</string>
    <string name="ext__list__ext_theme">Extensões de tema</string>
    <string name="ext__list__ext_keyboard">Extensões de teclado</string>
    <string name="ext__list__ext_languagepack">Extensões de pacotes de idiomas</string>
    <string name="ext__meta__authors">Autores</string>
    <string name="ext__meta__components">Componentes agrupados</string>
    <string name="ext__meta__components_theme">Temas agrupados</string>
    <string name="ext__meta__components_language_pack">Pacotes de linguagem agrupados</string>
    <string name="ext__meta__components_none_found">Este arquivo de extensão não contém nenhum componente agrupado.</string>
    <string name="ext__meta__description">Descrição</string>
    <string name="ext__meta__homepage">Página inicial</string>
    <string name="ext__meta__id">ID</string>
    <string name="ext__meta__issue_tracker">Rastreador de problemas</string>
    <string name="ext__meta__keywords">Palavras-chave</string>
    <string name="ext__meta__label">Etiqueta</string>
    <string name="ext__meta__license">Licença</string>
    <string name="ext__meta__maintainers">Mantedores</string>
    <string name="ext__meta__maintainers_by">Por: {maintainers}</string>
    <string name="ext__meta__title">Título</string>
    <string name="ext__meta__version">Versão</string>
    <string name="ext__error__not_found_title">Extensão não encontrada</string>
    <string name="ext__error__not_found_description">Nenhuma extensão com o ID \"{id}\" pode ser encontrada.</string>
    <string name="ext__editor__title_create_any">Criar extensão</string>
    <string name="ext__editor__title_create_keyboard">Criar extensão de teclado</string>
    <string name="ext__editor__title_create_theme">Criar extensão de tema</string>
    <string name="ext__editor__title_edit_any">Editar extensão</string>
    <string name="ext__editor__title_edit_keyboard">Editar extensão de teclado</string>
    <string name="ext__editor__title_edit_theme">Editar extensão de tema</string>
    <string name="ext__editor__metadata__title">Gerenciar metadados</string>
    <string name="ext__editor__metadata__title_invalid">Metadados inválidos</string>
    <string name="ext__editor__metadata__message_invalid">Os metadados para esta extensão não são válidos, verifique o editor de metadados para obter detalhes!</string>
    <string name="ext__editor__dependencies__title">Gerenciar dependências</string>
    <string name="ext__editor__files__title">Gerenciar arquivos compactados</string>
    <string name="ext__editor__create_component__title">Criar componente</string>
    <string name="ext__editor__create_component__title_theme">Criar tema</string>
    <string name="ext__editor__create_component__from_empty">Vazio</string>
    <string name="ext__editor__create_component__from_existing">De existente</string>
    <string name="ext__editor__create_component__from_empty_warning">Criar e configurar um componente vazio pode ser difícil se você for novo no {app_name} ou se você não estiver familiarizado com os detalhes. Considere copiar um componente existente e modificá-lo ao seu gosto, se for o caso.</string>
    <string name="ext__editor__edit_component__title">Editar componente</string>
    <string name="ext__editor__edit_component__title_theme">Editar componente de tema</string>
    <string name="ext__export__success">Extensão exportada com sucesso!</string>
    <string name="ext__export__failure">Falha ao exportar extensão: {error_message}</string>
    <string name="ext__import__success">Extensão importada com sucesso!</string>
    <string name="ext__import__failure">Falha ao importar extensão: {error_message}</string>
    <string name="ext__import__ext_any" comment="Title of Importer screen for import of any supported FlorisBoard extension">Importar extensão</string>
    <string name="ext__import__ext_keyboard" comment="Title of Importer screen for keyboard extension import">Importar extensão de teclado</string>
    <string name="ext__import__ext_theme" comment="Title of Importer screen for theme extension import">Importar extensão de tema</string>
    <string name="ext__import__ext_languagepack" comment="Title of Importer screen for language pack extension import">Importar extensão de pacote de linguagem</string>
    <string name="ext__import__file_skip" comment="Label when a file cannot be imported in the current context. The actual reason string is in a separate text view below this string.">O arquivo não pode ser importado. Razão:</string>
    <string name="ext__import__file_skip_unsupported" comment="Reason string when file is unsupported">Tipo de arquivo não compatível ou não reconhecido.</string>
    <string name="ext__import__file_skip_ext_core" comment="Reason string when ext has core extension ID">Não é possível substituir ou atualizar os pacotes de extensão padrão fornecidos com os ativos principais do aplicativo. Considere atualizar o próprio aplicativo se você pretende usar uma versão mais recente de um pacote de extensão principal.</string>
    <string name="ext__import__file_skip_ext_corrupted" comment="Reason string when file seems to be ext but is corrupted">O arquivo parece ser um arquivo de extensão, mas a análise dos dados do arquivo falhou. O arquivo está corrompido ou este arquivo não é uma extensão.</string>
    <string name="ext__import__file_skip_ext_incorrect_type" comment="Reason string when file is of incorrect extension serial type">Esperava-se um arquivo de extensão do tipo serial \"{expected_serial_type}\" mas, é um arquivo de serial \"{actual_serial_type}\".</string>
    <string name="ext__import__file_skip_ext_not_supported" comment="Reason string when file is loaded in incorrect context">Era esperado um arquivo de mídia (imagem, áudio, font, etc...) porém foi encontrado um arquivo de extensão.</string>
    <string name="ext__import__file_skip_media_not_supported" comment="Reason string when file is loaded in incorrect context">Era esperado um arquivo de extensão, porém foi encontrado um arquivo de mídia (image, audio, font, etc...).</string>
    <string name="ext__import__error_unexpected_exception" comment="Label when an error occurred during import. The error message will be appended below this text view">Ocorreu um erro inesperado durante a importação. Os seguintes detalhes foram fornecidos:</string>
    <string name="ext__validation__enter_version">Insira uma versão</string>
    <string name="ext__validation__enter_title">Insira um título</string>
    <string name="ext__validation__enter_license">Insira um código de licença</string>
    <string name="ext__validation__error_author">Insira, ao menos, um autor válido</string>
    <string name="ext__validation__enter_valid_number">Insira um número válido</string>
    <string name="ext__validation__enter_positive_number">Insira um número positivo (&gt;=0)</string>
    <string name="ext__validation__enter_number_between_0_100">Insira um número positivo entre 0 e 100</string>
    <string name="ext__validation__hint_value_above_50_percent">Qualquer valor acima de 50% se comportará como se você definisse 50%, considere diminuir o tamanho da porcentagem</string>
    <string name="ext__update_box__internet_permission_hint">Como este aplicativo não tem permissão de Internet, as atualizações das extensões instaladas devem ser verificadas manualmente.</string>
    <string name="ext__update_box__search_for_updates">Procurar por atualizações</string>
    <string name="ext__addon_management_box__managing_placeholder">Gerenciando {extensions}</string>
    <string name="ext__addon_management_box__addon_manager_info">Todas as tarefas relacionadas à importação, exportação, criação, personalização e remoção de extensões podem ser realizadas pela central de gerenciamento de complementos.</string>
    <string name="ext__addon_management_box__go_to_page">Ir para {ext_home_title}</string>
    <string name="ext__home__info">Você pode baixar e instalar extensões na FlorisBoard Addons Store ou importar qualquer arquivo de extensão que tenha baixado da interne.</string>
    <string name="ext__home__manage_extensions">Gerenciar extensões instaladas</string>
    <string name="ext__list__view_details">Ver detalhes</string>
    <string name="ext__check_updates__title">Verifique se há atualizações</string>
    <!-- Action strings -->
    <string name="action__add">Adicionar</string>
    <string name="action__apply">Aplicar</string>
    <string name="action__back_up">Fazer backup</string>
    <string name="action__cancel">Cancelar</string>
    <string name="action__create">Criar</string>
    <string name="action__default">Padrão</string>
    <string name="action__delete">Deletar</string>
    <string name="action__delete_confirm_title">Confirmar exclusão</string>
    <string name="action__delete_confirm_message">Você tem certeza que deseja excluir \"{name}\"? Esta ação não pode ser desfeita uma vez executada.</string>
    <string name="action__reset_confirm_message">Tem certeza que deseja redefinir \"{name}\"? Esta ação não pode ser desfeita.</string>
    <string name="action__discard">Descartar</string>
    <string name="action__discard_confirm_title">Alterações não salvas</string>
    <string name="action__discard_confirm_message">Tem certeza de que deseja descartar as alterações não salvas? Esta ação não pode ser desfeita uma vez executada.</string>
    <string name="action__edit">Editar</string>
    <string name="action__export">Exportar</string>
    <string name="action__import">Importar</string>
    <string name="action__no">Não</string>
    <string name="action__ok">Ok</string>
    <string name="action__restore">Restaurar</string>
    <string name="action__save">Salvar</string>
    <string name="action__select">Selecionar</string>
    <string name="action__select_dir">Selecionar diretório</string>
    <string name="action__select_dirs">Selecionar diretórios</string>
    <string name="action__select_file">Selecionar arquivo</string>
    <string name="action__select_files">Selecionar Arquivos</string>
    <string name="action__yes">Sim</string>
    <!-- Error strings (generic) -->
    <string name="error__title">Erro</string>
    <string name="error__details">Detalhes</string>
    <string name="error__invalid">Inválido</string>
    <string name="error__snackbar_message">Algo deu errado</string>
    <string name="error__snackbar_message_template">Algo deu errado: {error_message}</string>
    <!-- General strings -->
    <string name="general__example_given" comment="This string is used where an example will be inserted in the {example} block. When translating, use the abbreviation used in your language for 'example given'.">p. ex. {example}</string>
    <string name="general__no_browser_app_found_for_url">Nenhum aplicativo de navegador encontrado para lidar com a URL {url}</string>
    <string name="general__select_dropdown_value_placeholder">&#45; selecionar &#45;</string>
    <string name="general__unlimited">Ilimitado</string>
    <!-- Screen orientation strings -->
    <string name="screen_orientation__portrait">Retrato</string>
    <string name="screen_orientation__landscape">Paisagem</string>
    <string name="screen_orientation__vertical">Vertical</string>
    <string name="screen_orientation__horizontal">Horizontal</string>
    <!-- State strings -->
    <string name="state__disabled">Desativado</string>
    <string name="state__enabled">Ativado</string>
    <string name="state__no_dir_selected">Nenhum diretório selecionado</string>
    <string name="state__no_dirs_selected">Nenhum diretório selecionado</string>
    <string name="state__no_file_selected">Nenhum arquivo selecionado</string>
    <string name="state__no_files_selected">Nenhum arquivo selecionado</string>
    <!-- Enum label and description strings -->
    <string name="enum__candidates_display_mode__classic" comment="Enum value label">Clássico (3 colunas)</string>
    <string name="enum__candidates_display_mode__dynamic" comment="Enum value label">Largura dinâmica</string>
    <string name="enum__candidates_display_mode__dynamic_scrollable" comment="Enum value label">Largura dinâmica e rolável</string>
    <string name="enum__capitalization_behavior__capslock_by_double_tap" comment="Enum value label">Habilitar Capslock ao pressionar shift duas vezes</string>
    <string name="enum__capitalization_behavior__capslock_by_cycle" comment="Enum value label">Trocar para o próximo passo de capitalização cada vez que o a tecla shift for pressionada</string>
    <string name="enum__display_kbd_after_dialogs__always" comment="Enum value label">Sempre mostrar</string>
    <string name="enum__display_kbd_after_dialogs__always__description" comment="Enum value description">Sempre mostrar o teclado após fechar qualquer caixa de diálogo do editor</string>
    <string name="enum__display_kbd_after_dialogs__never" comment="Enum value label">Nunca mostrar</string>
    <string name="enum__display_kbd_after_dialogs__never__description" comment="Enum value description">Nunca mostrar o teclado após fechar qualquer caixa de diálogo do editor</string>
    <string name="enum__display_kbd_after_dialogs__remember" comment="Enum value label">Lembrar o último estado</string>
    <string name="enum__display_kbd_after_dialogs__remember__description" comment="Enum value description">Mostrar o teclado apenas após fechar qualquer caixa de diálogo do editor se estiver visível anteriormente</string>
    <string name="enum__display_language_names_in__system_locale" comment="Enum value label">Localidade do sistema</string>
    <string name="enum__display_language_names_in__system_locale__description" comment="Enum value description">Os nomes dos idiomas na interface de usuário do aplicativo e do teclado são exibidos na localidade definida para todo o dispositivo</string>
    <string name="enum__display_language_names_in__native_locale" comment="Enum value label">Localidade nativa</string>
    <string name="enum__display_language_names_in__native_locale__description" comment="Enum value description">Os nomes dos idiomas na interface de usuário do aplicativo e do teclado são exibidos na localidade referida por si só</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_prepend" comment="Enum value label">Organizar automaticamente (no início)</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_prepend__description" comment="Enum value description">Reorganiza os emojis automaticamente conforme você os usa. Os mais recentes aparecem primeiro.</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_append" comment="Enum value label">Organizar automaticamente (no final)</string>
    <string name="enum__emoji_history_update_strategy__auto_sort_append__description" comment="Enum value description">Reorganiza os emojis automaticamente conforme você os usa. Os mais recentes aparecem no final.</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_prepend" comment="Enum value label">Organizar manualmente (no início)</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_prepend__description" comment="Enum value description">Não reorganiza os emojis conforme o uso. Os mais recentes aparecem no início.</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_append" comment="Enum value label">Organizar manualmente (no final)</string>
    <string name="enum__emoji_history_update_strategy__manual_sort_append__description" comment="Enum value description">Não reorganiza os emojis conforme o uso. Os mais recentes aparecem no final.</string>
    <string name="enum__emoji_skin_tone__default" comment="Enum value label">{emoji} Tom de pele padrão</string>
    <string name="enum__emoji_skin_tone__light_skin_tone" comment="Enum value label">{emoji} Pele clara</string>
    <string name="enum__emoji_skin_tone__medium_light_skin_tone" comment="Enum value label">{emoji} Pele clara média</string>
    <string name="enum__emoji_skin_tone__medium_skin_tone" comment="Enum value label">{emoji} Tom de pele médio</string>
    <string name="enum__emoji_skin_tone__medium_dark_skin_tone" comment="Enum value label">{emoji} Pele escura média</string>
    <string name="enum__emoji_skin_tone__dark_skin_tone" comment="Enum value label">{emoji} Pele escura</string>
    <string name="enum__emoji_hair_style__default" comment="Enum value label">{emoji} Cabelo padrão</string>
    <string name="enum__emoji_hair_style__red_hair" comment="Enum value label">{emoji} Cabelo vermelho</string>
    <string name="enum__emoji_hair_style__curly_hair" comment="Enum value label">{emoji} Cabelo encaracolado</string>
    <string name="enum__emoji_hair_style__white_hair" comment="Enum value label">{emoji} Cabelo branco</string>
    <string name="enum__emoji_hair_style__bald" comment="Enum value label">{emoji} Careca</string>
    <string name="enum__extended_actions_placement__above_candidates" comment="Enum value label">Candidatos acima</string>
    <string name="enum__extended_actions_placement__overlay_app_ui" comment="Enum value label">Sobreposição do app</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly" comment="Enum value label">Usar modo de vibração direta</string>
    <string name="enum__haptic_vibration_mode__use_vibrator_directly__description" comment="Enum value description">{app_name} interage diretamente com o hardware de vibração. Isso permite maior controle sobre a duração e intensidade de uma vibração, porém pode não ser tão suave e otimizada quanto usar a resposta tátil</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface" comment="Enum value label">Usar resposta tátil</string>
    <string name="enum__haptic_vibration_mode__use_haptic_feedback_interface__description" comment="Enum value description">{app_name} utiliza a resposta tátil para acionar uma sequência pré-definida de vibrações ao pressionar as teclas. Isso pode funcionar bem em alguns dispositivos, mas falhar completamente ou desempenhar de forma precária em outros</string>
    <string name="enum__key_hint_mode__accent_priority" comment="Enum value label">O acento é priorizado</string>
    <string name="enum__key_hint_mode__accent_priority__description" comment="Enum value description">O caractere inicial selecionado após um toque longo é sempre o acento principal ou a sugestão de símbolo se nenhum acento principal estiver disponível</string>
    <string name="enum__key_hint_mode__hint_priority" comment="Enum value label">A sugestão é priorizada</string>
    <string name="enum__key_hint_mode__hint_priority__description" comment="Enum value description">O caractere inicial selecionado após um toque longo é sempre a sugestão de símbolo ou o acento principal se nenhuma sugestão de símbolo estiver disponível</string>
    <string name="enum__key_hint_mode__smart_priority" comment="Enum value label">Priorização inteligente</string>
    <string name="enum__key_hint_mode__smart_priority__description" comment="Enum value description">O caractere inicial selecionado após um toque longo é decidido dinamicamente como o acento principal ou a sugestão de símbolo, com base no idioma e layout atuais</string>
    <string name="enum__incognito_display_mode__display_behind_keyboard" comment="Enum value label">Exibir o indicador de navegação anônima atrás do teclado</string>
    <string name="enum__incognito_mode__dynamic_on_off" comment="Enum value label">Ligar/desligar dinamicamente</string>
    <string name="enum__incognito_mode__dynamic_on_off__description" comment="Enum value description">Opção recomendada. O modo de navegação anônima será ativado ou desativado dinamicamente por meio das opções passadas pelo aplicativo de destino, ou alternando-o manualmente por meio da ação rápida de navegação anônima na Smartbar.</string>
    <string name="enum__input_feedback_activation_mode__audio_respect_system_settings" comment="Enum value label">Reproduz sons dinamicamente para eventos de entrada, dependendo das configurações do sistema</string>
    <string name="enum__input_feedback_activation_mode__audio_ignore_system_settings" comment="Enum value label">Sempre reproduza sons para eventos de entrada, independentemente das configurações do sistema</string>
    <string name="enum__input_feedback_activation_mode__haptic_respect_system_settings" comment="Enum value label">Vibrar dinamicamente para eventos de entrada, dependendo das configurações do sistema</string>
    <string name="enum__input_feedback_activation_mode__haptic_ignore_system_settings" comment="Enum value label">Sempre vibrar para eventos de entrada, independente das configurações do sistema</string>
    <string name="enum__input_shift_state__unshifted" comment="Enum value label">Inalterado</string>
    <string name="enum__input_shift_state__shifted_manual" comment="Enum value label">Alterado (manual)</string>
    <string name="enum__input_shift_state__shifted_automatic" comment="Enum value label">Alterado (automático)</string>
    <string name="enum__input_shift_state__caps_lock" comment="Enum value label">Caps lock</string>
    <string name="enum__landscape_input_ui_mode__never_show" comment="Enum value label">Nunca mostrar</string>
    <string name="enum__landscape_input_ui_mode__always_show" comment="Enum value label">Sempre mostrar</string>
    <string name="enum__landscape_input_ui_mode__dynamically_show" comment="Enum value label">Mostrar dinamicamente</string>
    <string name="enum__one_handed_mode__start" comment="Enum value label">Modo canhoto</string>
    <string name="enum__one_handed_mode__end" comment="Enum value label">Modo destro</string>
    <string name="enum__shape_corner__top_start" comment="Enum value label">Início da parte superior</string>
    <string name="enum__shape_corner__top_end" comment="Enum value label">Final da parte superior</string>
    <string name="enum__shape_corner__bottom_end" comment="Enum value label">Final da parte inferior</string>
    <string name="enum__shape_corner__bottom_start" comment="Enum value label">Início da parte inferior</string>
    <string name="enum__smartbar_layout__suggestions_only" comment="Enum value label">Apenas sugestões</string>
    <string name="enum__smartbar_layout__suggestions_only__description" comment="Enum value description">Exibir apenas a linha em questão, sem nenhuma linha/botão de ação ou ação fixa</string>
    <string name="enum__smartbar_layout__actions_only" comment="Enum value label">Apenas ações</string>
    <string name="enum__smartbar_layout__actions_only__description" comment="Enum value description">Exibe apenas a linha de ações, sem a linha em questão ou uma ação fixa explícita</string>
    <string name="enum__smartbar_layout__suggestions_action_shared" comment="Enum value label">Sugestões &amp; Ações compartilhadas</string>
    <string name="enum__smartbar_layout__suggestions_action_shared__description" comment="Enum value description">Candidato e linhas de ação compartilhados, com ação fixa</string>
    <string name="enum__smartbar_layout__suggestions_actions_extended" comment="Enum value label">Sugestões &amp; Ações estendidas</string>
    <string name="enum__smartbar_layout__suggestions_actions_extended__description" comment="Enum value description">Linha em questão estática e linha de ações ativáveis, com ação fixa</string>
    <string name="enum__snygg_level__basic" comment="Enum value label">Básico</string>
    <string name="enum__snygg_level__basic__description" comment="Enum value description">Apenas propriedades de cor são mostradas, propriedades e regras são traduzidas.</string>
    <string name="enum__snygg_level__advanced" comment="Enum value label">Avançado</string>
    <string name="enum__snygg_level__advanced__description" comment="Enum value description">Todas as propriedades são mostradas, propriedades e regras são traduzidas.</string>
    <string name="enum__snygg_level__developer" comment="Enum value label">Desenvolvedor</string>
    <string name="enum__snygg_level__developer__description" comment="Enum value description">Todas as propriedades são mostradas, as propriedades e regras são exibidas conforme escrito no próprio arquivo de folha de estilo.</string>
    <string name="enum__space_bar_mode__nothing" comment="Enum value label">Sem rótulo</string>
    <string name="enum__space_bar_mode__current_language" comment="Enum value label">Idioma atual</string>
    <string name="enum__space_bar_mode__space_bar_key" comment="Enum value label">␣</string>
    <string name="enum__spelling_language_mode__use_system_languages" comment="Enum value label">Usar idiomas do sistema</string>
    <string name="enum__spelling_language_mode__use_keyboard_subtypes" comment="Enum value label">Usar subtipos de teclado</string>
    <string name="enum__swipe_action__no_action" comment="Enum value label">Nenhuma ação</string>
    <string name="enum__swipe_action__cycle_to_previous_keyboard_mode" comment="Enum value label">Alternar para o modo de teclado anterior</string>
    <string name="enum__swipe_action__cycle_to_next_keyboard_mode" comment="Enum value label">Alternar para o próximo modo de teclado</string>
    <string name="enum__swipe_action__delete_character" comment="Enum value label">Excluir caractere antes do cursor</string>
    <string name="enum__swipe_action__delete_characters_precisely" comment="Enum value label">Excluir caracteres com precisão</string>
    <string name="enum__swipe_action__delete_word" comment="Enum value label">Excluir palavra antes do cursor</string>
    <string name="enum__swipe_action__delete_words_precisely" comment="Enum value label">Excluir palavras com precisão</string>
    <string name="enum__swipe_action__hide_keyboard" comment="Enum value label">Esconder teclado</string>
    <string name="enum__swipe_action__insert_space" comment="Enum value label">Inserir espaço</string>
    <string name="enum__swipe_action__move_cursor_up" comment="Enum value label">Mover cursor para cima</string>
    <string name="enum__swipe_action__move_cursor_down" comment="Enum value label">Mover cursor para baixo</string>
    <string name="enum__swipe_action__move_cursor_left" comment="Enum value label">Mover cursor para esquerda</string>
    <string name="enum__swipe_action__move_cursor_right" comment="Enum value label">Mover cursor para direita</string>
    <string name="enum__swipe_action__move_cursor_start_of_line" comment="Enum value label">Mover cursor para o início da linha</string>
    <string name="enum__swipe_action__move_cursor_end_of_line" comment="Enum value label">Mover cursor para o final da linha</string>
    <string name="enum__swipe_action__move_cursor_start_of_page" comment="Enum value label">Mover cursor para o início da página</string>
    <string name="enum__swipe_action__move_cursor_end_of_page" comment="Enum value label">Mover cursor para o final da página</string>
    <string name="enum__swipe_action__switch_to_clipboard_context" comment="Enum value label">Abrir o gerenciador/histórico da área de transferência</string>
    <string name="enum__swipe_action__shift" comment="Enum value label">Shift</string>
    <string name="enum__swipe_action__redo" comment="Enum value label">Refazer</string>
    <string name="enum__swipe_action__undo" comment="Enum value label">Desfazer</string>
    <string name="enum__swipe_action__select_characters_precisely" comment="Enum value label">Selecionar os caracteres com precisão</string>
    <string name="enum__swipe_action__select_words_precisely" comment="Enum value label">Selecionar as palavras com precisão</string>
    <string name="enum__swipe_action__show_input_method_picker" comment="Enum value label">Mostrar alternador de teclado</string>
    <string name="enum__swipe_action__switch_to_prev_keyboard" comment="Enum value label">Mudar para teclado anterior</string>
    <string name="enum__swipe_action__switch_to_prev_subtype" comment="Enum value label">Mudar para formato de digitação anterior</string>
    <string name="enum__swipe_action__switch_to_next_subtype" comment="Enum value label">Mudar para próximo formato de digitação</string>
    <string name="enum__swipe_action__toggle_smartbar_visibility" comment="Enum value label">Alternar a visibilidade da barra inteligente</string>
    <string name="enum__theme_mode__always_day" comment="Enum value label">Sempre dia</string>
    <string name="enum__theme_mode__always_night" comment="Enum value label">Sempre noite</string>
    <string name="enum__theme_mode__follow_system" comment="Enum value label">Seguir o sistema</string>
    <string name="enum__theme_mode__follow_time" comment="Enum value label">Seguir a hora</string>
    <string name="enum__utility_key_action__switch_to_emojis" comment="Enum value label">Mudar para emojis</string>
    <string name="enum__utility_key_action__switch_language" comment="Enum value label">Trocar idioma</string>
    <string name="enum__utility_key_action__switch_keyboard_app" comment="Enum value label">Trocar aplicativo de teclado</string>
    <string name="enum__utility_key_action__dynamic_switch_language_emojis" comment="Enum value label">Dinâmico: Mudar para emojis / Trocar idioma</string>
    <!-- Unit strings (symbols) -->
    <!-- Unit strings (written words) -->
    <plurals name="unit__hours__written">
        <item quantity="one">{v} hora</item>
        <item quantity="other">{v} horas</item>
    </plurals>
    <plurals name="unit__minutes__written">
        <item quantity="one">{v} minuto</item>
        <item quantity="other">{v} minutos</item>
    </plurals>
    <plurals name="unit__seconds__written">
        <item quantity="one">{v} segundo</item>
        <item quantity="other">{v} segundos</item>
    </plurals>
    <plurals name="unit__items__written">
        <item quantity="one">{v} item</item>
        <item quantity="other">{v} itens</item>
    </plurals>
    <plurals name="unit__characters__written">
        <item quantity="one">{v} caractere</item>
        <item quantity="other">{v} caracteres</item>
    </plurals>
    <plurals name="unit__candidates__written">
        <item quantity="one">{v} candidato</item>
        <item quantity="other">{v} candidatos</item>
    </plurals>
</resources>
