import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/managers/clipboard_manager.dart';
import '../../../core/constants/app_constants.dart';

class ClipboardInputLayout extends StatelessWidget {
  const ClipboardInputLayout({
    super.key,
    required this.onClipboardItemSelected,
    required this.onModeChange,
  });

  final Function(String) onClipboardItemSelected;
  final Function(KeyboardMode) onModeChange;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ClipboardManager, ClipboardState>(
      builder: (context, state) {
        return Column(
          children: [
            // Header with actions
            Container(
              height: 40,
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                border: Border(
                  bottom: BorderSide(
                    color: Theme.of(context).colorScheme.outline,
                    width: 1,
                  ),
                ),
              ),
              child: Row(
                children: [
                  // Back button
                  IconButton(
                    onPressed: () => onModeChange(KeyboardMode.characters),
                    icon: const Icon(Icons.keyboard, size: 20),
                    tooltip: 'Back to keyboard',
                  ),

                  // Title
                  Expanded(
                    child: Text(
                      'Clipboard History',
                      style: Theme.of(context).textTheme.titleMedium,
                      textAlign: TextAlign.center,
                    ),
                  ),

                  // Clear all button
                  IconButton(
                    onPressed: state.history.isNotEmpty
                        ? () => _showClearConfirmation(context)
                        : null,
                    icon: const Icon(Icons.clear_all, size: 20),
                    tooltip: 'Clear all',
                  ),
                ],
              ),
            ),

            // Clipboard items
            Expanded(
              child: _buildClipboardList(context, state),
            ),
          ],
        );
      },
    );
  }

  Widget _buildClipboardList(BuildContext context, ClipboardState state) {
    if (state.history.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.content_paste_outlined,
              size: 48,
              color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
            ),
            const SizedBox(height: 16),
            Text(
              'No clipboard history',
              style: TextStyle(
                fontSize: 16,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.5),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Copy text to see it here',
              style: TextStyle(
                fontSize: 14,
                color: Theme.of(context).colorScheme.onSurface.withOpacity(0.3),
              ),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(8),
      itemCount: state.history.length,
      itemBuilder: (context, index) {
        final item = state.history[index];
        return _buildClipboardItem(context, item, index);
      },
    );
  }

  Widget _buildClipboardItem(
      BuildContext context, ClipboardItem item, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Theme.of(context).colorScheme.primary,
          child: Text(
            '${index + 1}',
            style: TextStyle(
              color: Theme.of(context).colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        title: Text(
          item.text,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
          style: const TextStyle(fontSize: 14),
        ),
        subtitle: Text(
          _formatTimestamp(item.timestamp),
          style: TextStyle(
            fontSize: 12,
            color:
                Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.6),
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (item.isPinned)
              Icon(
                Icons.push_pin,
                size: 16,
                color: Theme.of(context).colorScheme.primary,
              ),
            PopupMenuButton<String>(
              icon: const Icon(Icons.more_vert, size: 20),
              onSelected: (action) => _handleItemAction(context, item, action),
              itemBuilder: (context) => [
                PopupMenuItem(
                  value: 'pin',
                  child: Row(
                    children: [
                      Icon(item.isPinned
                          ? Icons.push_pin_outlined
                          : Icons.push_pin),
                      const SizedBox(width: 8),
                      Text(item.isPinned ? 'Unpin' : 'Pin'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'delete',
                  child: Row(
                    children: [
                      Icon(Icons.delete),
                      SizedBox(width: 8),
                      Text('Delete'),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
        onTap: () {
          onClipboardItemSelected(item.text);
          context.read<ClipboardManager>().pasteItem(item);
        },
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }

  void _handleItemAction(
      BuildContext context, ClipboardItem item, String action) {
    final clipboardManager = context.read<ClipboardManager>();

    switch (action) {
      case 'pin':
        if (item.isPinned) {
          clipboardManager.unpinItem(item.id);
        } else {
          clipboardManager.pinItem(item.id);
        }
        break;
      case 'delete':
        clipboardManager.removeItem(item.id);
        break;
    }
  }

  void _showClearConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Clipboard History'),
        content: const Text(
            'Are you sure you want to clear all clipboard history? This action cannot be undone.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              context.read<ClipboardManager>().clearHistory();
              Navigator.pop(context);
            },
            child: const Text('Clear All'),
          ),
        ],
      ),
    );
  }
}
