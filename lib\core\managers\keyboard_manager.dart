import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../constants/app_constants.dart';
import '../constants/key_codes.dart';
import '../../data/models/key_data.dart';
import '../../data/models/keyboard_layout.dart';
import '../services/input_event_dispatcher.dart';
import '../services/ime_service.dart';

class KeyboardManager extends Cubit<KeyboardState> {
  KeyboardManager() : super(const KeyboardState.initial());

  final InputEventDispatcher _inputEventDispatcher = InputEventDispatcher();
  final IMEService _imeService = IMEService();

  void initialize() {
    // Load default layout
    final defaultLayout = PredefinedLayouts.qwertyCharacters;
    emit(state.copyWith(currentLayout: defaultLayout));
  }

  void switchMode(KeyboardMode mode) {
    KeyboardLayout? newLayout;

    switch (mode) {
      case KeyboardMode.characters:
        newLayout = PredefinedLayouts.qwertyCharacters;
        break;
      case KeyboardMode.symbols:
        newLayout = PredefinedLayouts.qwertySymbols;
        break;
      case KeyboardMode.numeric:
        newLayout = PredefinedLayouts.numericLayout;
        break;
      default:
        newLayout = state.currentLayout;
    }

    emit(state.copyWith(
      currentMode: mode,
      currentLayout: newLayout,
    ));
  }

  void handleKeyPress(KeyData keyData) {
    _inputEventDispatcher.sendDownUp(keyData);
    _updateKeyboardState(keyData);
  }

  void handleKeyDown(KeyData keyData) {
    _inputEventDispatcher.sendDown(keyData);
  }

  void handleKeyUp(KeyData keyData) {
    _inputEventDispatcher.sendUp(keyData);
  }

  void handleKeyLongPress(KeyData keyData) {
    switch (keyData.code) {
      case KeyCode.SPACE:
        // Show language switcher or cursor movement
        break;
      case KeyCode.DELETE:
        // Start word deletion
        _imeService.deleteWord();
        break;
      default:
        // Show popup alternatives if available
        if (keyData.popup != null) {
          _showKeyPopup(keyData);
        }
        break;
    }
  }

  void handleSwipeGesture(KeyData keyData, SwipeDirection direction) {
    // Handle swipe gestures based on preferences
    // This would be implemented based on user preferences
    switch (direction) {
      case SwipeDirection.up:
        if (keyData.code == KeyCode.SPACE) {
          // Move cursor up or show language picker
        }
        break;
      case SwipeDirection.down:
        // Hide keyboard or other action
        break;
      case SwipeDirection.left:
        if (keyData.code == KeyCode.SPACE) {
          // Move cursor left
          IMEService.moveCursor(-1);
        }
        break;
      case SwipeDirection.right:
        if (keyData.code == KeyCode.SPACE) {
          // Move cursor right
          IMEService.moveCursor(1);
        }
        break;
    }
  }

  void _updateKeyboardState(KeyData keyData) {
    switch (keyData.code) {
      case KeyCode.SHIFT:
        _handleShiftKey();
        break;
      case KeyCode.CAPS_LOCK:
        _handleCapsLock();
        break;
      case KeyCode.MODE_CHANGE:
        _handleModeChange();
        break;
      case KeyCode.EMOJI_SWITCH:
        switchMode(KeyboardMode.media);
        break;
      case KeyCode.CLIPBOARD_SWITCH:
        switchMode(KeyboardMode.clipboard);
        break;
      default:
        // No special handling needed for other keys
        break;
    }
  }

  void _handleShiftKey() {
    InputShiftState newShiftState;

    switch (state.inputShiftState) {
      case InputShiftState.unshifted:
        newShiftState = InputShiftState.shiftedManual;
        break;
      case InputShiftState.shiftedManual:
        newShiftState = InputShiftState.capsLock;
        break;
      case InputShiftState.shiftedAutomatic:
        newShiftState = InputShiftState.unshifted;
        break;
      case InputShiftState.capsLock:
        newShiftState = InputShiftState.unshifted;
        break;
    }

    emit(state.copyWith(inputShiftState: newShiftState));
  }

  void _handleCapsLock() {
    final newShiftState = state.inputShiftState == InputShiftState.capsLock
        ? InputShiftState.unshifted
        : InputShiftState.capsLock;

    emit(state.copyWith(inputShiftState: newShiftState));
  }

  void _handleModeChange() {
    KeyboardMode newMode;

    switch (state.currentMode) {
      case KeyboardMode.characters:
        newMode = KeyboardMode.symbols;
        break;
      case KeyboardMode.symbols:
        newMode = KeyboardMode.characters;
        break;
      default:
        newMode = KeyboardMode.characters;
    }

    switchMode(newMode);
  }

  void _showKeyPopup(KeyData keyData) {
    // This would show a popup with alternative characters
    // Implementation would depend on the UI framework
    emit(state.copyWith(
      activePopup: keyData.popup,
      popupAnchorKey: keyData,
    ));
  }

  void hideKeyPopup() {
    emit(state.copyWith(
      activePopup: null,
      popupAnchorKey: null,
    ));
  }

  void selectPopupKey(KeyData keyData) {
    handleKeyPress(keyData);
    hideKeyPopup();
  }

  void setSelectionMode(bool enabled) {
    emit(state.copyWith(isSelectionMode: enabled));
  }

  void updateKeyVariation(KeyVariation variation) {
    emit(state.copyWith(keyVariation: variation));
  }

  @override
  Future<void> close() {
    _inputEventDispatcher.dispose();
    return super.close();
  }
}

class KeyboardState extends Equatable {
  const KeyboardState({
    required this.currentMode,
    required this.currentLayout,
    required this.inputShiftState,
    required this.isSelectionMode,
    required this.keyVariation,
    this.activePopup,
    this.popupAnchorKey,
  });

  const KeyboardState.initial()
      : currentMode = KeyboardMode.characters,
        currentLayout = null,
        inputShiftState = InputShiftState.unshifted,
        isSelectionMode = false,
        keyVariation = KeyVariation.normal,
        activePopup = null,
        popupAnchorKey = null;

  final KeyboardMode currentMode;
  final KeyboardLayout? currentLayout;
  final InputShiftState inputShiftState;
  final bool isSelectionMode;
  final KeyVariation keyVariation;
  final PopupSet? activePopup;
  final KeyData? popupAnchorKey;

  KeyboardState copyWith({
    KeyboardMode? currentMode,
    KeyboardLayout? currentLayout,
    InputShiftState? inputShiftState,
    bool? isSelectionMode,
    KeyVariation? keyVariation,
    PopupSet? activePopup,
    KeyData? popupAnchorKey,
  }) {
    return KeyboardState(
      currentMode: currentMode ?? this.currentMode,
      currentLayout: currentLayout ?? this.currentLayout,
      inputShiftState: inputShiftState ?? this.inputShiftState,
      isSelectionMode: isSelectionMode ?? this.isSelectionMode,
      keyVariation: keyVariation ?? this.keyVariation,
      activePopup: activePopup,
      popupAnchorKey: popupAnchorKey,
    );
  }

  bool get isShifted =>
      inputShiftState == InputShiftState.shiftedManual ||
      inputShiftState == InputShiftState.shiftedAutomatic ||
      inputShiftState == InputShiftState.capsLock;

  bool get isCapsLock => inputShiftState == InputShiftState.capsLock;

  bool get hasPopup => activePopup != null;

  @override
  List<Object?> get props => [
        currentMode,
        currentLayout,
        inputShiftState,
        isSelectionMode,
        keyVariation,
        activePopup,
        popupAnchorKey,
      ];
}
