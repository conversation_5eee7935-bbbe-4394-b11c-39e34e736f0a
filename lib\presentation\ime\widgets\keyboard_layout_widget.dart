import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../data/models/keyboard_layout.dart';
import '../../../data/models/key_data.dart';
import '../../../core/managers/theme_manager.dart';
import '../../../core/constants/app_constants.dart';
import '../../../core/constants/key_codes.dart';
import 'key_widget.dart';

class KeyboardLayoutWidget extends StatelessWidget {
  const KeyboardLayoutWidget({
    super.key,
    required this.layout,
    required this.onKeyPressed,
    required this.onKeyLongPressed,
    required this.onKeySwiped,
  });

  final KeyboardLayout layout;
  final Function(KeyData) onKeyPressed;
  final Function(KeyData) onKeyLongPressed;
  final Function(KeyData, SwipeDirection) onKeySwiped;

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<ThemeManager, ThemeState>(
      builder: (context, themeState) {
        return Container(
          padding: const EdgeInsets.all(4),
          color: themeState.currentTheme.colorScheme.background,
          child: Column(
            children:
                layout.rows.map((row) => _buildRow(context, row)).toList(),
          ),
        );
      },
    );
  }

  Widget _buildRow(BuildContext context, KeyboardRow row) {
    return Expanded(
      flex: (row.height * 100).round(),
      child: Row(
        children:
            row.keys.map((keyData) => _buildKey(context, keyData)).toList(),
      ),
    );
  }

  Widget _buildKey(BuildContext context, KeyData keyData) {
    // Calculate key width based on key type and properties
    double keyWidth = _getKeyWidth(keyData);

    return Expanded(
      flex: (keyWidth * 100).round(),
      child: Padding(
        padding: const EdgeInsets.all(2),
        child: KeyWidget(
          keyData: keyData,
          onPressed: () => onKeyPressed(keyData),
          onLongPressed: () => onKeyLongPressed(keyData),
          onSwiped: (direction) => onKeySwiped(keyData, direction),
        ),
      ),
    );
  }

  double _getKeyWidth(KeyData keyData) {
    // Special width handling for certain keys
    switch (keyData.code) {
      case KeyCode.SPACE:
        return 4.0; // Space bar is wider
      case KeyCode.SHIFT:
      case KeyCode.DELETE:
        return 1.5; // Modifier keys are slightly wider
      case KeyCode.ENTER:
        return 1.5;
      default:
        return 1.0; // Normal key width
    }
  }
}
