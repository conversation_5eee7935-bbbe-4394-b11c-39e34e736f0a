import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';

import '../constants/app_constants.dart';
import '../constants/key_codes.dart';
import '../../data/models/key_data.dart';

class IMEService {
  static const MethodChannel _channel =
      MethodChannel(AppConstants.imeMethodChannel);

  static Future<void> commitText(String text) async {
    try {
      await _channel.invokeMethod('commitText', {'text': text});
    } catch (e) {
      debugPrint('Failed to commit text: $e');
    }
  }

  static Future<void> deleteSurroundingText({
    int beforeLength = 1,
    int afterLength = 0,
  }) async {
    try {
      await _channel.invokeMethod('deleteSurroundingText', {
        'beforeLength': beforeLength,
        'afterLength': afterLength,
      });
    } catch (e) {
      debugPrint('Failed to delete surrounding text: $e');
    }
  }

  static Future<void> sendKeyEvent(int keyCode) async {
    try {
      await _channel.invokeMethod('sendKeyEvent', {'keyCode': keyCode});
    } catch (e) {
      debugPrint('Failed to send key event: $e');
    }
  }

  static Future<Map<String, dynamic>?> getInputConnection() async {
    try {
      return await _channel.invokeMethod('getInputConnection');
    } catch (e) {
      debugPrint('Failed to get input connection: $e');
      return null;
    }
  }

  static Future<void> setComposingText(
      String text, int newCursorPosition) async {
    try {
      await _channel.invokeMethod('setComposingText', {
        'text': text,
        'newCursorPosition': newCursorPosition,
      });
    } catch (e) {
      print('Failed to set composing text: $e');
    }
  }

  static Future<void> finishComposingText() async {
    try {
      await _channel.invokeMethod('finishComposingText');
    } catch (e) {
      print('Failed to finish composing text: $e');
    }
  }

  static Future<void> performEditorAction(int actionCode) async {
    try {
      await _channel
          .invokeMethod('performEditorAction', {'actionCode': actionCode});
    } catch (e) {
      print('Failed to perform editor action: $e');
    }
  }

  static Future<void> clearComposingText() async {
    try {
      await _channel.invokeMethod('clearComposingText');
    } catch (e) {
      print('Failed to clear composing text: $e');
    }
  }

  static Future<String?> getTextBeforeCursor(int length) async {
    try {
      return await _channel
          .invokeMethod('getTextBeforeCursor', {'length': length});
    } catch (e) {
      print('Failed to get text before cursor: $e');
      return null;
    }
  }

  static Future<String?> getTextAfterCursor(int length) async {
    try {
      return await _channel
          .invokeMethod('getTextAfterCursor', {'length': length});
    } catch (e) {
      print('Failed to get text after cursor: $e');
      return null;
    }
  }

  static Future<void> moveCursor(int offset) async {
    try {
      await _channel.invokeMethod('moveCursor', {'offset': offset});
    } catch (e) {
      print('Failed to move cursor: $e');
    }
  }

  static Future<void> selectText(int start, int end) async {
    try {
      await _channel.invokeMethod('selectText', {
        'start': start,
        'end': end,
      });
    } catch (e) {
      print('Failed to select text: $e');
    }
  }

  static Future<void> hideKeyboard() async {
    try {
      await _channel.invokeMethod('hideKeyboard');
    } catch (e) {
      print('Failed to hide keyboard: $e');
    }
  }

  static Future<void> openSettings() async {
    try {
      await _channel.invokeMethod('openSettings');
    } catch (e) {
      print('Failed to open settings: $e');
    }
  }

  // High-level methods for common operations
  Future<void> onKeyDown(KeyData keyData) async {
    switch (keyData.type) {
      case KeyType.character:
        await _handleCharacterKey(keyData);
        break;
      case KeyType.function:
        await _handleFunctionKey(keyData);
        break;
      case KeyType.modifier:
        await _handleModifierKey(keyData);
        break;
      case KeyType.numeric:
        await _handleNumericKey(keyData);
        break;
      default:
        break;
    }
  }

  Future<void> onKeyUp(KeyData keyData) async {
    // Handle key up events if needed
  }

  Future<void> onKeyCancel(KeyData keyData) async {
    // Handle key cancellation
  }

  Future<void> onKeyLongPress(KeyData keyData) async {
    switch (keyData.code) {
      case KeyCode.SPACE:
        // Show cursor movement or language switcher
        break;
      case KeyCode.DELETE:
        // Start word deletion
        await deleteWord();
        break;
      default:
        // Show popup alternatives
        break;
    }
  }

  Future<void> onKeyRepeat(KeyData keyData) async {
    switch (keyData.code) {
      case KeyCode.DELETE:
        await deleteSurroundingText();
        break;
      case KeyCode.SPACE:
        await commitText(' ');
        break;
      default:
        if (keyData.label != null) {
          await commitText(keyData.label!);
        }
        break;
    }
  }

  Future<void> _handleCharacterKey(KeyData keyData) async {
    if (keyData.label != null) {
      await commitText(keyData.label!);
    }
  }

  Future<void> _handleNumericKey(KeyData keyData) async {
    if (keyData.label != null) {
      await commitText(keyData.label!);
    }
  }

  Future<void> _handleFunctionKey(KeyData keyData) async {
    switch (keyData.code) {
      case KeyCode.SPACE:
        await commitText(' ');
        break;
      case KeyCode.ENTER:
        await commitText('\n');
        break;
      case KeyCode.DELETE:
        await deleteSurroundingText();
        break;
      case KeyCode.TAB:
        await commitText('\t');
        break;
      case KeyCode.MODE_CHANGE:
        // Handle mode change - this is handled by KeyboardManager
        break;
      case KeyCode.EMOJI_SWITCH:
        // Handle emoji switch - this is handled by KeyboardManager
        break;
      case KeyCode.CLIPBOARD_SWITCH:
        // Handle clipboard switch - this is handled by KeyboardManager
        break;
      case KeyCode.SETTINGS:
        await openSettings();
        break;
      default:
        break;
    }
  }

  Future<void> _handleModifierKey(KeyData keyData) async {
    switch (keyData.code) {
      case KeyCode.SHIFT:
        // Toggle shift state - handled by KeyboardManager
        break;
      case KeyCode.CAPS_LOCK:
        // Toggle caps lock - handled by KeyboardManager
        break;
      case KeyCode.CTRL:
        // Handle Ctrl modifier
        break;
      case KeyCode.ALT:
        // Handle Alt modifier
        break;
      default:
        break;
    }
  }

  Future<void> deleteWord() async {
    // Get text before cursor and delete last word
    final textBefore = await getTextBeforeCursor(100);
    if (textBefore != null && textBefore.isNotEmpty) {
      final words = textBefore.split(RegExp(r'\s+'));
      if (words.isNotEmpty) {
        final lastWord = words.last;
        await deleteSurroundingText(beforeLength: lastWord.length);
      }
    }
  }

  Future<void> deleteToBeginningOfLine() async {
    final textBefore = await getTextBeforeCursor(1000);
    if (textBefore != null) {
      final lastNewlineIndex = textBefore.lastIndexOf('\n');
      final deleteLength = lastNewlineIndex == -1
          ? textBefore.length
          : textBefore.length - lastNewlineIndex - 1;
      await deleteSurroundingText(beforeLength: deleteLength);
    }
  }

  Future<void> selectCurrentWord() async {
    final textBefore = await getTextBeforeCursor(100);
    final textAfter = await getTextAfterCursor(100);

    if (textBefore != null && textAfter != null) {
      // Find word boundaries
      final beforeMatch = RegExp(r'\S*$').firstMatch(textBefore);
      final afterMatch = RegExp(r'^\S*').firstMatch(textAfter);

      final beforeLength = beforeMatch?.group(0)?.length ?? 0;
      final afterLength = afterMatch?.group(0)?.length ?? 0;

      if (beforeLength > 0 || afterLength > 0) {
        await selectText(-beforeLength, afterLength);
      }
    }
  }
}
