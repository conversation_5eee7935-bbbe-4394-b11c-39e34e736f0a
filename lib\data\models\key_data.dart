import 'package:equatable/equatable.dart';

import '../../core/constants/key_codes.dart';
import '../../core/constants/app_constants.dart';

class KeyData extends Equatable {
  const KeyData({
    required this.code,
    required this.type,
    this.label,
    this.hint,
    this.isEnabled = true,
    this.isPressed = false,
    this.popup,
    this.properties = const {},
  });

  final KeyCode code;
  final KeyType type;
  final String? label;
  final String? hint;
  final bool isEnabled;
  final bool isPressed;
  final PopupSet? popup;
  final Map<String, dynamic> properties;

  @override
  List<Object?> get props => [
        code,
        type,
        label,
        hint,
        isEnabled,
        isPressed,
        popup,
        properties,
      ];

  // Predefined key data constants
  static const KeyData UNSPECIFIED = KeyData(
    code: KeyCode.UNSPECIFIED,
    type: KeyType.system,
  );

  static const KeyData SPACE = KeyData(
    code: KeyCode.SPACE,
    type: KeyType.function,
    label: ' ',
  );

  static const KeyData ENTER = KeyData(
    code: KeyCode.ENTER,
    type: KeyType.function,
  );

  static const KeyData DELETE = KeyData(
    code: KeyCode.DELETE,
    type: KeyType.function,
  );

  static const KeyData SHIFT = KeyData(
    code: KeyCode.SHIFT,
    type: KeyType.modifier,
  );

  static const KeyData CAPS_LOCK = KeyData(
    code: KeyCode.CAPS_LOCK,
    type: KeyType.modifier,
  );

  static const KeyData MODE_CHANGE = KeyData(
    code: KeyCode.MODE_CHANGE,
    type: KeyType.function,
    label: 'ABC',
  );

  static const KeyData EMOJI_SWITCH = KeyData(
    code: KeyCode.EMOJI_SWITCH,
    type: KeyType.function,
    label: '😀',
  );

  static const KeyData CLIPBOARD_SWITCH = KeyData(
    code: KeyCode.CLIPBOARD_SWITCH,
    type: KeyType.function,
  );

  static const KeyData SETTINGS = KeyData(
    code: KeyCode.SETTINGS,
    type: KeyType.function,
  );

  // Character key factory
  static KeyData character(String char, {String? hint}) {
    final charCode = char.codeUnitAt(0);
    KeyCode keyCode;

    // Map character to KeyCode
    if (char.length == 1) {
      if (char.toUpperCase().codeUnitAt(0) >= 65 &&
          char.toUpperCase().codeUnitAt(0) <= 90) {
        // A-Z
        keyCode = KeyCode.values.firstWhere(
          (k) => k.value == char.toUpperCase().codeUnitAt(0),
          orElse: () => KeyCode.UNSPECIFIED,
        );
      } else if (charCode >= 48 && charCode <= 57) {
        // 0-9
        keyCode = KeyCode.values.firstWhere(
          (k) => k.value == charCode,
          orElse: () => KeyCode.UNSPECIFIED,
        );
      } else {
        // Other characters
        keyCode = KeyCode.values.firstWhere(
          (k) => k.value == charCode,
          orElse: () => KeyCode.UNSPECIFIED,
        );
      }
    } else {
      keyCode = KeyCode.UNSPECIFIED;
    }

    return KeyData(
      code: keyCode,
      type: KeyType.character,
      label: char,
      hint: hint,
    );
  }

  // Numeric key factory
  static KeyData numeric(String digit, {String? hint}) {
    final digitCode = digit.codeUnitAt(0);
    final keyCode = KeyCode.values.firstWhere(
      (k) => k.value == digitCode,
      orElse: () => KeyCode.UNSPECIFIED,
    );

    return KeyData(
      code: keyCode,
      type: KeyType.numeric,
      label: digit,
      hint: hint,
    );
  }
}

class PopupSet extends Equatable {
  const PopupSet({
    required this.main,
    this.relevant = const [],
    this.direction = PopupDirection.up,
  });

  final List<KeyData> main;
  final List<KeyData> relevant;
  final PopupDirection direction;

  @override
  List<Object?> get props => [main, relevant, direction];
}

enum PopupDirection {
  up,
  down,
  left,
  right,
}

// Key bounds for touch handling
class KeyBounds extends Equatable {
  const KeyBounds({
    required this.left,
    required this.top,
    required this.right,
    required this.bottom,
  });

  final double left;
  final double top;
  final double right;
  final double bottom;

  double get width => right - left;
  double get height => bottom - top;
  double get centerX => left + width / 2;
  double get centerY => top + height / 2;

  bool contains(double x, double y) {
    return x >= left && x <= right && y >= top && y <= bottom;
  }

  KeyBounds copyWith({
    double? left,
    double? top,
    double? right,
    double? bottom,
  }) {
    return KeyBounds(
      left: left ?? this.left,
      top: top ?? this.top,
      right: right ?? this.right,
      bottom: bottom ?? this.bottom,
    );
  }

  @override
  List<Object?> get props => [left, top, right, bottom];
}

// Key visual state
class KeyVisualState extends Equatable {
  const KeyVisualState({
    this.isPressed = false,
    this.isEnabled = true,
    this.isHighlighted = false,
    this.scale = 1.0,
    this.opacity = 1.0,
  });

  final bool isPressed;
  final bool isEnabled;
  final bool isHighlighted;
  final double scale;
  final double opacity;

  KeyVisualState copyWith({
    bool? isPressed,
    bool? isEnabled,
    bool? isHighlighted,
    double? scale,
    double? opacity,
  }) {
    return KeyVisualState(
      isPressed: isPressed ?? this.isPressed,
      isEnabled: isEnabled ?? this.isEnabled,
      isHighlighted: isHighlighted ?? this.isHighlighted,
      scale: scale ?? this.scale,
      opacity: opacity ?? this.opacity,
    );
  }

  @override
  List<Object?> get props =>
      [isPressed, isEnabled, isHighlighted, scale, opacity];
}
