#!/usr/bin/env python3
import os
import re

# Define the replacements for each enum
replacements = {
    # KeyboardMode
    'KeyboardMode.CHARACTERS': 'KeyboardMode.characters',
    'KeyboardMode.SYMBOLS': 'KeyboardMode.symbols',
    'KeyboardMode.SYMBOLS2': 'KeyboardMode.symbols2',
    'KeyboardMode.NUMERIC': 'KeyboardMode.numeric',
    'KeyboardMode.NUMERIC_ADVANCED': 'KeyboardMode.numericAdvanced',
    'KeyboardMode.PHONE': 'KeyboardMode.phone',
    'KeyboardMode.MEDIA': 'KeyboardMode.media',
    'KeyboardMode.CLIPBOARD': 'KeyboardMode.clipboard',
    
    # KeyType
    'KeyType.CHARACTER': 'KeyType.character',
    'KeyType.NUMERIC': 'KeyType.numeric',
    'KeyType.FUNCTION': 'KeyType.function',
    'KeyType.MODIFIER': 'KeyType.modifier',
    'KeyType.NAVIGATION': 'KeyType.navigation',
    'KeyType.SYSTEM': 'KeyType.system',
    
    # InputShiftState
    'InputShiftState.UNSHIFTED': 'InputShiftState.unshifted',
    'InputShiftState.SHIFTED_MANUAL': 'InputShiftState.shiftedManual',
    'InputShiftState.SHIFTED_AUTOMATIC': 'InputShiftState.shiftedAutomatic',
    'InputShiftState.CAPS_LOCK': 'InputShiftState.capsLock',
}

def fix_file(filepath):
    """Fix enum references in a single file"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Apply all replacements
        for old, new in replacements.items():
            content = content.replace(old, new)
        
        # Only write if content changed
        if content != original_content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"Fixed: {filepath}")
            return True
        return False
    except Exception as e:
        print(f"Error processing {filepath}: {e}")
        return False

def main():
    """Fix all Dart files in the lib directory"""
    lib_dir = 'lib'
    fixed_count = 0
    
    for root, dirs, files in os.walk(lib_dir):
        for file in files:
            if file.endswith('.dart'):
                filepath = os.path.join(root, file)
                if fix_file(filepath):
                    fixed_count += 1
    
    print(f"Fixed {fixed_count} files")

if __name__ == '__main__':
    main()
