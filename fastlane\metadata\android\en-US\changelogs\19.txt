- Add option to adjust font size multiplier (#48)
- Add Arabic keyboard (thanks @<PERSON><PERSON><PERSON><PERSON>)
- Add Arabic translation (thanks @<PERSON><PERSON><PERSON><PERSON>)
- Add Dutch translation (thanks @mondlicht-und-sterne)
- Add Finnish translation (thanks @teemue)
- Add French translation (thanks @HeiWiper)
- Add Greek translation (thanks @tsiflimagas)
- Complete Italian translation (thanks @l<PERSON>barda)
- Fix bottom offset not applying correctly (#58)
- Fix symbol hint not accounting for missing shift (#68)
- Fix keyboard UI not displaying correctly for rtl languages (#69)
- Major improvements in auto sizing (#48, #50, #61)
- Improve precise character delete swipe (#25)
