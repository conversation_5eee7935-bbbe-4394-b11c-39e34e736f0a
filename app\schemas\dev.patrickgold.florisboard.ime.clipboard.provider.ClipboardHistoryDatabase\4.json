{"formatVersion": 1, "database": {"version": 4, "identityHash": "1dd181d116dcb4530fb5b33451ea9ab5", "entities": [{"tableName": "clipboard_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`_id` INTEGER PRIMARY KEY AUTOINCREMENT NOT NULL, `type` INTEGER NOT NULL, `text` TEXT, `uri` TEXT, `creationTimestampMs` INTEGER NOT NULL, `isPinned` INTEGER NOT NULL, `mimeTypes` TEXT NOT NULL, `is_sensitive` INTEGER NOT NULL DEFAULT 0, `is_remote_device` INTEGER NOT NULL DEFAULT 0)", "fields": [{"fieldPath": "id", "columnName": "_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "type", "columnName": "type", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "text", "columnName": "text", "affinity": "TEXT", "notNull": false}, {"fieldPath": "uri", "columnName": "uri", "affinity": "TEXT", "notNull": false}, {"fieldPath": "creationTimestampMs", "columnName": "creationTimestampMs", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "isPinned", "columnName": "isPinned", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "mimeTypes", "columnName": "mimeTypes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "isSensitive", "columnName": "is_sensitive", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}, {"fieldPath": "isRemoteDevice", "columnName": "is_remote_device", "affinity": "INTEGER", "notNull": true, "defaultValue": "0"}], "primaryKey": {"autoGenerate": true, "columnNames": ["_id"]}, "indices": [{"name": "index_clipboard_history__id", "unique": false, "columnNames": ["_id"], "orders": [], "createSql": "CREATE INDEX IF NOT EXISTS `index_clipboard_history__id` ON `${TABLE_NAME}` (`_id`)"}], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '1dd181d116dcb4530fb5b33451ea9ab5')"]}}