{"$": "ime.extension.keyboard", "meta": {"id": "org.florisboard.localization", "version": "0.2.0", "title": "Default subtype presets / popup mappings", "description": "Default subtype presets and popup mappings which are always available.", "maintainers": ["patrickgold <<EMAIL>>"], "license": "apache-2.0"}, "dependencies": ["org.florisboard.composers", "org.florisboard.currencysets", "org.florisboard.layouts"], "punctuationRules": [{"id": "default", "label": "<PERSON><PERSON><PERSON>", "symbolsPrecedingAutoSpace": ".,?‽!\"&%)]}»", "symbolsFollowingAutoSpace": "", "symbolsPrecedingPhantomSpace": ".,;:?‽!&%)]}»©®™", "symbolsFollowingPhantomSpace": "¿⸘¡([{", "symbolsTerminatingSentence": ".?‽!"}], "popupMappings": [{"id": "default", "authors": ["pat<PERSON><PERSON>"]}, {"id": "ar", "authors": ["HeiWiper"]}, {"id": "ast", "authors": ["Softastur"]}, {"id": "bg", "authors": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "bn-BD", "authors": ["i<PERSON><PERSON><PERSON>"]}, {"id": "ca", "authors": ["mi<PERSON><PERSON>"]}, {"id": "cjk", "authors": ["moonbeamcelery"]}, {"id": "ckb", "authors": ["GoRaN"]}, {"id": "cs", "authors": ["ste<PERSON>-misik"]}, {"id": "da", "authors": ["pat<PERSON><PERSON>"]}, {"id": "de", "authors": ["pat<PERSON><PERSON>"]}, {"id": "de-DE-neobone", "authors": ["ostrya"]}, {"id": "el", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": "en", "authors": ["pat<PERSON><PERSON>"]}, {"id": "eo", "authors": ["jere<PERSON><PERSON>-miller"]}, {"id": "es", "authors": ["pat<PERSON><PERSON>"]}, {"id": "et", "authors": ["OneSheepy"]}, {"id": "fa", "authors": ["PHELAT"]}, {"id": "fa2", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, {"id": "fa3", "authors": ["SaeID-Rz"]}, {"id": "fi", "authors": ["pat<PERSON><PERSON>"]}, {"id": "fo", "authors": ["BinFlush"]}, {"id": "fr", "authors": ["pat<PERSON><PERSON>"]}, {"id": "hi-IN", "authors": ["npnpatidar"]}, {"id": "hr", "authors": ["hedidn<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "hu", "authors": ["zoli111, gabik65"]}, {"id": "hy", "authors": ["PJTSearch"]}, {"id": "id", "authors": ["Linerly"]}, {"id": "is", "authors": ["pat<PERSON><PERSON>"]}, {"id": "it", "authors": ["pat<PERSON><PERSON>"]}, {"id": "iw", "authors": ["<PERSON>"]}, {"id": "ja-<PERSON><PERSON><PERSON><PERSON>", "authors": ["waelwindows"]}, {"id": "kab", "authors": ["yanis867"]}, {"id": "ko", "authors": ["pat<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"id": "ku", "authors": ["GoRaN"]}, {"id": "lt", "authors": ["pat<PERSON><PERSON>"]}, {"id": "lv", "authors": ["pat<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}, {"id": "nb", "authors": ["pat<PERSON><PERSON>"]}, {"id": "nn", "authors": ["pat<PERSON><PERSON>"]}, {"id": "pl", "authors": ["<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "pt", "authors": ["pat<PERSON><PERSON>"]}, {"id": "pt-BR", "authors": ["rickym7"]}, {"id": "ro", "authors": ["bertin0"]}, {"id": "ru", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "33kk"]}, {"id": "rue", "authors": ["svvvst"]}, {"id": "sk", "authors": ["ste<PERSON>-misik", "majso"]}, {"id": "sr", "authors": ["hedidn<PERSON><PERSON><PERSON><PERSON>", "GrbavaCigla"]}, {"id": "sv", "authors": ["pat<PERSON><PERSON>"]}, {"id": "tr", "authors": ["kisekinopureya", "pat<PERSON><PERSON>", "dv<PERSON><PERSON>r"]}, {"id": "udm", "authors": ["vorgoron"]}, {"id": "uk", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "33kk", "ho<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "uk-cyr-ext", "authors": ["<PERSON><PERSON><PERSON><PERSON>", "33kk", "ho<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "ur-PK", "authors": ["<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, {"id": "vi-VN", "authors": ["pat<PERSON><PERSON>", "<PERSON><PERSON><PERSON>"]}], "subtypePresets": [{"languageTag": "en-US", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:en", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "en-UK", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:pound", "popupMapping": "org.florisboard.localization:en", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "en-CA", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:en", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "en-AU", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:en", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "de-DE", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:de", "preferred": {"characters": "org.florisboard.layouts:qwertz"}}, {"languageTag": "de-AT", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:de", "preferred": {"characters": "org.florisboard.layouts:qwertz"}}, {"languageTag": "de-CH", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:de", "preferred": {"characters": "org.florisboard.layouts:swiss_german"}}, {"languageTag": "de-DE-neobone", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:de-DE-neobone", "preferred": {"characters": "org.florisboard.layouts:neo2", "symbols": "org.florisboard.layouts:neo2", "numericRow": "org.florisboard.layouts:neo2"}}, {"languageTag": "fr-FR", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:fr", "preferred": {"characters": "org.florisboard.layouts:a<PERSON>ty"}}, {"languageTag": "fr-CA", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:fr", "preferred": {"characters": "org.florisboard.layouts:canadian_french"}}, {"languageTag": "fr-CH", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:fr", "preferred": {"characters": "org.florisboard.layouts:swiss_french"}}, {"languageTag": "it-IT", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:it", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "it-CH", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:it", "preferred": {"characters": "org.florisboard.layouts:swiss_italian"}}, {"languageTag": "es-ES", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:es", "preferred": {"characters": "org.florisboard.layouts:spanish"}}, {"languageTag": "es-US", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:es", "preferred": {"characters": "org.florisboard.layouts:spanish"}}, {"languageTag": "es-419", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:es", "preferred": {"characters": "org.florisboard.layouts:spanish"}}, {"languageTag": "pt-PT", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:pt", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "pt-BR", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:pt-BR", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "nb-NO", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:nb", "preferred": {"characters": "org.florisboard.layouts:norwegian"}}, {"languageTag": "nn-NO", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:nn", "preferred": {"characters": "org.florisboard.layouts:norwegian"}}, {"languageTag": "sv-SE", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:sv", "preferred": {"characters": "org.florisboard.layouts:swedish_finnish"}}, {"languageTag": "fi-FI", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:fi", "preferred": {"characters": "org.florisboard.layouts:swedish_finnish"}}, {"languageTag": "da-DK", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:da", "preferred": {"characters": "org.florisboard.layouts:danish"}}, {"languageTag": "id", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:id", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "is-IS", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:is", "preferred": {"characters": "org.florisboard.layouts:icelandic"}}, {"languageTag": "fo", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:fo", "preferred": {"characters": "org.florisboard.layouts:faroese"}}, {"languageTag": "fa-FA", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:rial", "popupMapping": "org.florisboard.localization:fa", "preferred": {"characters": "org.florisboard.layouts:persian", "symbols": "org.florisboard.layouts:persian", "symbols2": "org.florisboard.layouts:persian", "numericRow": "org.florisboard.layouts:persian"}}, {"languageTag": "fa-FA", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:rial", "popupMapping": "org.florisboard.localization:fa2", "preferred": {"characters": "org.florisboard.layouts:persian2", "symbols": "org.florisboard.layouts:persian", "symbols2": "org.florisboard.layouts:persian", "numericRow": "org.florisboard.layouts:persian"}}, {"languageTag": "fa-FA", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:rial", "popupMapping": "org.florisboard.localization:fa3", "preferred": {"characters": "org.florisboard.layouts:persian3", "symbols": "org.florisboard.layouts:persian", "symbols2": "org.florisboard.layouts:persian", "numericRow": "org.florisboard.layouts:western_arabic"}}, {"languageTag": "ar", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:rial", "popupMapping": "org.florisboard.localization:ar", "preferred": {"characters": "org.florisboard.layouts:arabic", "symbols": "org.florisboard.layouts:eastern", "symbols2": "org.florisboard.layouts:eastern", "numericRow": "org.florisboard.layouts:eastern_arabic"}}, {"languageTag": "az", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:azerbaijani_manat", "popupMapping": "org.florisboard.localization:tr", "preferred": {"characters": "org.florisboard.layouts:azerbaijani"}}, {"languageTag": "hu", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:hu", "preferred": {"characters": "org.florisboard.layouts:hungarian"}}, {"languageTag": "eo", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:eo", "preferred": {"characters": "org.florisboard.layouts:esperanto"}}, {"languageTag": "hr", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:hr", "preferred": {"characters": "org.florisboard.layouts:qwertz"}}, {"languageTag": "hy", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:armenian_dram", "popupMapping": "org.florisboard.localization:hy", "preferred": {"characters": "org.florisboard.layouts:western_armenian", "symbols": "org.florisboard.layouts:armenian"}}, {"languageTag": "hy", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:armenian_dram", "popupMapping": "org.florisboard.localization:hy", "preferred": {"characters": "org.florisboard.layouts:eastern_armenian", "symbols": "org.florisboard.layouts:armenian"}}, {"languageTag": "ru", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:russian_ruble", "popupMapping": "org.florisboard.localization:ru", "preferred": {"characters": "org.florisboard.layouts:jcuken_russian"}}, {"languageTag": "rue", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:rue", "preferred": {"characters": "org.florisboard.layouts:rusyn"}}, {"languageTag": "uk", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:ukrainian_hryvnia", "popupMapping": "org.florisboard.localization:uk", "preferred": {"characters": "org.florisboard.layouts:jcuken_ukrainian"}}, {"languageTag": "el", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:el", "preferred": {"characters": "org.florisboard.layouts:greek"}}, {"languageTag": "ro", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:ro", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "pl", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:pl", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "bg-bg", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:bg", "preferred": {"characters": "org.florisboard.layouts:bulgarian_phonetic"}}, {"languageTag": "tr", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:turkish_lira", "popupMapping": "org.florisboard.localization:tr", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "iw-IL", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:israeli_new_shekel", "popupMapping": "org.florisboard.localization:iw", "preferred": {"characters": "org.florisboard.layouts:hebrew"}}, {"languageTag": "ckb", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:ckb", "preferred": {"characters": "org.florisboard.layouts:kurdish", "symbols": "org.florisboard.layouts:eastern", "symbols2": "org.florisboard.layouts:eastern", "numericRow": "org.florisboard.layouts:eastern_arabic"}}, {"languageTag": "sr-RS", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:sr", "preferred": {"characters": "org.florisboard.layouts:serbian_cyrillic"}}, {"languageTag": "sl-SI", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:sl-SI", "preferred": {"characters": "org.florisboard.layouts:slovenian"}}, {"languageTag": "lv-LV", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:lv", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "ku", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "popupMapping": "org.florisboard.localization:ku", "preferred": {"characters": "org.florisboard.layouts:kurdish_kur<PERSON>ci"}}, {"languageTag": "ca", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:ca", "preferred": {"characters": "org.florisboard.layouts:catalan"}}, {"languageTag": "IPA-IPA", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:dollar", "preferred": {"characters": "org.florisboard.layouts:ipa", "symbols": "org.florisboard.layouts:ipa", "symbols2": "org.florisboard.layouts:ipa"}}, {"languageTag": "sk", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:sk", "preferred": {"characters": "org.florisboard.layouts:qwertz"}}, {"languageTag": "cs", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:cs", "preferred": {"characters": "org.florisboard.layouts:qwertz"}}, {"languageTag": "ko", "composer": "org.florisboard.composers:hangul-unicode", "currencySet": "org.florisboard.currencysets:south_korean_won", "popupMapping": "org.florisboard.localization:ko", "preferred": {"characters": "org.florisboard.layouts:korean"}}, {"languageTag": "lt-LT", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:lt", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "ja-<PERSON><PERSON><PERSON><PERSON>", "composer": "org.florisboard.composers:kana-unicode", "currencySet": "org.florisboard.currencysets:yen", "popupMapping": "org.florisboard.localization:ja-<PERSON><PERSON><PERSON><PERSON>", "preferred": {"characters": "org.florisboard.layouts:jis", "symbols": "org.florisboard.layouts:cjk", "symbols2": "org.florisboard.layouts:cjk", "numericRow": "org.florisboard.layouts:cjk"}}, {"languageTag": "th-kd", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:thai_baht", "preferred": {"characters": "org.florisboard.layouts:thai_kedmanee", "numericRow": "org.florisboard.layouts:thai"}}, {"languageTag": "vi-VN", "composer": "org.florisboard.composers:telex", "currencySet": "org.florisboard.currencysets:vietnamese_dong", "popupMapping": "org.florisboard.localization:vi-VN", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "ig-NG", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:nigerian_naira", "preferred": {"characters": "org.florisboard.layouts:igbo"}}, {"languageTag": "hoc", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:indian_rupee", "preferred": {"characters": "org.florisboard.layouts:warang_citi", "numericRow": "org.florisboard.layouts:warang_citi"}}, {"languageTag": "udm", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:russian_ruble", "popupMapping": "org.florisboard.localization:udm", "preferred": {"characters": "org.florisboard.layouts:udmurt_extended"}}, {"languageTag": "ur-PK", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:pakistani_rupee", "popupMapping": "org.florisboard.localization:ur-PK", "preferred": {"characters": "org.florisboard.layouts:urdu_phonetic"}}, {"languageTag": "zh-CN-zhengma", "composer": "org.florisboard.composers:appender", "nlpProviders": {"spelling": "org.florisboard.nlp.providers.han.shape", "suggestion": "org.florisboard.nlp.providers.han.shape"}, "currencySet": "org.florisboard.currencysets:yen", "popupMapping": "org.florisboard.localization:cjk", "preferred": {"characters": "org.florisboard.layouts:qwerty", "symbols": "org.florisboard.layouts:cjk", "symbols2": "org.florisboard.layouts:cjk"}}, {"languageTag": "bn-BD", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:bangladeshi_taka", "popupMapping": "org.florisboard.localization:bn-BD", "preferred": {"characters": "org.florisboard.layouts:bengali_bd", "numericRow": "org.florisboard.layouts:bengali", "numericAdvanced": "org.florisboard.layouts:bengali"}}, {"languageTag": "hi-IN", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:indian_rupee", "popupMapping": "org.florisboard.localization:hi-IN", "preferred": {"characters": "org.florisboard.layouts:hindi_in", "numericRow": "org.florisboard.layouts:<PERSON><PERSON><PERSON><PERSON>"}}, {"languageTag": "ast", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:ast", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}, {"languageTag": "ta-lk", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:indian_rupee", "preferred": {"characters": "org.florisboard.layouts:tamil"}}, {"languageTag": "kab", "composer": "org.florisboard.composers:appender", "currencySet": "org.florisboard.currencysets:euro", "popupMapping": "org.florisboard.localization:kab", "preferred": {"characters": "org.florisboard.layouts:qwerty"}}]}