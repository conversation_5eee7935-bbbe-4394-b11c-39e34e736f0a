import 'package:flutter/material.dart';

// Custom settings widgets
class SettingsSection extends StatelessWidget {
  const SettingsSection({
    super.key,
    required this.title,
    required this.children,
  });

  final String title;
  final List<Widget> children;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 24, 16, 8),
          child: Text(
            title,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        ...children,
        const Divider(height: 1),
      ],
    );
  }
}

class SettingsTile extends StatelessWidget {
  const SettingsTile({
    super.key,
    this.leading,
    required this.title,
    this.description,
    this.trailing,
    this.onPressed,
  });

  final Widget? leading;
  final Widget title;
  final Widget? description;
  final Widget? trailing;
  final VoidCallback? onPressed;

  factory SettingsTile.navigation({
    Widget? leading,
    required Widget title,
    Widget? description,
    required Function(BuildContext) onPressed,
  }) {
    return SettingsTile(
      leading: leading,
      title: title,
      description: description,
      trailing: const Icon(Icons.chevron_right),
      onPressed: () => onPressed,
    );
  }

  @override
  Widget build(BuildContext context) {
    return ListTile(
      leading: leading,
      title: title,
      subtitle: description,
      trailing: trailing,
      onTap: onPressed,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}

class SwitchSettingsTile extends StatelessWidget {
  const SwitchSettingsTile({
    super.key,
    required this.title,
    this.description,
    required this.value,
    required this.onChanged,
  });

  final String title;
  final String? description;
  final bool value;
  final ValueChanged<bool> onChanged;

  @override
  Widget build(BuildContext context) {
    return SwitchListTile(
      title: Text(title),
      subtitle: description != null ? Text(description!) : null,
      value: value,
      onChanged: onChanged,
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}

class ListSettingsTile<T> extends StatelessWidget {
  const ListSettingsTile({
    super.key,
    required this.title,
    this.description,
    required this.value,
    required this.values,
    required this.valueLabels,
    required this.onChanged,
  });

  final String title;
  final String? description;
  final T value;
  final List<T> values;
  final Map<T, String> valueLabels;
  final ValueChanged<T> onChanged;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (description != null) Text(description!),
          Text(
            valueLabels[value] ?? value.toString(),
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.primary,
            ),
          ),
        ],
      ),
      trailing: const Icon(Icons.chevron_right),
      onTap: () => _showSelectionDialog(context),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }

  void _showSelectionDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: values.map((option) => RadioListTile<T>(
            title: Text(valueLabels[option] ?? option.toString()),
            value: option,
            groupValue: value,
            onChanged: (newValue) {
              if (newValue != null) {
                onChanged(newValue);
                Navigator.pop(context);
              }
            },
          )).toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

class SliderSettingsTile extends StatelessWidget {
  const SliderSettingsTile({
    super.key,
    required this.title,
    this.description,
    required this.value,
    required this.min,
    required this.max,
    this.divisions,
    required this.onChanged,
  });

  final String title;
  final String? description;
  final double value;
  final double min;
  final double max;
  final int? divisions;
  final ValueChanged<double> onChanged;

  @override
  Widget build(BuildContext context) {
    return ListTile(
      title: Text(title),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (description != null) Text(description!),
          const SizedBox(height: 8),
          Row(
            children: [
              Text(min.toStringAsFixed(1)),
              Expanded(
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  divisions: divisions,
                  onChanged: onChanged,
                ),
              ),
              Text(max.toStringAsFixed(1)),
            ],
          ),
          Center(
            child: Text(
              value.toStringAsFixed(divisions != null ? 1 : 0),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
    );
  }
}
