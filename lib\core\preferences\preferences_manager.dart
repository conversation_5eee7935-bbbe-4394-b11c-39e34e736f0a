import 'package:shared_preferences/shared_preferences.dart';

import '../constants/app_constants.dart';

class PreferencesManager {
  static PreferencesManager? _instance;
  static PreferencesManager get instance => _instance ??= PreferencesManager._();
  
  PreferencesManager._();

  late final SharedPreferences _prefs;
  
  // Preference categories
  late final KeyboardPreferences keyboard;
  late final ThemePreferences theme;
  late final ClipboardPreferences clipboard;
  late final GesturePreferences gestures;
  late final InputFeedbackPreferences inputFeedback;
  late final EmojiPreferences emoji;
  late final SmartbarPreferences smartbar;
  late final OtherPreferences other;

  Future<void> initialize() async {
    _prefs = await SharedPreferences.getInstance();
    
    keyboard = KeyboardPreferences(_prefs);
    theme = ThemePreferences(_prefs);
    clipboard = ClipboardPreferences(_prefs);
    gestures = GesturePreferences(_prefs);
    inputFeedback = InputFeedbackPreferences(_prefs);
    emoji = EmojiPreferences(_prefs);
    smartbar = SmartbarPreferences(_prefs);
    other = OtherPreferences(_prefs);
  }
}

// Base preference class
abstract class PreferenceCategory {
  final SharedPreferences _prefs;
  
  PreferenceCategory(this._prefs);
  
  T _get<T>(String key, T defaultValue) {
    switch (T) {
      case bool:
        return (_prefs.getBool(key) ?? defaultValue) as T;
      case int:
        return (_prefs.getInt(key) ?? defaultValue) as T;
      case double:
        return (_prefs.getDouble(key) ?? defaultValue) as T;
      case String:
        return (_prefs.getString(key) ?? defaultValue) as T;
      default:
        throw UnsupportedError('Unsupported preference type: $T');
    }
  }
  
  Future<void> _set<T>(String key, T value) async {
    switch (T) {
      case bool:
        await _prefs.setBool(key, value as bool);
        break;
      case int:
        await _prefs.setInt(key, value as int);
        break;
      case double:
        await _prefs.setDouble(key, value as double);
        break;
      case String:
        await _prefs.setString(key, value as String);
        break;
      default:
        throw UnsupportedError('Unsupported preference type: $T');
    }
  }
}

class KeyboardPreferences extends PreferenceCategory {
  KeyboardPreferences(SharedPreferences prefs) : super(prefs);

  // Number row
  bool get numberRow => _get('keyboard.numberRow', false);
  set numberRow(bool value) => _set('keyboard.numberRow', value);

  // Hints
  bool get hintedNumberRowEnabled => _get('keyboard.hintedNumberRowEnabled', true);
  set hintedNumberRowEnabled(bool value) => _set('keyboard.hintedNumberRowEnabled', value);

  KeyHintMode get hintedNumberRowMode => KeyHintMode.values[
    _get('keyboard.hintedNumberRowMode', KeyHintMode.SMART_PRIORITY.index)
  ];
  set hintedNumberRowMode(KeyHintMode value) => 
      _set('keyboard.hintedNumberRowMode', value.index);

  bool get hintedSymbolsEnabled => _get('keyboard.hintedSymbolsEnabled', true);
  set hintedSymbolsEnabled(bool value) => _set('keyboard.hintedSymbolsEnabled', value);

  KeyHintMode get hintedSymbolsMode => KeyHintMode.values[
    _get('keyboard.hintedSymbolsMode', KeyHintMode.SMART_PRIORITY.index)
  ];
  set hintedSymbolsMode(KeyHintMode value) => 
      _set('keyboard.hintedSymbolsMode', value.index);

  // Utility key
  bool get utilityKeyEnabled => _get('keyboard.utilityKeyEnabled', true);
  set utilityKeyEnabled(bool value) => _set('keyboard.utilityKeyEnabled', value);

  UtilityKeyAction get utilityKeyAction => UtilityKeyAction.values[
    _get('keyboard.utilityKeyAction', UtilityKeyAction.DYNAMIC_SWITCH_LANGUAGE_EMOJIS.index)
  ];
  set utilityKeyAction(UtilityKeyAction value) => 
      _set('keyboard.utilityKeyAction', value.index);

  // Space bar
  SpaceBarMode get spaceBarMode => SpaceBarMode.values[
    _get('keyboard.spaceBarMode', SpaceBarMode.CURRENT_LANGUAGE.index)
  ];
  set spaceBarMode(SpaceBarMode value) => _set('keyboard.spaceBarMode', value.index);

  bool get spaceBarSwitchesToCharacters => _get('keyboard.spaceBarSwitchesToCharacters', true);
  set spaceBarSwitchesToCharacters(bool value) => 
      _set('keyboard.spaceBarSwitchesToCharacters', value);

  // Timing
  int get longPressDelay => _get('keyboard.longPressDelay', 300);
  set longPressDelay(int value) => _set('keyboard.longPressDelay', value);

  // Font
  double get fontSizeMultiplier => _get('keyboard.fontSizeMultiplier', 1.0);
  set fontSizeMultiplier(double value) => _set('keyboard.fontSizeMultiplier', value);

  // Popups
  bool get popupEnabled => _get('keyboard.popupEnabled', true);
  set popupEnabled(bool value) => _set('keyboard.popupEnabled', value);

  bool get mergeHintPopupsEnabled => _get('keyboard.mergeHintPopupsEnabled', false);
  set mergeHintPopupsEnabled(bool value) => _set('keyboard.mergeHintPopupsEnabled', value);

  // Incognito
  IncognitoDisplayMode get incognitoDisplayMode => IncognitoDisplayMode.values[
    _get('keyboard.incognitoDisplayMode', IncognitoDisplayMode.DISPLAY_BEHIND_KEYBOARD.index)
  ];
  set incognitoDisplayMode(IncognitoDisplayMode value) => 
      _set('keyboard.incognitoDisplayMode', value.index);
}

class ThemePreferences extends PreferenceCategory {
  ThemePreferences(SharedPreferences prefs) : super(prefs);

  ThemeModeEnum get mode => ThemeModeEnum.values[
    _get('theme.mode', ThemeModeEnum.FOLLOW_SYSTEM.index)
  ];
  set mode(ThemeModeEnum value) => _set('theme.mode', value.index);

  String get dayThemeId => _get('theme.dayThemeId', 'kirat_day');
  set dayThemeId(String value) => _set('theme.dayThemeId', value);

  String get nightThemeId => _get('theme.nightThemeId', 'kirat_night');
  set nightThemeId(String value) => _set('theme.nightThemeId', value);

  String get accentColor => _get('theme.accentColor', '#4CAF50');
  set accentColor(String value) => _set('theme.accentColor', value);
}

class ClipboardPreferences extends PreferenceCategory {
  ClipboardPreferences(SharedPreferences prefs) : super(prefs);

  bool get historyEnabled => _get('clipboard.historyEnabled', false);
  set historyEnabled(bool value) => _set('clipboard.historyEnabled', value);

  bool get syncToFloris => _get('clipboard.syncToFloris', true);
  set syncToFloris(bool value) => _set('clipboard.syncToFloris', value);

  bool get syncToSystem => _get('clipboard.syncToSystem', false);
  set syncToSystem(bool value) => _set('clipboard.syncToSystem', value);

  int get maxHistorySize => _get('clipboard.maxHistorySize', 50);
  set maxHistorySize(int value) => _set('clipboard.maxHistorySize', value);
}

class GesturePreferences extends PreferenceCategory {
  GesturePreferences(SharedPreferences prefs) : super(prefs);

  // Swipe gestures
  SwipeAction get swipeUp => SwipeAction.values[
    _get('gestures.swipeUp', SwipeAction.SHIFT.index)
  ];
  set swipeUp(SwipeAction value) => _set('gestures.swipeUp', value.index);

  SwipeAction get swipeDown => SwipeAction.values[
    _get('gestures.swipeDown', SwipeAction.HIDE_KEYBOARD.index)
  ];
  set swipeDown(SwipeAction value) => _set('gestures.swipeDown', value.index);

  SwipeAction get swipeLeft => SwipeAction.values[
    _get('gestures.swipeLeft', SwipeAction.DELETE_WORD.index)
  ];
  set swipeLeft(SwipeAction value) => _set('gestures.swipeLeft', value.index);

  SwipeAction get swipeRight => SwipeAction.values[
    _get('gestures.swipeRight', SwipeAction.SPACE.index)
  ];
  set swipeRight(SwipeAction value) => _set('gestures.swipeRight', value.index);

  // Thresholds
  double get swipeDistanceThreshold => _get('gestures.swipeDistanceThreshold', 50.0);
  set swipeDistanceThreshold(double value) => _set('gestures.swipeDistanceThreshold', value);

  double get swipeVelocityThreshold => _get('gestures.swipeVelocityThreshold', 500.0);
  set swipeVelocityThreshold(double value) => _set('gestures.swipeVelocityThreshold', value);
}

class InputFeedbackPreferences extends PreferenceCategory {
  InputFeedbackPreferences(SharedPreferences prefs) : super(prefs);

  // Audio feedback
  bool get audioEnabled => _get('inputFeedback.audioEnabled', false);
  set audioEnabled(bool value) => _set('inputFeedback.audioEnabled', value);

  double get audioVolume => _get('inputFeedback.audioVolume', 0.5);
  set audioVolume(double value) => _set('inputFeedback.audioVolume', value);

  // Haptic feedback
  bool get hapticEnabled => _get('inputFeedback.hapticEnabled', true);
  set hapticEnabled(bool value) => _set('inputFeedback.hapticEnabled', value);

  double get hapticStrength => _get('inputFeedback.hapticStrength', 1.0);
  set hapticStrength(double value) => _set('inputFeedback.hapticStrength', value);
}

class EmojiPreferences extends PreferenceCategory {
  EmojiPreferences(SharedPreferences prefs) : super(prefs);

  bool get historyEnabled => _get('emoji.historyEnabled', true);
  set historyEnabled(bool value) => _set('emoji.historyEnabled', value);

  int get maxHistorySize => _get('emoji.maxHistorySize', 24);
  set maxHistorySize(int value) => _set('emoji.maxHistorySize', value);
}

class SmartbarPreferences extends PreferenceCategory {
  SmartbarPreferences(SharedPreferences prefs) : super(prefs);

  bool get enabled => _get('smartbar.enabled', true);
  set enabled(bool value) => _set('smartbar.enabled', value);

  bool get suggestionsEnabled => _get('smartbar.suggestionsEnabled', false);
  set suggestionsEnabled(bool value) => _set('smartbar.suggestionsEnabled', value);
}

class OtherPreferences extends PreferenceCategory {
  OtherPreferences(SharedPreferences prefs) : super(prefs);

  String get appLanguage => _get('other.appLanguage', 'auto');
  set appLanguage(String value) => _set('other.appLanguage', value);

  bool get showAppIcon => _get('other.showAppIcon', true);
  set showAppIcon(bool value) => _set('other.showAppIcon', value);
}
