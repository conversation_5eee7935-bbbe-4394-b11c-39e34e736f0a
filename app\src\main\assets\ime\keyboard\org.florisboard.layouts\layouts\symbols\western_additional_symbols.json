[[{"code": 37, "label": "%", "popup": {"main": {"code": 8240, "label": "‰"}, "relevant": [{"code": 8453, "label": "℅"}]}}, {"code": 92, "label": "\\"}, {"code": 124, "label": "|"}, {"code": 61, "label": "="}, {"code": 91, "label": "["}, {"code": 93, "label": "]"}, {"code": 60, "label": "<"}, {"code": 62, "label": ">"}, {"code": 123, "label": "{"}, {"code": 125, "label": "}"}], [{"code": 64, "label": "@"}, {"code": 35, "label": "#", "popup": {"main": {"code": 8470, "label": "№"}}}, {"code": -801, "label": "currency_slot_1", "popup": {"main": {"code": -802, "label": "currency_slot_2"}, "relevant": [{"code": -806, "label": "currency_slot_6"}, {"code": -803, "label": "currency_slot_3"}, {"code": -804, "label": "currency_slot_4"}, {"code": -805, "label": "currency_slot_5"}]}}, {"code": 95, "label": "_"}, {"code": 38, "label": "&"}, {"code": 45, "label": "-", "popup": {"main": {"code": 95, "label": "_"}, "relevant": [{"code": 8212, "label": "—"}, {"code": 8211, "label": "–"}, {"code": 183, "label": "·"}]}}, {"code": 43, "label": "+", "popup": {"main": {"code": 177, "label": "±"}}}, {"$": "layout_direction_selector", "ltr": {"code": 40, "label": "(", "popup": {"main": {"code": 60, "label": "<"}, "relevant": [{"code": 91, "label": "["}, {"code": 123, "label": "{"}]}}, "rtl": {"code": 41, "label": "(", "popup": {"main": {"code": 62, "label": "<"}, "relevant": [{"code": 93, "label": "["}, {"code": 125, "label": "{"}]}}}, {"$": "layout_direction_selector", "ltr": {"code": 41, "label": ")", "popup": {"main": {"code": 62, "label": ">"}, "relevant": [{"code": 93, "label": "]"}, {"code": 125, "label": "}"}]}}, "rtl": {"code": 40, "label": ")", "popup": {"main": {"code": 60, "label": ">"}, "relevant": [{"code": 91, "label": "]"}, {"code": 123, "label": "}"}]}}}, {"code": 47, "label": "/"}], [{"code": 42, "label": "*", "popup": {"main": {"code": 8224, "label": "†"}, "relevant": [{"code": 9733, "label": "★"}, {"code": 8225, "label": "‡"}]}}, {"code": 34, "label": "\"", "popup": {"main": {"code": 8221, "label": "”"}, "relevant": [{"code": 8222, "label": "„"}, {"code": 8220, "label": "“"}, {"code": 171, "label": "«"}, {"code": 187, "label": "»"}]}}, {"code": 39, "label": "'", "popup": {"main": {"code": 8217, "label": "’"}, "relevant": [{"code": 8218, "label": "‚"}, {"code": 8216, "label": "‘"}, {"code": 8249, "label": "‹"}, {"code": 8250, "label": "›"}]}}, {"code": 58, "label": ":", "popup": {"main": {"code": 8942, "label": "⋮"}}}, {"code": 59, "label": ";"}, {"code": 33, "label": "!", "popup": {"main": {"code": 161, "label": "¡"}}}, {"code": 63, "label": "?", "popup": {"main": {"code": 191, "label": "¿"}, "relevant": [{"code": 8253, "label": "‽"}]}}]]