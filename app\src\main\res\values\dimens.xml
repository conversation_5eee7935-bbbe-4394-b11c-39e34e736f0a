<?xml version="1.0" encoding="utf-8"?>
<resources>
    <dimen name="inputView_baseHeight">248dp</dimen>
    <dimen name="smartbar_baseHeight">40dp</dimen>
    <dimen name="textKeyboardView_baseHeight">208dp</dimen>
    <dimen name="mediaKeyboardView_baseHeight">@dimen/inputView_baseHeight</dimen>

    <fraction name="inputView_minHeightFraction">38.2%p</fraction>
    <fraction name="inputView_maxHeightFraction">46%p</fraction>

    <dimen name="key_width">33dp</dimen>
    <dimen name="key_height">42dp</dimen>
    <dimen name="emoji_key_width">@dimen/key_height</dimen>
    <dimen name="emoji_key_height">@dimen/key_height</dimen>

    <dimen name="key_marginH">2dp</dimen>
    <dimen name="key_marginV">5dp</dimen>
    <dimen name="keyboard_row_marginH">@dimen/key_marginH</dimen>

    <dimen name="keyboard_preview_margin">16dp</dimen>

    <dimen name="key_borderRadius">6dp</dimen>

    <dimen name="key_textSize">18sp</dimen>
    <dimen name="key_textHintSize">10sp</dimen>
    <dimen name="key_numeric_textSize">12sp</dimen>
    <dimen name="key_popup_textSize">21sp</dimen>
    <dimen name="key_space_textSize">12sp</dimen>
    <dimen name="emoji_key_textSize">22sp</dimen>
    <dimen name="devtools_memory_overlay_textSize">8sp</dimen>

    <dimen name="landscapeInputUi_padding">8dp</dimen>
    <dimen name="landscapeInputUi_actionButton_cornerRadius">6dp</dimen>
    <dimen name="landscapeInputUi_editText_borderWidth">1dp</dimen>
    <dimen name="landscapeInputUi_editText_cornerRadius">6dp</dimen>
    <dimen name="landscapeInputUi_editText_padding">8dp</dimen>

    <dimen name="media_bottom_button_width">60dp</dimen>
    <dimen name="media_bottom_button_height">@dimen/key_height</dimen>
    <dimen name="media_tab_indicator_height">4dp</dimen>
    <dimen name="media_tab_paddingH">0dp</dimen>

    <dimen name="clipboard_button_width">60dp</dimen>
    <dimen name="clipboard_button_height">@dimen/key_height</dimen>

    <dimen name="one_handed_width">50dp</dimen>
    <dimen name="one_handed_button_width">@dimen/one_handed_width</dimen>
    <dimen name="one_handed_button_height">@dimen/one_handed_width</dimen>

    <dimen name="smartbar_height">40dp</dimen>
    <dimen name="smartbar_radius">20dp</dimen>
    <dimen name="smartbar_button_margin">4dp</dimen>
    <dimen name="smartbar_button_padding">6dp</dimen>
    <dimen name="smartbar_candidate_textSize">14sp</dimen>
    <dimen name="smartbar_candidate_marginH">16dp</dimen>
    <dimen name="smartbar_divider_width">1dp</dimen>

    <dimen name="fab_margin">16dp</dimen>

    <dimen name="suggestion_chip_bg_padding_start">13dp</dimen>
    <dimen name="suggestion_chip_bg_padding_top">0dp</dimen>
    <dimen name="suggestion_chip_bg_padding_end">13dp</dimen>
    <dimen name="suggestion_chip_bg_padding_bottom">0dp</dimen>

    <dimen name="suggestion_chip_fg_title_margin_start">4dp</dimen>
    <dimen name="suggestion_chip_fg_title_margin_top">0dp</dimen>
    <dimen name="suggestion_chip_fg_title_margin_end">4dp</dimen>
    <dimen name="suggestion_chip_fg_title_margin_bottom">0dp</dimen>

    <dimen name="suggestion_chip_fg_subtitle_margin_start">4dp</dimen>
    <dimen name="suggestion_chip_fg_subtitle_margin_top">0dp</dimen>
    <dimen name="suggestion_chip_fg_subtitle_margin_end">4dp</dimen>
    <dimen name="suggestion_chip_fg_subtitle_margin_bottom">0dp</dimen>

    <dimen name="clipboard_text_item_pin_margin" >25dp</dimen>

    <dimen name="gesture_distance_threshold_very_short">24dp</dimen>
    <dimen name="gesture_distance_threshold_short">28dp</dimen>
    <dimen name="gesture_distance_threshold_normal">32dp</dimen>
    <dimen name="gesture_distance_threshold_long">36dp</dimen>
    <dimen name="gesture_distance_threshold_very_long">40dp</dimen>

    <integer name="gesture_velocity_threshold_very_slow">1000</integer>
    <integer name="gesture_velocity_threshold_slow">1450</integer>
    <integer name="gesture_velocity_threshold_normal">1900</integer>
    <integer name="gesture_velocity_threshold_fast">2350</integer>
    <integer name="gesture_velocity_threshold_very_fast">2800</integer>
</resources>
