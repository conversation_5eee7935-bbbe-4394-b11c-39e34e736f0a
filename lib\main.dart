import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/hive_flutter.dart';

import 'core/managers/keyboard_manager.dart';
import 'core/managers/theme_manager.dart';
import 'core/managers/extension_manager.dart';
import 'core/managers/clipboard_manager.dart';
import 'core/managers/emoji_manager.dart';
import 'core/managers/smartbar_manager.dart';
import 'core/preferences/preferences_manager.dart';
import 'core/memory/memory_manager.dart';
import 'core/constants/app_constants.dart';
import 'presentation/app/screens/main_app.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Hive
  await Hive.initFlutter();

  // Initialize preferences
  await PreferencesManager.instance.initialize();

  // Initialize memory manager
  MemoryManager.instance.initialize();

  runApp(const KiratKeyboardApp());
}

class KiratKeyboardApp extends StatelessWidget {
  const KiratKeyboardApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider<KeyboardManager>(
          create: (context) => KeyboardManager(),
        ),
        BlocProvider<ThemeManager>(
          create: (context) => ThemeManager()..initialize(),
        ),
        BlocProvider<ExtensionManager>(
          create: (context) => ExtensionManager()..initialize(),
        ),
        BlocProvider<ClipboardManager>(
          create: (context) => ClipboardManager()..initialize(),
        ),
        BlocProvider<EmojiManager>(
          create: (context) => EmojiManager()..initialize(),
        ),
        BlocProvider<SmartbarManager>(
          create: (context) => SmartbarManager()..initialize(),
        ),
      ],
      child: BlocBuilder<ThemeManager, ThemeState>(
        builder: (context, themeState) {
          return MaterialApp(
            title: 'Kirat Keyboard',
            theme: _buildThemeData(themeState, false),
            darkTheme: _buildThemeData(themeState, true),
            themeMode: _getThemeMode(),
            home: const MainApp(),
            debugShowCheckedModeBanner: false,
          );
        },
      ),
    );
  }

  ThemeData _buildThemeData(ThemeState themeState, bool isDark) {
    final colorScheme = isDark
        ? ColorScheme.fromSeed(
            seedColor: Colors.green,
            brightness: Brightness.dark,
          )
        : ColorScheme.fromSeed(
            seedColor: Colors.green,
            brightness: Brightness.light,
          );

    return ThemeData(
      colorScheme: colorScheme,
      useMaterial3: true,
    );
  }

  ThemeMode _getThemeMode() {
    final themeMode = PreferencesManager.instance.theme.mode;
    switch (themeMode) {
      case ThemeModeEnum.ALWAYS_DAY:
        return ThemeMode.light;
      case ThemeModeEnum.ALWAYS_NIGHT:
        return ThemeMode.dark;
      case ThemeModeEnum.FOLLOW_SYSTEM:
        return ThemeMode.system;
      default:
        return ThemeMode.system;
    }
  }
}
